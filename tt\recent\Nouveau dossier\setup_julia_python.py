"""
CONFIGURATION ARCHITECTURE HYBRIDE JULIA-PYTHON
===============================================

Configuration pour utiliser Julia avec indexation one-based
et éliminer tous les indices 0 dans les calculs.

Auteur: Système Hybride
Date: 2025-01-08
"""

import os
import sys
from pathlib import Path

def setup_julia_environment():
    """
    🔧 SETUP - Configuration environnement Julia-Python
    
    Configure l'environnement pour utiliser Julia avec indexation one-based
    et éliminer tous les tableaux avec indices 0.
    """
    print("🚀 Configuration Architecture Hybride Julia-Python")
    print("=" * 60)
    
    # Installation des dépendances Julia
    try:
        import julia
        print("✅ PyJulia déjà installé")
    except ImportError:
        print("📦 Installation de PyJulia...")
        os.system("pip install julia")
        import julia
    
    # Installation de Julia si nécessaire
    try:
        julia.install()
        print("✅ Julia configuré pour Python")
    except:
        print("⚠️  Julia déjà configuré")
    
    # Import Julia
    from julia import Main
    
    # Configuration Julia pour one-based indexing
    Main.eval("""
    println("🎯 Julia configuré avec indexation ONE-BASED")
    println("📊 Tous les tableaux commencent à l'indice 1")
    println("🚫 Aucun indice 0 dans les calculs")
    """)
    
    return Main

def verify_one_based_indexing():
    """
    ✅ VÉRIFICATION - Test indexation one-based
    
    Vérifie que Julia utilise bien l'indexation one-based
    et que tous les tableaux commencent à 1.
    """
    from julia import Main
    
    print("\n🔍 VÉRIFICATION INDEXATION ONE-BASED")
    print("-" * 40)
    
    # Test basique d'indexation
    Main.eval("""
    # Création d'un tableau test
    test_array = [10, 20, 30, 40, 50]
    
    println("📊 Tableau test: ", test_array)
    println("🎯 Premier élément (indice 1): ", test_array[1])
    println("🎯 Dernier élément (indice end): ", test_array[end])
    println("📏 Longueur du tableau: ", length(test_array))
    
    # Vérification que l'indice 0 n'existe pas
    try
        println("❌ Test indice 0: ", test_array[0])
    catch BoundsError
        println("✅ Indice 0 correctement rejeté (BoundsError)")
    end
    """)
    
    return True

def create_julia_modules():
    """
    📁 CRÉATION - Modules Julia spécialisés
    
    Crée les modules Julia pour les calculs avec indexation one-based.
    """
    print("\n📁 CRÉATION DES MODULES JULIA")
    print("-" * 40)
    
    # Module pour les calculs entropiques
    entropy_module = '''
module EntropyCalculationsOneBase

export calculate_entropy_one_based, process_sequence_one_based

"""
🧮 CALCULS ENTROPIQUES ONE-BASED

Tous les calculs utilisent l'indexation one-based.
Aucun indice 0 dans les tableaux.
"""

function calculate_entropy_one_based(sequence::Vector)
    """
    📊 Calcul entropie avec indexation one-based
    
    Args:
        sequence: Séquence INDEX5 (indices 1 à length(sequence))
    
    Returns:
        Entropie calculée avec indices one-based
    """
    n = length(sequence)
    println("📏 Longueur séquence: $n (indices 1 à $n)")
    
    # Comptage des fréquences (one-based)
    freq_dict = Dict{String, Int}()
    for i in 1:n  # Commence à 1, pas 0
        val = sequence[i]
        freq_dict[val] = get(freq_dict, val, 0) + 1
    end
    
    # Calcul probabilités (one-based)
    probs = Float64[]
    for (val, count) in freq_dict
        push!(probs, count / n)
    end
    
    # Calcul entropie de Shannon
    entropy = 0.0
    for i in 1:length(probs)  # One-based
        p = probs[i]
        if p > 0
            entropy -= p * log2(p)
        end
    end
    
    println("🎯 Entropie calculée: $entropy bits")
    return entropy
end

function process_sequence_one_based(sequence::Vector, position::Int)
    """
    🔄 Traitement séquence avec position one-based
    
    Args:
        sequence: Séquence complète
        position: Position dans la séquence (1-based)
    
    Returns:
        Sous-séquence traitée avec indices one-based
    """
    if position < 1 || position > length(sequence)
        error("❌ Position invalide: $position (doit être entre 1 et $(length(sequence)))")
    end
    
    # Extraction sous-séquence (1 à position)
    sub_sequence = sequence[1:position]
    
    println("📊 Sous-séquence [1:$position]: $sub_sequence")
    return sub_sequence
end

end  # module
'''
    
    # Sauvegarde du module
    with open("entropy_one_based.jl", "w", encoding="utf-8") as f:
        f.write(entropy_module)
    
    print("✅ Module entropy_one_based.jl créé")
    
    return "entropy_one_based.jl"

if __name__ == "__main__":
    # Configuration complète
    julia_main = setup_julia_environment()
    verify_one_based_indexing()
    module_path = create_julia_modules()
    
    print(f"\n🎉 CONFIGURATION TERMINÉE")
    print(f"📁 Module Julia créé: {module_path}")
    print(f"🎯 Prêt pour calculs one-based!")
