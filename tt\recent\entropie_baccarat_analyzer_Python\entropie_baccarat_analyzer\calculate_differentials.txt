# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2303 à 2345
# Type: Méthode de la classe INDEX5DifferentialAnalyzer

    def calculate_differentials(self, entropy_evolution):
        """
        Calcule les différentiels pour toutes les métriques.

        Args:
            entropy_evolution: Liste des résultats d'évolution entropique

        Returns:
            Liste des différentiels pour chaque main
        """
        if not entropy_evolution or len(entropy_evolution) < 2:
            return []

        differentials = []

        # Première main : différentiels = 0 (pas de main précédente)
        differentials.append({
            'position': 1,
            'diff_conditional': 0.0,
            'diff_entropy_rate': 0.0,
            'diff_simple_entropy': 0.0,
            'diff_simple_entropy_theoretical': 0.0
        })

        # Calculer les différentiels pour les mains suivantes
        for i in range(1, len(entropy_evolution)):
            current = entropy_evolution[i]
            previous = entropy_evolution[i-1]

            diff_conditional = abs(current.get('conditional_entropy', 0) - previous.get('conditional_entropy', 0))
            diff_entropy_rate = abs(current.get('entropy_rate', 0) - previous.get('entropy_rate', 0))
            diff_simple_entropy = abs(current.get('simple_entropy', 0) - previous.get('simple_entropy', 0))
            diff_simple_entropy_theoretical = abs(current.get('simple_entropy_theoretical', 0) - previous.get('simple_entropy_theoretical', 0))

            differentials.append({
                'position': current.get('position', i+1),
                'diff_conditional': diff_conditional,
                'diff_entropy_rate': diff_entropy_rate,
                'diff_simple_entropy': diff_simple_entropy,
                'diff_simple_entropy_theoretical': diff_simple_entropy_theoretical
            })

        return differentials