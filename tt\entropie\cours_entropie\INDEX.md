# 📇 INDEX GÉNÉRAL DU COURS D'ENTROPIE
## Navigation Complète et Références Croisées

### 🎯 Guide d'Utilisation de l'Index
- **Concepts** : Définitions et explications principales
- **Formules** : Expressions mathématiques avec références
- **Applications** : Domaines d'usage et exemples
- **Exercices** : Localisation des problèmes pratiques

---

## 🔤 INDEX ALPHABÉTIQUE

### A

**AEP (Asymptotic Equipartition Property)**
- Définition : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)
- Théorème SMB : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)
- Applications : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)

**Additivité de l'Entropie**
- Propriété : [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- Démonstration : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Systèmes indépendants : [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)

**Algorithme de Huffman**
- Description : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Implémentation : [Ressources/implementations_python.py](ressources/implementations_python.py)
- Exercices : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)

**Applications Pratiques**
- Compression : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Machine Learning : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Cryptographie : [Débutant/exercices_debutant.md](niveau_debutant/exercices_debutant.md)
- Bioinformatique : [Débutant/exercices_debutant.md](niveau_debutant/exercices_debutant.md)

### B

**Bernoulli (Distribution de)**
- Entropie : [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- Formule : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Implémentation : [Ressources/implementations_python.py](ressources/implementations_python.py)

**Bit (Unité d'Information)**
- Définition : [Débutant/01_introduction.md](niveau_debutant/01_introduction.md)
- Calcul : [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- Conversions : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)

### C

**Canal de Communication**
- Théorie : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Capacité : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Exercices : [Débutant/exercices_debutant.md](niveau_debutant/exercices_debutant.md)

**Codage de Source**
- Théorème : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Algorithmes : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Implémentations : [Ressources/implementations_python.py](ressources/implementations_python.py)

**Complexité de Kolmogorov**
- Définition : [Ressources/glossaire.md](ressources/glossaire.md)
- Liens avec entropie : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)
- Bibliographie : [Ressources/bibliographie.md](ressources/bibliographie.md)

**Compression de Données**
- Principes : [Débutant/01_introduction.md](niveau_debutant/01_introduction.md)
- Algorithmes : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Applications : [Débutant/exercices_debutant.md](niveau_debutant/exercices_debutant.md)

### D

**Divergence de Kullback-Leibler**
- Définition : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Propriétés : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Calculs : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Implémentation : [Ressources/implementations_python.py](ressources/implementations_python.py)

**Distribution Empirique**
- Définition : [Ressources/glossaire.md](ressources/glossaire.md)
- Estimation : [Ressources/implementations_python.py](ressources/implementations_python.py)
- Correction de biais : [Ressources/implementations_python.py](ressources/implementations_python.py)

### E

**Entropie Conditionnelle**
- Définition : [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)
- Formule : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Calculs : [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)
- Exercices : [Débutant/exercices_debutant.md](niveau_debutant/exercices_debutant.md)

**Entropie Croisée**
- Définition : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Applications ML : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Implémentation : [Ressources/implementations_python.py](ressources/implementations_python.py)

**Entropie Jointe**
- Définition : [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)
- Règle de chaîne : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Calculs : [Ressources/implementations_python.py](ressources/implementations_python.py)

**Entropie Métrique (Kolmogorov-Sinai)**
- Définition : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)
- Calculs : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)
- Applications : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)

**Entropie de Rényi**
- Définition : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Cas particuliers : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Implémentation : [Ressources/implementations_python.py](ressources/implementations_python.py)

**Entropie de Shannon**
- Introduction : [Débutant/01_introduction.md](niveau_debutant/01_introduction.md)
- Formule : [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- Propriétés : [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- Implémentation : [Ressources/implementations_python.py](ressources/implementations_python.py)

**Entropie Topologique**
- Définition : [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)
- Calculs : [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)
- Principe variationnel : [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)

### F

**Formules Mathématiques**
- Formulaire complet : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Niveau débutant : [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- Niveau intermédiaire : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Niveau expert : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)

### G

**Glossaire**
- Définitions complètes : [Ressources/glossaire.md](ressources/glossaire.md)
- Concepts de base : [Débutant/01_introduction.md](niveau_debutant/01_introduction.md)
- Termes avancés : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)

### H

**Huffman (Algorithme de)**
- Description : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Exemple détaillé : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Implémentation : [Ressources/implementations_python.py](ressources/implementations_python.py)

### I

**Implémentations Python**
- Code complet : [Ressources/implementations_python.py](ressources/implementations_python.py)
- Tests : [Ressources/implementations_python.py](ressources/implementations_python.py)
- Exemples : [Ressources/implementations_python.py](ressources/implementations_python.py)

**Inégalités Fondamentales**
- Jensen : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Gibbs : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Kraft : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)

**Information Mutuelle**
- Définition : [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)
- Propriétés : [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)
- Calculs : [Ressources/implementations_python.py](ressources/implementations_python.py)
- Applications : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)

### K

**Kraft (Inégalité de)**
- Énoncé : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Démonstration : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Applications : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)

**Kolmogorov-Sinai (Entropie)**
- Voir : [Entropie Métrique](#entropie-métrique-kolmogorov-sinai)

### L

**Logarithmes**
- Bases : [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- Conversions : [Ressources/formulaire_complet.md](ressources/formulaire_complet.md)
- Calcul sécurisé : [Ressources/implementations_python.py](ressources/implementations_python.py)

### M

**Machine Learning**
- Applications : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- Sélection de caractéristiques : [Débutant/exercices_debutant.md](niveau_debutant/exercices_debutant.md)
- Fonctions de perte : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)

### P

**Principe Variationnel**
- Énoncé : [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)
- Démonstration : [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)
- Applications : [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)

**Probabilités**
- Distributions : [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- Conditionnelles : [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)
- Validation : [Ressources/implementations_python.py](ressources/implementations_python.py)

### S

**Shannon (Claude)**
- Biographie : [Ressources/bibliographie.md](ressources/bibliographie.md)
- Théorème fondamental : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Article original : [Ressources/bibliographie.md](ressources/bibliographie.md)

**Systèmes Dynamiques**
- Entropie métrique : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)
- Entropie topologique : [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)
- Applications : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)

### T

**Théorème de Codage de Source**
- Énoncé : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Démonstration : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- Applications : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)

---

## 📊 INDEX PAR FORMULES

### Formules de Base
- **H(X) = -∑ p(x) log₂ p(x)** → [Débutant/02_formule_shannon.md](niveau_debutant/02_formule_shannon.md)
- **H(Y|X) = ∑ p(x) H(Y|X=x)** → [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)
- **I(X;Y) = H(X) + H(Y) - H(X,Y)** → [Débutant/03_entropie_conditionnelle.md](niveau_debutant/03_entropie_conditionnelle.md)

### Formules Intermédiaires
- **D(p||q) = ∑ p(x) log₂(p(x)/q(x))** → [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- **H(p,q) = -∑ p(x) log₂ q(x)** → [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)
- **∑ 2^(-lᵢ) ≤ 1** → [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)

### Formules Avancées
- **h_μ(T) = sup_α h_μ(T,α)** → [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)
- **h_top(T) = sup{h_μ(T) : μ T-invariante}** → [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)

---

## 🎯 INDEX PAR APPLICATIONS

### Informatique
- **Compression** : [Intermédiaire/02_codage_source.md](niveau_intermediaire/02_codage_source.md)
- **Cryptographie** : [Débutant/exercices_debutant.md](niveau_debutant/exercices_debutant.md)
- **Machine Learning** : [Intermédiaire/01_entropie_relative.md](niveau_intermediaire/01_entropie_relative.md)

### Physique
- **Thermodynamique** : [Débutant/01_introduction.md](niveau_debutant/01_introduction.md)
- **Mécanique statistique** : [Expert/01_entropie_metrique.md](niveau_expert/01_entropie_metrique.md)
- **Systèmes dynamiques** : [Expert/02_entropie_topologique.md](niveau_expert/02_entropie_topologique.md)

### Biologie
- **Bioinformatique** : [Débutant/exercices_debutant.md](niveau_debutant/exercices_debutant.md)
- **Génétique** : [Ressources/glossaire.md](ressources/glossaire.md)
- **Évolution** : [Ressources/glossaire.md](ressources/glossaire.md)

---

## 📚 INDEX PAR NIVEAU

### 🟢 Niveau Débutant
- [Introduction](niveau_debutant/01_introduction.md)
- [Formule de Shannon](niveau_debutant/02_formule_shannon.md)
- [Entropie Conditionnelle](niveau_debutant/03_entropie_conditionnelle.md)
- [Exercices](niveau_debutant/exercices_debutant.md)

### 🟡 Niveau Intermédiaire
- [Entropie Relative](niveau_intermediaire/01_entropie_relative.md)
- [Codage de Source](niveau_intermediaire/02_codage_source.md)

### 🔴 Niveau Expert
- [Entropie Métrique](niveau_expert/01_entropie_metrique.md)
- [Entropie Topologique](niveau_expert/02_entropie_topologique.md)

### 📖 Ressources
- [Formulaire Complet](ressources/formulaire_complet.md)
- [Implémentations Python](ressources/implementations_python.py)
- [Glossaire](ressources/glossaire.md)
- [Bibliographie](ressources/bibliographie.md)

---

## 🔍 RECHERCHE RAPIDE

### Par Mot-Clé
- **Entropie** : Tous les fichiers
- **Shannon** : [Débutant/02](niveau_debutant/02_formule_shannon.md), [Bibliographie](ressources/bibliographie.md)
- **Kolmogorov** : [Expert/01](niveau_expert/01_entropie_metrique.md), [Glossaire](ressources/glossaire.md)
- **Huffman** : [Intermédiaire/02](niveau_intermediaire/02_codage_source.md), [Python](ressources/implementations_python.py)
- **Compression** : [Débutant/01](niveau_debutant/01_introduction.md), [Intermédiaire/02](niveau_intermediaire/02_codage_source.md)

### Par Difficulté
- **Facile** : Niveau Débutant + Ressources/Glossaire
- **Moyen** : Niveau Intermédiaire + Exercices
- **Difficile** : Niveau Expert + Bibliographie

### Par Type de Contenu
- **Théorie** : Tous les niveaux
- **Pratique** : Exercices + Implémentations
- **Applications** : Exemples dans tous les niveaux
- **Références** : Bibliographie + Glossaire

---

*Cet index permet une navigation efficace dans l'ensemble du cours d'entropie, facilitant la recherche d'informations spécifiques et les références croisées.*
