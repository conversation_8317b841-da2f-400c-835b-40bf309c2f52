# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2675 à 2696
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def calculate_required_index1(self, current_index5):
        """
        Calcule INDEX1 obligatoire selon les règles déterministes

        Règles:
        - Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
        - Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
        """
        if not current_index5:
            return None

        try:
            parts = current_index5.split('_')
            current_index1 = int(parts[0])
            current_index2 = parts[1]

            if current_index2 == 'C':
                return 1 - current_index1  # Inversion
            else:  # A ou B
                return current_index1      # Conservation
        except:
            return None