# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 1866 à 1879
# Type: Méthode de la classe INDEX5Predictor

    def predict_compression_patterns(self, sequence_history):
        """
        Exploite les patterns de compression pour prédiction
        """
        # Chercher les patterns répétitifs les plus récents
        for pattern_len in range(2, min(6, len(sequence_history))):
            recent_pattern = sequence_history[-pattern_len:]

            # Chercher ce pattern dans l'historique
            continuation = self.find_exact_pattern_continuation(recent_pattern, sequence_history)
            if continuation:
                return continuation

        return None