# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 1540 à 1582
# Type: Méthode de la classe INDEX5Calculator

    def calculate_bayesian_theoretical_alignment(self, sequence_history):
        """
        Calcule l'alignement entre les observations récentes et les probabilités théoriques
        en utilisant une approche bayésienne. Plus le score est élevé, plus les observations
        récentes sont alignées avec la théorie.
        """
        if len(sequence_history) < 10:
            return 0.0

        from collections import Counter

        # Analyser les 20 dernières observations
        recent_sequence = sequence_history[-20:] if len(sequence_history) >= 20 else sequence_history
        observed_freq = Counter(recent_sequence)

        # Calculer le score d'alignement bayésien
        alignment_scores = []

        for index5_value in self.THEORETICAL_PROBS.keys():
            p_theoretical = self.THEORETICAL_PROBS[index5_value]
            observed_count = observed_freq.get(index5_value, 0)
            p_observed = observed_count / len(recent_sequence)

            # Score d'alignement pour cette valeur (1 - différence relative)
            if p_theoretical > 0:
                relative_diff = abs(p_observed - p_theoretical) / p_theoretical
                alignment_score = max(0.0, 1.0 - relative_diff)
                alignment_scores.append(alignment_score)

        if alignment_scores:
            # Score moyen pondéré par les probabilités théoriques
            weighted_alignment = 0.0
            total_weight = 0.0

            for i, (index5_value, p_theoretical) in enumerate(self.THEORETICAL_PROBS.items()):
                if i < len(alignment_scores):
                    weighted_alignment += alignment_scores[i] * p_theoretical
                    total_weight += p_theoretical

            if total_weight > 0:
                return round(weighted_alignment / total_weight, 4)

        return 0.0