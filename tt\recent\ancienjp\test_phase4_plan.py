"""
Tests PHASE 4 - Optimisation et Déploiement selon plan.txt (lignes 849-904)
Tests de Performance selon lignes 881-904
"""

import os
import sys
import time

# CORRECTIF : Ajouter le répertoire bin de Julia au PATH AVANT tout import
julia_bin_path = r"C:\Users\<USER>\.julia\juliaup\julia-1.11.5+0.x64.w64.mingw32\bin"
current_path = os.environ.get('PATH', '')
if julia_bin_path not in current_path:
    os.environ['PATH'] = julia_bin_path + os.pathsep + current_path

from python_julia_bridge import AdvancedJuliaPythonBridge

def generate_test_sequence(length):
    """Génère une séquence de test selon plan.txt ligne 886"""
    base_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE", "0_A_BANKER", "1_B_PLAYER"]
    return (base_sequence * (length // len(base_sequence) + 1))[:length]

def benchmark_julia_performance():
    """
    Compare performance Julia selon plan.txt lignes 882-904
    """
    print("=== TESTS PHASE 4 PERFORMANCE SELON PLAN.TXT ===")
    
    # Séquence de test selon plan ligne 886
    sequence = generate_test_sequence(1000)  # 1000 éléments
    print(f"1. Séquence de test générée: {len(sequence)} éléments")
    
    # Test Julia hybride selon plan lignes 894-898
    print("\n2. Test Julia hybride:")
    analyzer_julia = AdvancedJuliaPythonBridge()
    
    start = time.time()
    result_julia = analyzer_julia.analyze_complete_game_one_based(sequence)
    time_julia = time.time() - start
    
    print(f"   Temps Julia: {time_julia:.3f}s")
    print(f"   Métriques calculées: {len(result_julia)}")
    print(f"   Séquence analysée: {result_julia['sequence_length']} éléments")
    print(f"   Évolution entropique: {len(result_julia['entropy_evolution'])} positions")
    
    # Métriques de performance selon plan lignes 900-904
    print(f"\n3. Métriques de performance:")
    elements_per_second = len(sequence) / time_julia
    print(f"   Éléments/seconde: {elements_per_second:.0f}")
    print(f"   Temps par élément: {time_julia/len(sequence)*1000:.3f}ms")
    
    # Validation des résultats
    print(f"\n4. Validation des résultats:")
    valid_results = (
        result_julia['sequence_length'] == len(sequence) and
        len(result_julia['entropy_evolution']) == len(sequence) and
        'complexity_metrics' in result_julia and
        time_julia > 0
    )
    
    print(f"   Résultats valides: {valid_results}")
    print(f"   Performance acceptable (< 5s): {time_julia < 5.0}")
    
    # Critères de succès selon plan
    success = valid_results and time_julia < 5.0
    
    if success:
        print("\nPHASE 4 PERFORMANCE VALIDEE SELON PLAN.TXT")
        print(f"Architecture Julia opérationnelle: {time_julia:.3f}s pour {len(sequence)} éléments")
        return True
    else:
        print("\nPHASE 4 PERFORMANCE INSUFFISANTE")
        return False

if __name__ == "__main__":
    success = benchmark_julia_performance()
    sys.exit(0 if success else 1)
