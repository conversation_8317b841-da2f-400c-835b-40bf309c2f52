# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 462 à 514
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def _calculate_conditional_entropy(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie conditionnelle H(Xₙ|X₁,...,Xₙ₋₁) avec contexte fixe.
        CORRECTION EXPERTE: Utilise un contexte de longueur fixe pour cohérence mathématique.

        Référence: entropie/cours_entropie/niveau_debutant/03_entropie_conditionnelle.md
        Formule: H(X|Y) = ∑ P(y) × H(X|y)
        """
        if len(sequence) < 2:
            return 0.0

        # CORRECTION: Utiliser un contexte de longueur fixe (longueur 1 pour simplicité)
        # Cela donne H(Xₙ|Xₙ₋₁) au lieu de mélanger différentes longueurs
        context_length = min(1, len(sequence) - 1)

        # Compter les transitions contexte → symbole suivant
        context_transitions = {}

        for i in range(len(sequence) - context_length):
            if context_length == 1:
                context = sequence[i]  # Contexte de longueur 1
            else:
                context = tuple(sequence[i:i+context_length])

            next_symbol = sequence[i+context_length]

            if context not in context_transitions:
                context_transitions[context] = {}

            if next_symbol not in context_transitions[context]:
                context_transitions[context][next_symbol] = 0
            context_transitions[context][next_symbol] += 1

        if not context_transitions:
            return 0.0

        # Calculer H(X|Contexte) = ∑ P(contexte) × H(X|contexte)
        total_transitions = sum(sum(transitions.values()) for transitions in context_transitions.values())
        conditional_entropy = 0.0

        for context, transitions in context_transitions.items():
            context_prob = sum(transitions.values()) / total_transitions

            # CORRECTION AEP: H(X|ce contexte) calculé selon AEP
            # Créer la séquence des symboles suivants pour ce contexte
            context_sequence = []
            for next_symbol, count in transitions.items():
                context_sequence.extend([next_symbol] * count)

            context_entropy = self._calculate_sequence_entropy_aep(context_sequence)
            conditional_entropy += context_prob * context_entropy

        return conditional_entropy