"""
verification_plan_complete.py - Vérification complète de la conformité au plan.txt
Teste chaque étape du plan ligne par ligne
"""

import os
import sys

# CORRECTIF : Ajouter le répertoire bin de Julia au PATH AVANT tout import
julia_bin_path = r"C:\Users\<USER>\.julia\juliaup\julia-1.11.5+0.x64.w64.mingw32\bin"
current_path = os.environ.get('PATH', '')
if julia_bin_path not in current_path:
    os.environ['PATH'] = julia_bin_path + os.pathsep + current_path

from python_julia_bridge import AdvancedJuliaPythonBridge

def test_phase_1_julia_core():
    """
    PHASE 1 : Vérification des modules Julia Core (lignes 654-698 plan.txt)
    """
    print("=== PHASE 1 : MODULES JULIA CORE ===")
    
    # Test 1.1 : Fichier julia_entropy_core.jl existe
    if os.path.exists('julia_entropy_core.jl'):
        print("OK: julia_entropy_core.jl existe")
    else:
        print("ERREUR: julia_entropy_core.jl manquant")
        return False
    
    # Test 1.2 : Module contient les 14 méthodes requises
    with open('julia_entropy_core.jl', 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_functions = [
        'safe_log_one_based', 'validate_probabilities_one_based',
        'calculate_shannon_entropy_one_based', 'calculate_sequence_entropy_aep_one_based',
        'calculate_conditional_entropy_one_based', 'estimate_metric_entropy_one_based',
        'calculate_block_entropies_one_based', 'calculate_block_entropies_raw_one_based',
        'calculate_block_entropy_evolution_one_based', 'calculate_sequence_complexity_one_based',
        'approximate_lz_complexity_one_based', 'approximate_topological_entropy_one_based',
        'calculate_repetition_rate_one_based', 'calculate_simple_entropy_theoretical_one_based'
    ]
    
    missing_functions = []
    for func in required_functions:
        if f'function {func}' not in content:
            missing_functions.append(func)
    
    if missing_functions:
        print(f"ERREUR: Fonctions manquantes: {missing_functions}")
        return False
    else:
        print(f"OK: Toutes les 14 fonctions présentes")
    
    # Test 1.3 : Probabilités théoriques corrigées
    if 'THEORETICAL_PROBS_ONE_BASED' in content and '6.4676' in content:
        print("OK: Probabilités théoriques corrigées présentes")
    else:
        print("ERREUR: Probabilités théoriques incorrectes")
        return False
    
    print("PHASE 1 : COMPLETE\n")
    return True

def test_phase_2_python_bridge():
    """
    PHASE 2 : Vérification du pont Python-Julia (lignes 700-750 plan.txt)
    """
    print("=== PHASE 2 : PONT PYTHON-JULIA ===")
    
    # Test 2.1 : Fichier python_julia_bridge.py existe
    if os.path.exists('python_julia_bridge.py'):
        print("OK: python_julia_bridge.py existe")
    else:
        print("ERREUR: python_julia_bridge.py manquant")
        return False
    
    # Test 2.2 : Pont contient toutes les méthodes
    try:
        bridge = AdvancedJuliaPythonBridge()
        print("OK: Pont initialisé avec succès")
    except Exception as e:
        print(f"ERREUR: Initialisation pont échouée: {e}")
        return False
    
    # Test 2.3 : Test des méthodes principales
    test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"]
    
    try:
        shannon_result = bridge.calculate_shannon_entropy_one_based([0.5, 0.3, 0.2])
        aep_result = bridge.calculate_sequence_entropy_aep_one_based(test_sequence)
        complexity_result = bridge.calculate_sequence_complexity_one_based(test_sequence)
        
        print(f"OK: Shannon entropy: {shannon_result}")
        print(f"OK: AEP entropy: {aep_result}")
        print(f"OK: Complexité: {len(complexity_result)} métriques")
        
    except Exception as e:
        print(f"ERREUR: Test méthodes échoué: {e}")
        return False
    
    print("PHASE 2 : COMPLETE\n")
    return True

def test_phase_3_configuration():
    """
    PHASE 3 : Vérification du script de configuration (lignes 752-800 plan.txt)
    """
    print("=== PHASE 3 : SCRIPT CONFIGURATION ===")
    
    # Test 3.1 : Fichier setup_julia_python.py existe
    if os.path.exists('setup_julia_python.py'):
        print("OK: setup_julia_python.py existe")
    else:
        print("ERREUR: setup_julia_python.py manquant")
        return False
    
    # Test 3.2 : Script contient les fonctions requises
    with open('setup_julia_python.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_functions = [
        'install_julia_dependencies', 'install_python_dependencies',
        'test_julia_module', 'test_python_julia_bridge', 'benchmark_performance'
    ]
    
    for func in required_functions:
        if f'def {func}' in content:
            print(f"OK: Fonction {func} présente")
        else:
            print(f"ERREUR: Fonction {func} manquante")
            return False
    
    print("PHASE 3 : COMPLETE\n")
    return True

def test_phase_4_validation():
    """
    PHASE 4 : Tests et validation (lignes 802-850 plan.txt)
    """
    print("=== PHASE 4 : TESTS ET VALIDATION ===")
    
    # Test 4.1 : Analyse complète d'une séquence
    bridge = AdvancedJuliaPythonBridge()
    test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE", "1_A_BANKER", "0_C_TIE"] * 20  # 100 éléments
    
    try:
        result = bridge.analyze_complete_game_one_based(test_sequence)
        
        required_keys = [
            'sequence_length', 'full_sequence', 'entropy_evolution',
            'final_metric_entropy', 'final_conditional_entropy',
            'complexity_metrics'
        ]
        
        for key in required_keys:
            if key in result:
                print(f"OK: Clé {key} présente")
            else:
                print(f"ERREUR: Clé {key} manquante")
                return False
        
        print(f"OK: Analyse complète - {result['sequence_length']} éléments")
        print(f"OK: Evolution entropique - {len(result['entropy_evolution'])} positions")
        
    except Exception as e:
        print(f"ERREUR: Analyse complète échouée: {e}")
        return False
    
    print("PHASE 4 : COMPLETE\n")
    return True

def main():
    """
    Vérification complète de la conformité au plan.txt
    """
    print("VERIFICATION COMPLETE DE LA CONFORMITE AU PLAN.TXT")
    print("=" * 60)
    
    phases = [
        ("PHASE 1 - Modules Julia Core", test_phase_1_julia_core),
        ("PHASE 2 - Pont Python-Julia", test_phase_2_python_bridge),
        ("PHASE 3 - Script Configuration", test_phase_3_configuration),
        ("PHASE 4 - Tests et Validation", test_phase_4_validation)
    ]
    
    passed_phases = []
    failed_phases = []
    
    for phase_name, phase_test in phases:
        try:
            if phase_test():
                passed_phases.append(phase_name)
            else:
                failed_phases.append(phase_name)
        except Exception as e:
            print(f"ERREUR CRITIQUE dans {phase_name}: {e}")
            failed_phases.append(phase_name)
    
    # Résumé final
    print("=" * 60)
    print("RESUME FINAL")
    print("=" * 60)
    
    print(f"Phases reussies ({len(passed_phases)}/4):")
    for phase in passed_phases:
        print(f"  OK {phase}")

    if failed_phases:
        print(f"\nPhases echouees ({len(failed_phases)}/4):")
        for phase in failed_phases:
            print(f"  ERREUR {phase}")

    success_rate = len(passed_phases) / len(phases) * 100
    print(f"\nTaux de conformite au plan.txt: {success_rate:.1f}%")

    if len(passed_phases) == len(phases):
        print("\nPLAN.TXT EXECUTE AVEC SUCCES COMPLET !")
        print("Architecture hybride Julia-Python parfaitement conforme au plan")
        return True
    else:
        print("\nCORRECTIONS NECESSAIRES")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
