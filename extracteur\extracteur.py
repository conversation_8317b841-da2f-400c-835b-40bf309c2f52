#!/usr/bin/env python3
"""
Assistant d'extraction de méthodes pour le code Python.
Cet assistant permet d'extraire toutes les méthodes et classes d'un projet Python
et de les sauvegarder dans des fichiers texte séparés.
"""

import os
import sys
import argparse
import time
import shutil
import ast
import re
import tokenize
import io
from typing import List, Dict, Tuple, Optional, Set

class MethodExtractor(ast.NodeVisitor):
    """
    Visiteur AST qui extrait les méthodes et classes des fichiers Python.
    """

    def __init__(self, source_code: str, file_path: str):
        """
        Initialise l'extracteur de méthodes.

        Args:
            source_code: Le code source à analyser
            file_path: Le chemin du fichier source
        """
        self.source_code = source_code
        self.source_lines = source_code.splitlines()
        self.file_path = file_path
        self.methods = []
        self.classes = []
        self.classes_without_methods = []  # Nouvelle liste pour les classes sans méthodes
        self.current_class = None

    def visit_ClassDef(self, node):
        """
        Visite une définition de classe et extrait ses informations.

        Args:
            node: Le nœud AST de la classe
        """
        # Sauvegarder la classe parente si elle existe
        parent_class = self.current_class

        # Obtenir la ligne de déclaration de la classe et l'indication des lignes
        class_line = self.source_lines[node.lineno - 1]
        end_line = self._get_end_line(node)
        class_declaration = f"{class_line} (Lignes: {node.lineno} à {end_line})"

        # Définir la classe courante pour les deux types de fichiers (avec et sans méthodes)
        self.current_class = {
            'name': node.name,
            'lineno': node.lineno,
            'end_lineno': end_line,
            'source': class_declaration,  # Utiliser uniquement la déclaration pour les fichiers class_
            'parent_class': parent_class['name'] if parent_class else None,
            'file_path': self.file_path
        }

        # Ajouter la classe à la liste (pour les fichiers class_)
        self.classes.append(self.current_class)

        # Ajouter également à la liste des classes sans méthodes (pour les fichiers class_without_methods_)
        # Nous utilisons le même format pour les deux types de fichiers
        self.classes_without_methods.append(self.current_class.copy())

        # Visiter les enfants (méthodes de la classe)
        self.generic_visit(node)

        # Restaurer la classe parente
        self.current_class = parent_class



    def visit_FunctionDef(self, node):
        """
        Visite une définition de fonction et extrait ses informations.

        Args:
            node: Le nœud AST de la fonction
        """
        # Extraire les informations de la méthode
        method = {
            'name': node.name,
            'lineno': node.lineno,
            'end_lineno': self._get_end_line(node),
            'source': self._get_source(node),
            'class_name': self.current_class['name'] if self.current_class else None,
            'file_path': self.file_path
        }

        # Ajouter la méthode à la liste
        self.methods.append(method)

        # Visiter les enfants (fonctions imbriquées)
        self.generic_visit(node)

    def _get_end_line(self, node) -> int:
        """
        Obtient le numéro de la dernière ligne d'un nœud AST.

        Args:
            node: Le nœud AST

        Returns:
            Le numéro de la dernière ligne
        """
        if hasattr(node, 'end_lineno') and node.end_lineno is not None:
            return node.end_lineno

        # Parcourir récursivement pour trouver la dernière ligne
        max_line = node.lineno
        for child in ast.iter_child_nodes(node):
            if hasattr(child, 'lineno'):
                end_line = self._get_end_line(child)
                max_line = max(max_line, end_line)

        # Ajouter quelques lignes pour les accolades fermantes, etc.
        return max_line + 1

    def _get_source(self, node) -> str:
        """
        Extrait le code source d'un nœud AST.

        Args:
            node: Le nœud AST

        Returns:
            Le code source du nœud
        """
        start_line = node.lineno - 1  # Les lignes commencent à 0 dans la liste
        end_line = self._get_end_line(node)

        # Extraire les lignes de code avec leur indentation d'origine
        source_lines = self.source_lines[start_line:end_line]

        # Retourner le code source avec l'indentation d'origine préservée
        return '\n'.join(source_lines)


def extract_methods_from_file(file_path: str) -> Tuple[List[Dict], List[Dict], List[Dict]]:
    """
    Extrait toutes les méthodes et classes d'un fichier Python.

    Args:
        file_path: Le chemin du fichier à analyser

    Returns:
        Un tuple contenant la liste des méthodes, la liste des classes et la liste des classes sans méthodes
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source_code = f.read()

        # Analyser le code avec AST
        tree = ast.parse(source_code)
        extractor = MethodExtractor(source_code, file_path)
        extractor.visit(tree)

        return extractor.methods, extractor.classes, extractor.classes_without_methods
    except Exception as e:
        print(f"Erreur lors de l'analyse de {file_path}: {e}")
        return [], [], []


def save_to_file(item: Dict, output_dir: str, item_type: str):
    """
    Sauvegarde une méthode ou une classe dans un fichier texte.
    Évite l'écrasement des méthodes homonymes en ajoutant un suffixe numérique.

    Args:
        item: La méthode ou classe à sauvegarder
        output_dir: Le répertoire de sortie
        item_type: Le type d'élément ('method' ou 'class')
    """
    # Créer un nom de fichier sécurisé
    file_name = item['name']

    # Ajouter un préfixe pour le type de classe uniquement
    if item_type == 'method':
        prefix = ""  # Pas de préfixe pour les méthodes
        # Ne pas inclure le nom de la classe dans le nom du fichier pour les méthodes
    else:  # class
        prefix = "class_"

    # Créer le chemin du fichier
    base_name = os.path.basename(item['file_path']).replace('.py', '')

    # Créer le répertoire de sortie
    # Utiliser uniquement le nom du fichier Python comme nom de dossier
    output_subdir = os.path.join(output_dir, base_name)
    os.makedirs(output_subdir, exist_ok=True)

    # Créer le chemin complet du fichier - ne pas inclure le nom de la classe pour les méthodes
    base_file_path = os.path.join(output_subdir, f"{prefix}{file_name}")
    file_path = f"{base_file_path}.txt"

    # Vérifier si le fichier existe déjà et ajouter un suffixe numérique si nécessaire
    counter = 1
    while os.path.exists(file_path):
        # Lire le contenu du fichier existant pour vérifier s'il s'agit d'une méthode différente
        with open(file_path, 'r', encoding='utf-8') as f:
            existing_content = f.read()

        # Si le fichier contient exactement la même méthode (même emplacement dans le fichier source),
        # nous pouvons simplement le remplacer
        source_location = f"# Fichier: {os.path.abspath(item['file_path'])}\n# Lignes: {item['lineno']} à {item['end_lineno']}"
        if source_location in existing_content:
            # Même méthode, on peut remplacer
            break

        # Sinon, c'est une méthode homonyme, on crée un nouveau fichier avec un suffixe
        file_path = f"{base_file_path}_{counter}.txt"
        counter += 1

        # Journaliser la détection d'une méthode homonyme
        if counter == 2:  # Premier conflit détecté
            print(f"ATTENTION: Méthodes homonymes détectées pour '{file_name}' dans {base_name}")

    # Ajouter des informations détaillées sur l'emplacement d'origine
    header = f"# Emplacement exact dans le fichier source:\n"
    header += f"# Fichier: {os.path.abspath(item['file_path'])}\n"
    header += f"# Lignes: {item['lineno']} à {item['end_lineno']}\n"

    # Ajouter des informations supplémentaires selon le type
    if item_type == 'method':
        header += f"# Type: Méthode"
        if item['class_name']:
            header += f" de la classe {item['class_name']}"
    else:  # class
        header += f"# Type: Classe complète"
        if item['parent_class']:
            header += f" (hérite de {item['parent_class']})"

    # Ajouter une information sur le suffixe si c'est une méthode homonyme
    if counter > 1:
        header += f"\n# Note: Cette méthode est homonyme (même nom que d'autres méthodes)"

    header += "\n\n"

    # Sauvegarder le contenu
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(header + item['source'])

    # Afficher le message approprié
    if counter > 1:
        print(f"Sauvegardé (homonyme #{counter-1}): {file_path}")
    else:
        print(f"Sauvegardé: {file_path}")


def process_directory(directory: str, output_dir: str):
    """
    Traite uniquement les fichiers Python du répertoire spécifié (sans les sous-répertoires),
    en excluant le fichier method_extraction_assistant.py lui-même.

    Args:
        directory: Le répertoire à traiter
        output_dir: Le répertoire de sortie pour les fichiers extraits
    """
    # Créer le répertoire de sortie s'il n'existe pas
    os.makedirs(output_dir, exist_ok=True)

    # Obtenir le chemin absolu du script actuel
    current_script = os.path.abspath(__file__)
    current_script_name = os.path.basename(__file__)

    # Obtenir le chemin absolu du répertoire
    abs_directory = os.path.abspath(directory)

    # Lister uniquement les fichiers Python du répertoire spécifié (sans les sous-répertoires)
    # et exclure le fichier method_extraction_assistant.py
    python_files = []
    for f in os.listdir(directory):
        file_path = os.path.join(directory, f)
        # Ne prendre que les fichiers Python qui sont directement dans le répertoire spécifié
        if os.path.isfile(file_path) and f.endswith('.py') and f != current_script_name:
            # Vérifier que ce n'est pas le script actuel (double vérification)
            if os.path.abspath(file_path) != current_script:
                python_files.append(file_path)

    # Traiter uniquement les fichiers Python du répertoire spécifié
    for file_path in python_files:
        print(f"Traitement de {file_path}...")

        # Extraire les méthodes, classes et classes sans méthodes (ces dernières ne seront pas utilisées)
        methods, classes, _ = extract_methods_from_file(file_path)

        # Sauvegarder les méthodes
        for method in methods:
            save_to_file(method, output_dir, 'method')

        # Sauvegarder les classes complètes
        for cls in classes:
            save_to_file(cls, output_dir, 'class')


class MethodExtractionAssistant:
    """
    Assistant pour extraire les méthodes et classes d'un projet Python.
    """

    def __init__(self):
        """
        Initialise l'assistant d'extraction de méthodes.
        """
        self.output_dir = "extracted_methods"
        self.source_dir = ""
        self.extracted_count = 0
        self.processed_files = 0

    def welcome(self):
        """
        Affiche un message de bienvenue.
        """
        print("\n" + "=" * 80)
        print("ASSISTANT D'EXTRACTION DE MÉTHODES PYTHON".center(80))
        print("=" * 80)
        print("\nCet assistant va extraire toutes les méthodes et classes de votre projet Python")
        print("et les sauvegarder dans des fichiers texte séparés pour une analyse facile.")
        print("\n" + "-" * 80 + "\n")

    def get_source_directory(self) -> str:
        """
        Demande à l'utilisateur le répertoire source à analyser.

        Returns:
            Le chemin du répertoire source
        """
        default_dir = os.getcwd()

        while True:
            print(f"Répertoire source à analyser [par défaut: {default_dir}]: ", end="")
            source_dir = input().strip()

            if not source_dir:
                source_dir = default_dir

            if not os.path.exists(source_dir):
                print(f"Erreur: Le répertoire '{source_dir}' n'existe pas.")
                continue

            if not os.path.isdir(source_dir):
                print(f"Erreur: '{source_dir}' n'est pas un répertoire.")
                continue

            return source_dir

    def get_output_directory(self) -> str:
        """
        Demande à l'utilisateur le répertoire de sortie.

        Returns:
            Le chemin du répertoire de sortie
        """
        default_output = os.path.join(os.getcwd(), "extracted_methods")

        print(f"Répertoire de sortie pour les méthodes extraites [par défaut: {default_output}]: ", end="")
        output_dir = input().strip()

        if not output_dir:
            output_dir = default_output

        return output_dir

    def confirm_extraction(self) -> bool:
        """
        Demande confirmation à l'utilisateur avant de commencer l'extraction.

        Returns:
            True si l'utilisateur confirme, False sinon
        """
        print("\n" + "-" * 80)
        print(f"Prêt à extraire les méthodes de: {self.source_dir}")
        print(f"Les méthodes seront sauvegardées dans: {self.output_dir}")
        print("-" * 80)

        while True:
            print("Voulez-vous continuer? (o/n): ", end="")
            response = input().strip().lower()

            if response in ['o', 'oui', 'y', 'yes']:
                return True
            elif response in ['n', 'non', 'no']:
                return False
            else:
                print("Veuillez répondre par 'o' (oui) ou 'n' (non).")

    def prepare_output_directory(self):
        """
        Prépare le répertoire de sortie.
        """
        if os.path.exists(self.output_dir):
            print(f"\nLe répertoire de sortie '{self.output_dir}' existe déjà.")
            print("Que souhaitez-vous faire?")
            print("1. Supprimer le contenu existant et continuer")
            print("2. Conserver le contenu existant et ajouter les nouvelles méthodes")
            print("3. Annuler l'opération")

            while True:
                print("Votre choix (1-3): ", end="")
                choice = input().strip()

                if choice == '1':
                    print(f"Suppression du contenu de '{self.output_dir}'...")
                    shutil.rmtree(self.output_dir)
                    os.makedirs(self.output_dir)
                    break
                elif choice == '2':
                    print(f"Conservation du contenu existant de '{self.output_dir}'...")
                    break
                elif choice == '3':
                    print("Opération annulée.")
                    sys.exit(0)
                else:
                    print("Choix invalide. Veuillez entrer 1, 2 ou 3.")
        else:
            os.makedirs(self.output_dir)

    def extract_methods(self):
        """
        Extrait les méthodes et classes du répertoire source.
        """
        print("\nDémarrage de l'extraction des méthodes...")
        start_time = time.time()

        # Utiliser la fonction process_directory intégrée
        process_directory(self.source_dir, self.output_dir)

        end_time = time.time()
        self.duration = end_time - start_time

        # Compter les fichiers extraits
        method_count = 0
        class_count = 0

        for root, _, files in os.walk(self.output_dir):
            for file in files:
                if not file.startswith("class_") and file.endswith(".txt"):
                    method_count += 1
                elif file.startswith("class_"):
                    class_count += 1

        self.extracted_count = method_count + class_count

        print("\n" + "=" * 80)
        print("EXTRACTION TERMINÉE".center(80))
        print("=" * 80)
        print(f"Méthodes extraites: {method_count}")
        print(f"Classes complètes extraites: {class_count}")
        print(f"Total: {self.extracted_count} éléments")
        print(f"Durée: {self.duration:.2f} secondes")
        print(f"Répertoire de sortie: {os.path.abspath(self.output_dir)}")
        print("=" * 80)

    def show_extraction_summary(self):
        """
        Affiche un résumé de l'extraction.
        """
        if self.extracted_count == 0:
            print("\nAucune méthode ou classe n'a été extraite.")
            print("Vérifiez que le répertoire source contient des fichiers Python valides.")
            return

        print("\nL'extraction est terminée avec succès!")
        print(f"Vous pouvez trouver les méthodes et classes extraites dans: {os.path.abspath(self.output_dir)}")
        print("\nStructure des fichiers extraits:")
        print("- Chaque méthode est sauvegardée dans un fichier texte séparé")
        print("- Les noms des fichiers sont:")
        print("  * Nom direct de la méthode pour les méthodes (ex: 'get_data.txt')")
        print("  * Préfixe 'class_' pour les classes complètes (ex: 'class_MyClass.txt')")
        print("- Un dossier unique est créé pour chaque fichier Python source")
        print("- Chaque dossier porte le nom du fichier Python source (ex: 'test_class')")
        print("- Chaque fichier contient des informations détaillées sur l'emplacement exact de la classe/méthode dans le fichier source")
        print("- Seuls les fichiers Python du répertoire courant sont traités (sans les sous-répertoires)")
        print("- Le fichier method_extraction_assistant.py lui-même est exclu de l'extraction")

    def run(self):
        """
        Exécute l'assistant d'extraction de méthodes.
        """
        self.welcome()

        # Obtenir les répertoires source et de sortie
        self.source_dir = self.get_source_directory()
        self.output_dir = self.get_output_directory()

        # Confirmer l'extraction
        if not self.confirm_extraction():
            print("Opération annulée.")
            return

        # Préparer le répertoire de sortie
        self.prepare_output_directory()

        # Extraire les méthodes
        self.extract_methods()

        # Afficher le résumé
        self.show_extraction_summary()

def main():
    """
    Point d'entrée principal du script.
    """
    # Analyser les arguments de ligne de commande
    parser = argparse.ArgumentParser(description='Assistant d\'extraction de méthodes pour le code Python.')
    parser.add_argument('--source', '-s', help='Le répertoire source à analyser')
    parser.add_argument('--output', '-o', help='Le répertoire de sortie pour les méthodes extraites')
    parser.add_argument('--yes', '-y', action='store_true', help='Répondre oui à toutes les questions')
    parser.add_argument('--force', '-f', action='store_true', help='Supprimer le répertoire de sortie s\'il existe')

    args = parser.parse_args()

    # Créer l'assistant
    assistant = MethodExtractionAssistant()

    # Si des arguments sont fournis, les utiliser
    if args.source or args.output or args.yes or args.force:
        # Configurer les répertoires
        if args.source:
            assistant.source_dir = args.source
        else:
            assistant.source_dir = os.getcwd()

        if args.output:
            assistant.output_dir = args.output

        # Afficher le message de bienvenue
        assistant.welcome()

        # Vérifier que le répertoire source existe
        if not os.path.exists(assistant.source_dir):
            print(f"Erreur: Le répertoire source '{assistant.source_dir}' n'existe pas.")
            return

        if not os.path.isdir(assistant.source_dir):
            print(f"Erreur: '{assistant.source_dir}' n'est pas un répertoire.")
            return

        # Afficher les informations
        print(f"Répertoire source: {assistant.source_dir}")
        print(f"Répertoire de sortie: {assistant.output_dir}")

        # Gérer le répertoire de sortie
        if os.path.exists(assistant.output_dir):
            if args.force:
                print(f"Suppression du contenu de '{assistant.output_dir}'...")
                shutil.rmtree(assistant.output_dir)
                os.makedirs(assistant.output_dir)
            else:
                print(f"Le répertoire de sortie '{assistant.output_dir}' existe déjà.")
                print("Ajout des nouvelles méthodes au contenu existant.")
        else:
            os.makedirs(assistant.output_dir)

        # Extraire les méthodes
        assistant.extract_methods()

        # Afficher le résumé
        assistant.show_extraction_summary()
    else:
        # Exécuter l'assistant en mode interactif
        assistant.run()

if __name__ == "__main__":
    main()
