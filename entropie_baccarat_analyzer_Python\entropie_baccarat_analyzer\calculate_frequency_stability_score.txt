# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 1631 à 1667
# Type: Méthode de la classe INDEX5Calculator

    def calculate_frequency_stability_score(self, sequence_history):
        """
        Calcule un score de stabilité des fréquences INDEX5.
        Compare les fréquences récentes avec les fréquences globales.
        Plus le score est élevé, plus les fréquences sont stables.
        """
        if len(sequence_history) < 20:
            return 0.0

        from collections import Counter

        # Fréquences globales
        global_freq = Counter(sequence_history)
        global_total = len(sequence_history)

        # Fréquences récentes (30 dernières observations)
        recent_sequence = sequence_history[-30:] if len(sequence_history) >= 30 else sequence_history[-len(sequence_history)//2:]
        recent_freq = Counter(recent_sequence)
        recent_total = len(recent_sequence)

        # Calculer la stabilité pour chaque valeur INDEX5
        stability_scores = []

        for index5_value in self.THEORETICAL_PROBS.keys():
            global_prob = global_freq.get(index5_value, 0) / global_total
            recent_prob = recent_freq.get(index5_value, 0) / recent_total

            # Score de stabilité = 1 - différence relative
            if global_prob > 0:
                relative_diff = abs(recent_prob - global_prob) / global_prob
                stability_score = max(0.0, 1.0 - relative_diff)
                stability_scores.append(stability_score)

        if stability_scores:
            return round(sum(stability_scores) / len(stability_scores), 4)

        return 0.0