"""
Tests PHASE 3 - Migration Progressive selon plan.txt (lignes 805-847)
Vérification PRIORITÉ 1 : Calculs Entropiques Fondamentaux (lignes 807-821)
"""

import os
import sys

# CORRECTIF : Ajouter le répertoire bin de Julia au PATH AVANT tout import
julia_bin_path = r"C:\Users\<USER>\.julia\juliaup\julia-1.11.5+0.x64.w64.mingw32\bin"
current_path = os.environ.get('PATH', '')
if julia_bin_path not in current_path:
    os.environ['PATH'] = julia_bin_path + os.pathsep + current_path

from python_julia_bridge import AdvancedJuliaPythonBridge

def test_priority1_entropy_methods():
    """
    Test PRIORITÉ 1 : 13 méthodes entropiques selon plan.txt lignes 809-821
    """
    print("=== TESTS PHASE 3 PRIORITE 1 SELON PLAN.TXT ===")
    
    # Ordre exact selon plan.txt lignes 809-821
    required_methods = [
        'safe_log_one_based',                           # 1. ligne 809
        'validate_probabilities_one_based',             # 2. ligne 810  
        'calculate_shannon_entropy_one_based',          # 3. ligne 811
        'calculate_sequence_entropy_aep_one_based',     # 4. ligne 812
        'calculate_conditional_entropy_one_based',      # 5. ligne 813
        'estimate_metric_entropy_one_based',            # 6. ligne 814
        'calculate_block_entropies_one_based',          # 7. ligne 815
        'calculate_block_entropies_raw_one_based',      # 8. ligne 816
        'calculate_block_entropy_evolution_one_based',  # 9. ligne 817
        'calculate_sequence_complexity_one_based',      # 10. ligne 818
        'approximate_lz_complexity_one_based',          # 11. ligne 819
        'approximate_topological_entropy_one_based',   # 12. ligne 820
        'calculate_repetition_rate_one_based'           # 13. ligne 821
    ]
    
    bridge = AdvancedJuliaPythonBridge()
    
    print("1. Vérification présence des 13 méthodes PRIORITÉ 1:")
    missing_methods = []
    present_methods = []
    
    for i, method in enumerate(required_methods, 1):
        if hasattr(bridge, method):
            present_methods.append(method)
            print(f"   {i:2d}. {method}: PRESENT")
        else:
            missing_methods.append(method)
            print(f"   {i:2d}. {method}: MANQUANT")
    
    print(f"\n2. Résumé PRIORITÉ 1:")
    print(f"   Méthodes présentes: {len(present_methods)}/13")
    print(f"   Méthodes manquantes: {len(missing_methods)}")
    
    # Test fonctionnel des méthodes présentes
    print("\n3. Tests fonctionnels:")
    test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"]
    functional_tests_passed = 0
    
    try:
        # Test méthodes de base
        if hasattr(bridge, 'calculate_shannon_entropy_one_based'):
            result = bridge.calculate_shannon_entropy_one_based([0.5, 0.3, 0.2])
            print(f"   Shannon entropy: {result}")
            functional_tests_passed += 1
            
        if hasattr(bridge, 'calculate_sequence_entropy_aep_one_based'):
            result = bridge.calculate_sequence_entropy_aep_one_based(test_sequence)
            print(f"   AEP entropy: {result}")
            functional_tests_passed += 1
            
        if hasattr(bridge, 'calculate_block_entropy_evolution_one_based'):
            result = bridge.calculate_block_entropy_evolution_one_based(test_sequence)
            print(f"   Evolution entropique: {len(result)} positions")
            functional_tests_passed += 1
            
    except Exception as e:
        print(f"   ERREUR tests fonctionnels: {e}")
    
    print(f"\n4. Tests fonctionnels réussis: {functional_tests_passed}/3")
    
    # Critères de succès selon plan
    success = (len(missing_methods) == 0 and functional_tests_passed >= 3)
    
    if success:
        print("\nPHASE 3 PRIORITE 1 VALIDEE SELON PLAN.TXT")
        return True
    else:
        print("\nPHASE 3 PRIORITE 1 INCOMPLETE")
        if missing_methods:
            print(f"Méthodes à ajouter: {missing_methods}")
        return False

if __name__ == "__main__":
    success = test_priority1_entropy_methods()
    sys.exit(0 if success else 1)
