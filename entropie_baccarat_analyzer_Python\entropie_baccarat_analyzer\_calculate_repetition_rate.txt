# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 709 à 722
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def _calculate_repetition_rate(self, sequence: List[str]) -> float:
        """
        Calcule le taux de répétition dans la séquence.
        """
        if len(sequence) < 2:
            return 0.0

        # Compter les répétitions immédiates
        repetitions = 0
        for i in range(len(sequence) - 1):
            if sequence[i] == sequence[i + 1]:
                repetitions += 1

        return repetitions / (len(sequence) - 1)