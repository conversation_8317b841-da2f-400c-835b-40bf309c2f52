#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 ANALYSEUR DE CLASSIFICATION INTENSIVE
========================================
Objectif : Identifier quelle règle de sélection appliquée à quelle métrique 
permet de classifier correctement l'INDEX5 qui correspond aux métriques observées.

Méthodologie :
1. Pour chaque main n : extraire les 6 valeurs simulées de chaque métrique
2. Identifier l'INDEX5 observé
3. Tester différentes règles de sélection sur chaque métrique
4. Déterminer quelle règle pointe vers l'INDEX5 observé
"""

import re
import pandas as pd
import numpy as np
from collections import defaultdict

class AnalyseurClassificationIntensive:
    def __init__(self):
        self.donnees_mains = {}
        self.resultats_classification = defaultdict(lambda: defaultdict(int))
        self.index5_possibles = [
            '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', 
            '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER',
            '1_A_BANKER', '1_B_BANKER', '1_C_BANKER', 
            '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER'
        ]
        
    def extraire_donnees_rapport(self, fichier_rapport, fichier_extraction):
        """Extrait les données des tableaux et des observations"""
        print(f"📊 Extraction des données de {fichier_rapport} et {fichier_extraction}")
        
        # Extraction des observations
        observations = self._extraire_observations(fichier_extraction)
        
        # Extraction des tableaux prédictifs
        tableaux = self._extraire_tableaux_predictifs(fichier_rapport)
        
        # Consolidation
        for main, obs in observations.items():
            if main in tableaux['differentiels'] and main in tableaux['scores']:
                self.donnees_mains[main] = {
                    'index5_observe': obs['index5_observe'],
                    'metriques_observees': {
                        'diff_c': obs['diff_c'],
                        'diff_t': obs['diff_t'],
                        'div_eg': obs['div_eg'],
                        'ent_g': obs['ent_g'],
                        'score': obs['score']
                    },
                    'simulations': {
                        'differentiels': tableaux['differentiels'][main],
                        'scores': tableaux['scores'][main]
                    }
                }
        
        print(f"✅ {len(self.donnees_mains)} mains consolidées")
    
    def _extraire_observations(self, fichier_extraction):
        """Extrait les INDEX5 observés et leurs métriques"""
        observations = {}
        
        with open(fichier_extraction, 'r', encoding='utf-8') as f:
            for ligne in f:
                match = re.match(r'\s*(\d+)\s*\|\s*([^|]+)\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)', ligne)
                if match:
                    main = int(match.group(1))
                    index5 = match.group(2).strip()
                    diff_c = float(match.group(3))
                    diff_t = float(match.group(4))
                    div_eg = float(match.group(5))
                    ent_g = float(match.group(6))
                    score = float(match.group(7))
                    
                    observations[main] = {
                        'index5_observe': index5,
                        'diff_c': diff_c,
                        'diff_t': diff_t,
                        'div_eg': div_eg,
                        'ent_g': ent_g,
                        'score': score
                    }
        
        return observations
    
    def _extraire_tableaux_predictifs(self, fichier_rapport):
        """Extrait les tableaux prédictifs avec structure complexe"""
        with open(fichier_rapport, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        tableaux = {
            'differentiels': {},
            'scores': {}
        }
        
        # Extraction des différentiels
        self._extraire_differentiels(contenu, tableaux)
        
        # Extraction des scores
        self._extraire_scores(contenu, tableaux)
        
        return tableaux
    
    def _extraire_differentiels(self, contenu, tableaux):
        """Extrait les tableaux de différentiels"""
        # Recherche du début du tableau différentiels
        pattern_debut = r'🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS POUR LES 9 VALEURS INDEX5 POSSIBLES'
        match_debut = re.search(pattern_debut, contenu)
        
        if not match_debut:
            return
        
        # Extraction de la section
        debut_section = match_debut.end()
        pattern_fin = r'🎯 TABLEAU PRÉDICTIF - SCORES'
        match_fin = re.search(pattern_fin, contenu[debut_section:])
        
        if match_fin:
            fin_section = debut_section + match_fin.start()
            section = contenu[debut_section:fin_section]
        else:
            section = contenu[debut_section:]
        
        # Parsing des mains et INDEX5
        self._parser_section_differentiels(section, tableaux)
    
    def _parser_section_differentiels(self, section, tableaux):
        """Parse la section des différentiels"""
        lignes = section.split('\n')
        
        # Identification des mains
        mains_detectees = []
        for ligne in lignes[:20]:
            matches_main = re.findall(r'Main (\d+)', ligne)
            if matches_main:
                mains_detectees.extend([int(m) for m in matches_main])
        
        mains_detectees = sorted(list(set(mains_detectees)))
        
        if not mains_detectees:
            return
        
        print(f"   📊 Mains détectées dans différentiels: {len(mains_detectees)} ({min(mains_detectees)}-{max(mains_detectees)})")
        
        # Parsing des lignes pour chaque INDEX5
        for ligne in lignes:
            ligne_clean = ligne.strip()
            for index5 in self.index5_possibles:
                if ligne_clean.startswith(index5 + ' ') or ligne_clean.startswith(index5 + '|'):
                    self._parser_ligne_differentiels(ligne, index5, mains_detectees, tableaux)
                    break
    
    def _parser_ligne_differentiels(self, ligne, index5, mains, tableaux):
        """Parse une ligne de différentiels pour un INDEX5"""
        # Suppression du nom INDEX5
        ligne_donnees = ligne.replace(index5, '', 1).strip()
        
        # Pattern pour extraire les groupes de 4 valeurs
        pattern_groupe = r'\|([^|]*)\|([^|]*)\|([^|]*)\|([^|]*)'
        matches = re.findall(pattern_groupe, ligne_donnees)
        
        if not matches:
            return
        
        # Association aux mains
        for i, match in enumerate(matches):
            if i < len(mains):
                main = mains[i]
                diff_c_str, diff_t_str, div_eg_str, ent_g_str = match
                
                # Nettoyage et conversion
                try:
                    diff_c_clean = diff_c_str.strip()
                    diff_t_clean = diff_t_str.strip()
                    div_eg_clean = div_eg_str.strip()
                    ent_g_clean = ent_g_str.strip()
                    
                    if (diff_c_clean != 'N/A' and diff_c_clean != '' and
                        diff_t_clean != 'N/A' and diff_t_clean != '' and
                        div_eg_clean != 'N/A' and div_eg_clean != '' and
                        ent_g_clean != 'N/A' and ent_g_clean != ''):
                        
                        diff_c_val = float(diff_c_clean)
                        diff_t_val = float(diff_t_clean)
                        div_eg_val = float(div_eg_clean)
                        ent_g_val = float(ent_g_clean)
                        
                        # Stockage
                        if main not in tableaux['differentiels']:
                            tableaux['differentiels'][main] = {}
                        
                        tableaux['differentiels'][main][index5] = {
                            'DiffC': diff_c_val,
                            'DiffT': diff_t_val,
                            'DivEG': div_eg_val,
                            'EntG': ent_g_val
                        }
                        
                except (ValueError, TypeError):
                    continue
    
    def _extraire_scores(self, contenu, tableaux):
        """Extrait les tableaux de scores"""
        # Recherche de tous les tableaux de scores
        pattern_scores = r'🎯 TABLEAU PRÉDICTIF - SCORES POUR LES 9 VALEURS INDEX5 POSSIBLES'
        matches_scores = list(re.finditer(pattern_scores, contenu))
        
        print(f"   🎯 {len(matches_scores)} tableau(x) de scores trouvé(s)")
        
        for i, match in enumerate(matches_scores):
            self._extraire_section_scores(contenu, match, tableaux)
    
    def _extraire_section_scores(self, contenu, match_debut, tableaux):
        """Extrait une section de scores"""
        debut_section = match_debut.end()

        # Recherche de la fin (section suivante ou fin de fichier)
        section_recherche = contenu[debut_section:debut_section + 5000]  # Plus large

        # Recherche des mains dans cette section
        mains_section = []
        lignes_section = section_recherche.split('\n')

        for ligne in lignes_section[:15]:  # Plus de lignes à examiner
            matches_main = re.findall(r'Main (\d+)', ligne)
            if matches_main:
                mains_section.extend([int(m) for m in matches_main])

        mains_section = sorted(list(set(mains_section)))

        if not mains_section:
            return

        print(f"   📊 Mains détectées dans scores: {len(mains_section)} ({min(mains_section)}-{max(mains_section)})")

        # Parsing des lignes de scores
        for ligne in lignes_section:
            ligne_clean = ligne.strip()
            for index5 in self.index5_possibles:
                if ligne_clean.startswith(index5 + ' ') or ligne_clean.startswith(index5 + '|'):
                    self._parser_ligne_scores(ligne, index5, mains_section, tableaux)
                    break
    
    def _parser_ligne_scores(self, ligne, index5, mains, tableaux):
        """Parse une ligne de scores"""
        # Suppression du nom INDEX5
        ligne_donnees = ligne.replace(index5, '', 1).strip()
        
        # Pattern pour extraire les scores
        pattern_score = r'\|([^|]*)'
        matches = re.findall(pattern_score, ligne_donnees)
        
        if not matches:
            return
        
        # Association aux mains
        for i, score_str in enumerate(matches):
            if i < len(mains):
                main = mains[i]
                score_clean = score_str.strip()
                
                if score_clean and score_clean != 'N/A' and score_clean != 'SCORE':
                    try:
                        score_val = float(score_clean)
                        
                        # Stockage
                        if main not in tableaux['scores']:
                            tableaux['scores'][main] = {}
                        
                        tableaux['scores'][main][index5] = score_val
                        
                    except (ValueError, TypeError):
                        continue
    
    def analyser_classification_intensive(self):
        """Analyse intensive des règles de classification"""
        print("\n🎯 ANALYSE INTENSIVE DES RÈGLES DE CLASSIFICATION")
        print("=" * 60)
        
        # Métriques à tester
        metriques = ['DiffC', 'DiffT', 'DivEG', 'EntG', 'SCORE']
        
        # Règles de sélection à tester
        regles = ['MIN', 'MAX', 'MEDIAN']
        
        # Statistiques globales
        stats_globales = {}
        
        for metrique in metriques:
            stats_globales[metrique] = {}
            for regle in regles:
                stats_globales[metrique][regle] = {
                    'correct': 0,
                    'total': 0,
                    'precision': 0.0
                }
        
        # Analyse main par main
        for main, donnees in self.donnees_mains.items():
            index5_observe = donnees['index5_observe']
            
            # Test de chaque métrique avec chaque règle
            for metrique in metriques:
                valeurs_metrique = self._extraire_valeurs_metrique(donnees, metrique)
                
                if len(valeurs_metrique) >= 6:  # Au moins 6 valeurs (hors TIE)
                    for regle in regles:
                        index5_predit = self._appliquer_regle(valeurs_metrique, regle)
                        
                        stats_globales[metrique][regle]['total'] += 1
                        
                        if index5_predit == index5_observe:
                            stats_globales[metrique][regle]['correct'] += 1
        
        # Calcul des précisions
        for metrique in metriques:
            for regle in regles:
                stats = stats_globales[metrique][regle]
                if stats['total'] > 0:
                    stats['precision'] = stats['correct'] / stats['total']
        
        # Affichage des résultats
        self._afficher_resultats_classification(stats_globales)
        
        return stats_globales
    
    def _extraire_valeurs_metrique(self, donnees, metrique):
        """Extrait les 6 valeurs d'une métrique pour les INDEX5 non-TIE"""
        valeurs = {}
        
        if metrique == 'SCORE':
            simulations = donnees['simulations']['scores']
        else:
            simulations = donnees['simulations']['differentiels']
        
        # Extraction des valeurs pour les 6 INDEX5 non-TIE
        for index5 in self.index5_possibles:
            if 'TIE' not in index5 and index5 in simulations:
                if metrique == 'SCORE':
                    valeurs[index5] = simulations[index5]
                else:
                    if metrique in simulations[index5]:
                        valeurs[index5] = simulations[index5][metrique]
        
        return valeurs
    
    def _appliquer_regle(self, valeurs_metrique, regle):
        """Applique une règle de sélection et retourne l'INDEX5 correspondant"""
        if not valeurs_metrique:
            return None
        
        if regle == 'MIN':
            return min(valeurs_metrique.keys(), key=lambda k: valeurs_metrique[k])
        elif regle == 'MAX':
            return max(valeurs_metrique.keys(), key=lambda k: valeurs_metrique[k])
        elif regle == 'MEDIAN':
            valeurs_triees = sorted(valeurs_metrique.values())
            mediane = valeurs_triees[len(valeurs_triees)//2]
            return min(valeurs_metrique.keys(), key=lambda k: abs(valeurs_metrique[k] - mediane))
        
        return None
    
    def _afficher_resultats_classification(self, stats_globales):
        """Affiche les résultats de classification"""
        print("\n📊 RÉSULTATS DE CLASSIFICATION INTENSIVE")
        print("=" * 60)
        
        # Tri par précision décroissante
        resultats_tries = []
        for metrique, regles in stats_globales.items():
            for regle, stats in regles.items():
                if stats['total'] > 0:
                    resultats_tries.append({
                        'metrique': metrique,
                        'regle': regle,
                        'precision': stats['precision'],
                        'correct': stats['correct'],
                        'total': stats['total']
                    })
        
        resultats_tries.sort(key=lambda x: x['precision'], reverse=True)
        
        print(f"{'MÉTRIQUE':<10} {'RÈGLE':<8} {'PRÉCISION':<12} {'CORRECT/TOTAL':<15}")
        print("-" * 50)
        
        for resultat in resultats_tries:
            precision_pct = resultat['precision'] * 100
            print(f"{resultat['metrique']:<10} {resultat['regle']:<8} {precision_pct:>8.2f}%    {resultat['correct']:>3}/{resultat['total']:<3}")
        
        # Meilleur résultat
        if resultats_tries:
            meilleur = resultats_tries[0]
            print(f"\n🏆 MEILLEURE RÈGLE : {meilleur['metrique']} - {meilleur['regle']}")
            print(f"   📊 Précision : {meilleur['precision']*100:.2f}%")
            print(f"   ✅ Succès : {meilleur['correct']}/{meilleur['total']}")

def main():
    """Fonction principale d'analyse"""
    print("🎯 DÉMARRAGE DE L'ANALYSE DE CLASSIFICATION INTENSIVE")
    print("=" * 60)

    analyseur = AnalyseurClassificationIntensive()

    # Liste des fichiers à analyser
    fichiers_rapport = [
        ('rapport258.txt', 'rapport_extraction258.txt'),
        ('rapport321.txt', 'rapport_extraction321.txt'),
        ('rapport34.txt', 'rapport_extraction34.txt'),
        ('rapport648.txt', 'rapport_extraction648.txt'),
        ('rapport84.txt', 'rapport_extraction84.txt')
    ]

    # Analyse de tous les fichiers disponibles
    try:
        for rapport, extraction in fichiers_rapport:
            try:
                print(f"\n📊 Traitement de {rapport}...")
                analyseur.extraire_donnees_rapport(rapport, extraction)
            except FileNotFoundError:
                print(f"⚠️ Fichier {rapport} ou {extraction} non trouvé, ignoré")
            except Exception as e:
                print(f"⚠️ Erreur avec {rapport}: {e}")

        if analyseur.donnees_mains:
            stats = analyseur.analyser_classification_intensive()
            print(f"\n✅ ANALYSE TERMINÉE - {len(analyseur.donnees_mains)} mains analysées")
        else:
            print("❌ Aucune donnée consolidée pour l'analyse")

    except Exception as e:
        print(f"❌ Erreur lors de l'analyse : {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
