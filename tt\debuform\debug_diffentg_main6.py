#!/usr/bin/env python3
"""Debug spécifique de DiffEntG pour la Main 6"""

def debug_diffentg_main6():
    """Vérifie si DiffEntG pour Main 6 utilise bien Main 5 vs Main 6 simulée"""
    
    print("🔍 DEBUG DiffEntG POUR LA MAIN 6")
    print("=" * 50)
    
    # Simulation de la séquence jusqu'à Main 6
    sequence = ['0_A_BANKER', '1_B_PLAYER', '0_C_BANKER', '1_A_PLAYER', '0_B_BANKER', '1_C_PLAYER']
    mains = [1, 2, 3, 4, 5, 6]  # Numéros de mains
    
    print("📊 Séquence jusqu'à Main 6:")
    for i, (main, symbol) in enumerate(zip(mains, sequence)):
        print(f"   Position {i}: Main {main} = '{symbol}'")
    print()
    
    # ANALYSE POUR LE TABLEAU PRÉDICTIF MAIN 6
    position = 5  # Position de Main 6 (index 5)
    print(f"🎯 CALCUL DiffEntG POUR MAIN 6 (position {position})")
    print("-" * 50)
    
    # ÉTAPE 1: Métriques actuelles (Main 6)
    print("📋 ÉTAPE 1: MÉTRIQUES ACTUELLES")
    print(f"   Position analysée: {position} (Main {mains[position]})")
    
    # Dans l'évolution, pour position n, on utilise subsequence = sequence[:n]
    # Pour position 5 (Main 6), on utilise sequence[:5] = Mains 1-5
    current_subsequence = sequence[:position]  # Mains 1-5
    print(f"   Sous-séquence actuelle (pour Main {mains[position-1]}): {current_subsequence}")
    print(f"   Longueur: {len(current_subsequence)}")
    print(f"   → current_theoretical = AEP({current_subsequence})")
    print()
    
    # ÉTAPE 2: Simulation pour Main 7
    print("📋 ÉTAPE 2: SIMULATION POUR MAIN 7")
    simulated_value = '0_A_BANKER'  # Exemple de valeur simulée
    
    # Dans le calcul prédictif: simulated_sequence = sequence[:position+1] + [possible_index5]
    # Pour position 5: sequence[:6] + [simulé] = Mains 1-6 + Main 7 simulée
    simulated_sequence = sequence[:position+1] + [simulated_value]
    print(f"   Séquence simulée: {simulated_sequence}")
    print(f"   Longueur: {len(simulated_sequence)}")
    print(f"   → simulated_theoretical = AEP({simulated_sequence})")
    print()
    
    # ÉTAPE 3: Vérification de votre hypothèse
    print("🔍 VÉRIFICATION DE L'HYPOTHÈSE")
    print("-" * 40)
    print("VOTRE HYPOTHÈSE:")
    print("   (Entropie globale à la main n) = Entropie globale à la main 5")
    print("   (Entropie globale à la main n+1 simulée) = Entropie globale à la main 6 simulée")
    print()
    
    print("RÉALITÉ DANS LE CODE:")
    print(f"   current_theoretical utilise: {current_subsequence}")
    print(f"   → Entropie globale des Mains 1-{len(current_subsequence)} (pas Main 5 seule)")
    print()
    print(f"   simulated_theoretical utilise: {simulated_sequence}")
    print(f"   → Entropie globale des Mains 1-{len(simulated_sequence)} (Mains 1-6 + Main 7 simulée)")
    print()
    
    # ÉTAPE 4: Conclusion
    print("🎯 CONCLUSION")
    print("-" * 40)
    print("❌ VOTRE HYPOTHÈSE N'EST PAS CORRECTE")
    print()
    print("Le code calcule:")
    print("   current_theoretical = AEP(Mains 1 à 5)")
    print("   simulated_theoretical = AEP(Mains 1 à 6 + Main 7 simulée)")
    print()
    print("Donc DiffEntG pour Main 6 =")
    print("   |AEP(Mains 1-6 + Main 7 simulée) - AEP(Mains 1-5)|")
    print()
    print("Ce n'est PAS:")
    print("   |AEP(Main 6 simulée) - AEP(Main 5)|")
    print()
    print("Mais plutôt:")
    print("   |AEP(séquence étendue avec simulation) - AEP(séquence actuelle)|")

if __name__ == "__main__":
    debug_diffentg_main6()
