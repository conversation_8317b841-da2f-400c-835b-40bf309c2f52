"""
Diagnostic approfondi de PyJulia
"""

import sys
import os

print("=== DIAGNOSTIC COMPLET PYJULIA ===")

print("\n1. Configuration Python...")
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")

print("\n2. Test import julia...")
try:
    import julia
    print("OK: Module julia importe")
    print(f"Julia version: {julia.__version__}")
except Exception as e:
    print(f"ERREUR import julia: {e}")
    exit(1)

print("\n3. Configuration Julia...")
try:
    # Obtenir des informations sur Julia sans initialiser Main
    julia_info = julia.Julia(debug=True)
    print("OK: Julia info obtenue")
except Exception as e:
    print(f"ERREUR Julia info: {e}")

print("\n4. Test initialisation Julia avec debug...")
try:
    # Essayer d'initialiser Julia avec plus de debug
    print("Tentative d'initialisation Julia...")
    
    # Configurer l'environnement avant l'initialisation
    os.environ['JULIA_DEBUG'] = 'all'
    
    from julia import Main
    print("OK: Main initialise avec succes")
    
except Exception as e:
    print(f"ERREUR Main: {e}")
    
    # Analyser l'erreur en détail
    print("\nAnalyse detaillee de l'erreur:")
    print(f"Type d'erreur: {type(e)}")
    print(f"Message: {str(e)}")
    
    # Vérifier si c'est bien un problème PyCall
    if "PyCall" in str(e):
        print("CONFIRME: Probleme PyCall detecte")
        
        print("\n5. Diagnostic PyCall depuis Python...")
        
        # Essayer de diagnostiquer PyCall
        try:
            # Lancer Julia directement pour tester PyCall
            import subprocess
            result = subprocess.run(['julia', '-e', 'using PyCall; println("PyCall OK")'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("OK: PyCall fonctionne dans Julia standalone")
                print(f"Sortie: {result.stdout}")
            else:
                print("ERREUR: PyCall ne fonctionne pas dans Julia standalone")
                print(f"Erreur: {result.stderr}")
                
        except Exception as diag_e:
            print(f"ERREUR diagnostic PyCall: {diag_e}")

print("\n6. Variables d'environnement pertinentes...")
env_vars = ['JULIA_PYTHONCALL_EXE', 'PYTHON', 'JULIA_DEBUG', 'JULIA_LOAD_PATH']
for var in env_vars:
    value = os.environ.get(var, 'Non defini')
    print(f"{var}: {value}")

print("\n=== FIN DIAGNOSTIC ===")
