# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 1210 à 1235
# Type: Méthode de la classe INDEX5Calculator

    def calculate_context_predictability(self, sequence_history, current_metrics):
        """
        Calcule le niveau de prédictibilité contextuelle basé sur l'entropie conditionnelle
        et les patterns récents. Retourne un score de prédictibilité contextuelle.
        """
        if len(sequence_history) < 5:
            return 0.0

        # 1. Score basé sur l'entropie conditionnelle (plus faible = plus prévisible)
        # CORRECTION AEP: Nouvelle normalisation basée sur les vraies plages observées
        conditional_entropy = current_metrics.get('conditional_entropy', 6.2192)
        entropy_score = max(0.0, (6.2192 - conditional_entropy) / 6.2192)  # Normalisation corrigée

        # 2. Score basé sur la répétition des patterns récents
        recent_pattern = sequence_history[-5:]
        pattern_matches = self.count_pattern_occurrences(recent_pattern, sequence_history)
        pattern_score = min(pattern_matches / 10.0, 1.0)  # Normalisation sur 10 occurrences max

        # 3. Score basé sur le taux de répétition
        repetition_rate = current_metrics.get('repetition_rate', 0)
        repetition_score = min(repetition_rate * 5, 1.0)  # Normalisation

        # 4. Score composite pondéré
        context_predictability = (0.5 * entropy_score + 0.3 * pattern_score + 0.2 * repetition_score)

        return round(context_predictability, 4)