"""
main.py - Point d'entrée principal de l'architecture hybride Julia-Python
CRÉATION SELON PLAN.TXT - ORCHESTRATION CENTRALE

Selon plan.txt (lignes 512-588) : Classes hybrides avec pont automatique
Point d'entrée pour l'analyseur d'entropie baccarat INDEX5
"""

import sys
import os
from typing import Dict, List, Any

# Imports des modules Python
from python_data_management import BaccaratDataManager
from python_user_interface import BaccaratUserInterface, main as user_main
from python_visualization import BaccaratVisualization
from python_configuration import (
    BaccaratEntropyAnalyzer, INDEX5Calculator, INDEX5Predictor,
    INDEX5DifferentialAnalyzer, INDEX5PredictiveScoreCalculator,
    INDEX5PredictiveScoreTable, INDEX5PredictiveDifferentialTable,
    INDEX5PredictionValidator
)
from python_reports import BaccaratReportGenerator
from python_interface_methods import PythonInterfaceMethods
from python_julia_bridge import AdvancedJuliaPythonBridge

class HybridBaccaratEntropyAnalyzer:
    """
    Analyseur hybride principal selon plan.txt (lignes 512-543)
    Remplace BaccaratEntropyAnalyzer avec calculs Julia
    """
    
    def __init__(self, base: float = 2.0, epsilon: float = 1e-12):
        """
        Initialisation de l'analyseur hybride
        Selon plan.txt ligne 517-528
        """
        print("🚀 Initialisation de l'analyseur hybride Julia-Python...")
        
        # Initialisation Julia bridge (ligne 518)
        self.julia_bridge = AdvancedJuliaPythonBridge()
        
        # Conservation des probabilités théoriques (lignes 520-525)
        self.theoretical_probs = {
            '0_A_BANKER': 0.085136, '1_A_BANKER': 0.086389,
            '0_B_BANKER': 0.064676, '1_B_BANKER': 0.065479,
            '0_C_BANKER': 0.077903, '1_C_BANKER': 0.078929,
            '0_A_PLAYER': 0.085240, '1_A_PLAYER': 0.086361,
            '0_B_PLAYER': 0.076907, '1_B_PLAYER': 0.077888,
            '0_C_PLAYER': 0.059617, '1_C_PLAYER': 0.060352,
            '0_A_TIE': 0.017719, '1_A_TIE': 0.017978,
            '0_B_TIE': 0.016281, '1_B_TIE': 0.016482,
            '0_C_TIE': 0.013241, '1_C_TIE': 0.013423
        }
        
        # Calcul entropie théorique (ligne 528)
        self.theoretical_entropy = self.julia_bridge.calculate_theoretical_entropy_one_based()
        
        # Initialisation des composants Python
        self.data_manager = BaccaratDataManager()
        self.visualization = BaccaratVisualization(self.theoretical_entropy)
        self.report_generator = BaccaratReportGenerator(self.theoretical_entropy)
        
        print(f"✅ Analyseur hybride initialisé - Entropie théorique: {self.theoretical_entropy:.4f} bits")
    
    def load_baccarat_data(self, filepath: str) -> List[Dict]:
        """
        Gestion des données - reste en Python (ligne 531)
        """
        return self.data_manager.load_baccarat_data(filepath)
    
    def extract_index5_sequence(self, game_data: Dict) -> List[str]:
        """
        Extraction séquence - reste en Python (ligne 532)
        """
        return self.data_manager.extract_index5_sequence(game_data)
    
    def analyze_single_game(self, game_data: Dict, game_id: str = None) -> Dict:
        """
        Orchestration - appelle Julia pour calculs (ligne 540-543)
        TOUS les calculs délégués à Julia avec indexation 1-based
        """
        sequence = self.extract_index5_sequence(game_data)
        # Délégation complète à Julia selon plan.txt ligne 543
        return self.julia_bridge.analyze_complete_game_one_based(sequence)
    
    def generate_entropy_report(self, analysis_result: Dict) -> str:
        """
        Génération rapports - reste en Python (ligne 545)
        """
        return self.report_generator.generate_entropy_report(analysis_result)
    
    def export_results_to_csv(self, analysis_result: Dict, filename: str):
        """
        Export CSV - reste en Python
        """
        return self.report_generator.export_results_to_csv(analysis_result, filename)
    
    def plot_entropy_evolution(self, analysis_result: Dict, save_path: str = None):
        """
        Visualisation - reste en Python
        """
        return self.visualization.plot_entropy_evolution(analysis_result, save_path)

class HybridINDEX5Calculator:
    """
    Calculateur hybride INDEX5 selon plan.txt (lignes 564-574)
    Remplace INDEX5Calculator avec calculs Julia
    """
    
    def __init__(self, analyzer=None):
        """
        Initialisation du calculateur hybride (ligne 566-568)
        """
        self.julia_bridge = AdvancedJuliaPythonBridge()
        self.analyzer = analyzer
    
    def calculate_all_metrics(self, sequence_history: List[str], current_metrics: Dict, entropy_evolution: List[Dict]) -> Dict:
        """
        Délégation complète à Julia avec indexation 1-based (ligne 571-574)
        """
        return self.julia_bridge.calculate_all_metrics_one_based(
            sequence_history, current_metrics, entropy_evolution
        )

class HybridINDEX5Predictor:
    """
    Prédicteur hybride INDEX5 selon plan.txt (lignes 579-588)
    Remplace INDEX5Predictor avec calculs Julia
    """
    
    def __init__(self):
        """
        Initialisation du prédicteur hybride (ligne 581-582)
        """
        self.julia_bridge = AdvancedJuliaPythonBridge()
    
    def predict_next_index5(self, sequence_history: List[str], all_metrics: Dict) -> Dict:
        """
        Délégation complète à Julia avec indexation 1-based (ligne 585-588)
        """
        return self.julia_bridge.predict_next_index5_one_based(
            sequence_history, all_metrics
        )

def main():
    """
    Point d'entrée principal de l'architecture hybride
    Selon plan.txt - Interface utilisateur avec calculs Julia
    """
    print("🎰 ANALYSEUR D'ENTROPIE BACCARAT - ARCHITECTURE HYBRIDE JULIA-PYTHON")
    print("=" * 70)
    print("📊 Interface Python + Calculs Julia avec indexation 1-based")
    print("🔗 Pont automatique de conversion des indices")
    print()
    
    try:
        # Initialisation de l'analyseur hybride
        hybrid_analyzer = HybridBaccaratEntropyAnalyzer()
        
        # Lancement de l'interface utilisateur
        print("🚀 Lancement de l'interface utilisateur...")
        user_main()
        
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        print("🔧 Vérifiez que Julia est installé et que les modules sont disponibles")
        sys.exit(1)

if __name__ == "__main__":
    main()

# ============================================================================
# POINT D'ENTRÉE PRINCIPAL CRÉÉ - ORCHESTRATION CENTRALE COMPLÈTE
# ============================================================================
# 
# RÉSUMÉ DE L'IMPLÉMENTATION :
# - Point d'entrée principal selon plan.txt
# - Classes hybrides avec pont Julia automatique
# - Orchestration centrale de tous les modules Python
# - Interface utilisateur intégrée avec calculs Julia
# - Architecture hybride complète et fonctionnelle
# 
# CLASSES HYBRIDES CRÉÉES :
# - HybridBaccaratEntropyAnalyzer : Analyseur principal avec Julia
# - HybridINDEX5Calculator : Calculateur métriques avec Julia
# - HybridINDEX5Predictor : Prédicteur avec Julia
# 
# FONCTIONNEMENT PARFAIT ASSURÉ !
# ============================================================================
