# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2442 à 2452
# Type: Méthode de la classe INDEX5PredictiveScoreTable
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self):
        """Initialisation de la classe tableau prédictif avec scores"""
        self.all_index5_values = [
            "0_A_BANKER", "0_B_BANKER", "0_C_BANKER",
            "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER",
            "0_A_TIE", "0_B_TIE", "0_C_TIE",
            "1_A_BANKER", "1_B_BANKER", "1_C_BANKER",
            "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER",
            "1_A_TIE", "1_<PERSON>_TIE", "1_C_TIE"
        ]
        self.score_calculator = INDEX5PredictiveScoreCalculator()