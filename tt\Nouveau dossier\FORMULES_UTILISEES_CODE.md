# 🔧 FORMULES MATHÉMATIQUES UTILISÉES DANS LE CODE
## Analyse Détaillée des Implémentations

### 📁 Fichiers Analysés
- `predicteur_index5.py` - Prédicteur principal
- `entropie_baccarat_analyzer.py` - Analyseur d'entropie
- `entropie/cours_entropie/ressources/implementations_python.py` - Implémentations de référence

---

## 🎯 FORMULES PRINCIPALES IMPLÉMENTÉES

### 1. Entropie de Shannon
**Localisation** : `predicteur_index5.py:60-88`, `entropie_baccarat_analyzer.py:66-85`

**Formule mathématique** :
```
H(X) = -∑ p(x) log₂ p(x)
```

**Implémentation** :
```python
def _calculate_shannon_entropy(self, probabilities: List[float]) -> float:
    p = np.array(probabilities)
    p = np.where(p <= 0, epsilon, p)  # Gestion de log(0)
    log_p = np.log(p) / np.log(2.0)   # log base 2
    entropy_terms = p * log_p
    entropy_terms = np.where(p == epsilon, 0, entropy_terms)  # 0*log(0) = 0
    return -np.sum(entropy_terms)
```

**Paramètres utilisés** :
- `epsilon = 1e-12` (éviter log(0))
- Base logarithmique : 2 (résultats en bits)

### 2. Entropie Métrique Kolmogorov-Sinai
**Localisation** : `predicteur_index5.py:90-122`, `entropie_baccarat_analyzer.py:325-342`

**Formule mathématique** :
```
h_μ(T,α) = lim (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
           n→∞
```

**Implémentation** :
```python
def calculate_kolmogorov_sinai_entropy(self, sequence: List[str]) -> float:
    max_length = min(4, len(sequence))  # Limite pratique
    entropies = []
    
    for block_len in range(1, max_length + 1):
        # Extraire tous les blocs de longueur block_len
        blocks = {}
        for i in range(len(sequence) - block_len + 1):
            block = tuple(sequence[i:i+block_len])
            blocks[block] = blocks.get(block, 0) + 1
        
        # Calculer les probabilités des blocs
        total_blocks = sum(blocks.values())
        block_probs = [count / total_blocks for count in blocks.values()]
        
        # Entropie du bloc divisée par la longueur (entropie par symbole)
        block_entropy = self._calculate_shannon_entropy(block_probs)
        entropies.append(block_entropy / block_len)
    
    return entropies[-1] if entropies else 0.0  # Approximation de la limite
```

**Paramètres** :
- `max_length = 4` (longueur maximale des blocs)
- Approximation par la dernière valeur calculée

### 3. Entropie Conditionnelle
**Localisation** : `predicteur_index5.py:124-168`, `entropie_baccarat_analyzer.py:344-366`

**Formule mathématique** :
```
H(Y|X) = ∑ p(x) H(Y|X=x)
         x∈X
```

**Implémentation** :
```python
def calculate_conditional_entropy(self, sequence: List[str]) -> float:
    max_context = min(3, len(sequence) - 1)  # Contextes jusqu'à longueur 3
    context_transitions = {}
    
    # Compter les transitions contexte → symbole suivant
    for context_len in range(1, max_context + 1):
        for i in range(len(sequence) - context_len):
            context = tuple(sequence[i:i+context_len])
            next_symbol = sequence[i+context_len]
            
            if context not in context_transitions:
                context_transitions[context] = {}
            context_transitions[context][next_symbol] = \
                context_transitions[context].get(next_symbol, 0) + 1
    
    # Calculer H(X|Contexte) = Σ P(contexte) × H(X|contexte)
    total_transitions = sum(sum(transitions.values()) 
                           for transitions in context_transitions.values())
    conditional_entropy = 0.0
    
    for context, transitions in context_transitions.items():
        context_prob = sum(transitions.values()) / total_transitions
        symbol_probs = [count / sum(transitions.values()) 
                       for count in transitions.values()]
        context_entropy = self._calculate_shannon_entropy(symbol_probs)
        conditional_entropy += context_prob * context_entropy
    
    return conditional_entropy
```

**Paramètres** :
- `max_context = 3` (longueur maximale du contexte)
- Méthode de contextes variables

### 4. Complexité Lempel-Ziv
**Localisation** : `predicteur_index5.py:170-208`, `entropie_baccarat_analyzer.py:425-460`

**Algorithme LZ77** :
```python
def calculate_lempel_ziv_complexity(self, sequence: List[str]) -> int:
    if not sequence:
        return 0
    
    complexity = 1  # Premier symbole
    i = 1
    
    while i < len(sequence):
        # Recherche du plus long préfixe déjà vu
        max_match_length = 0
        for j in range(i):
            match_length = 0
            while (i + match_length < len(sequence) and 
                   j + match_length < i and
                   sequence[i + match_length] == sequence[j + match_length]):
                match_length += 1
            max_match_length = max(max_match_length, match_length)
        
        # Avancer d'au moins 1 position
        i += max(1, max_match_length)
        complexity += 1
    
    return complexity
```

**Principe** : Compte le nombre de nouveaux motifs distincts dans la séquence

### 5. Conversion Entropie → Prédictibilité
**Localisation** : `predicteur_index5.py:477-493`

**Formule de conversion** :
```python
def get_prediction_confidence(self, conditional_entropy: float) -> float:
    max_entropy = 3.9297  # Entropie théorique INDEX5 réelle
    
    if conditional_entropy <= 0:
        return 1.0  # Confiance maximale
    if conditional_entropy >= max_entropy:
        return 0.0  # Confiance minimale
    
    # Conversion linéaire
    confidence = 1.0 - (conditional_entropy / max_entropy)
    
    # Ajustement vers plage observée (80-82.5%)
    confidence = 0.80 + (confidence * 0.05)
    
    return confidence
```

**Paramètres critiques** :
- `max_entropy = 3.9297` bits (entropie théorique INDEX5)
- Plage de confiance : 80% à 85%

---

## 🔢 CONSTANTES MATHÉMATIQUES UTILISÉES

### Entropie Théorique INDEX5
```python
# Probabilités théoriques (non-uniformes)
THEORETICAL_PROBS = {
    '0_A_BANKER': 8.5136, '0_B_BANKER': 7.6907, '0_C_BANKER': 7.7903,
    '1_A_BANKER': 8.6389, '1_B_BANKER': 6.5479, '1_C_BANKER': 7.8929,
    '0_A_PLAYER': 8.5240, '0_B_PLAYER': 7.6907, '0_C_PLAYER': 5.9617,
    '1_A_PLAYER': 8.6361, '1_B_PLAYER': 7.7888, '1_C_PLAYER': 6.0352,
    '0_A_TIE': 1.7719, '0_B_TIE': 1.6281, '0_C_TIE': 1.3241,
    '1_A_TIE': 1.7978, '1_B_TIE': 1.6482, '1_C_TIE': 1.3423
}

# Entropie calculée : 3.9297 bits (≠ log₂(18) = 4.1699 bits)
MAX_THEORETICAL_ENTROPY = 3.9297
```

### Signature Entropique Universelle
```python
TARGET_METRIC_ENTROPY = 1.456  # ±0.004 bits
ENTROPY_TOLERANCE = 0.004       # Déviation maximale
CV_THRESHOLD = 0.0026          # Coefficient variation (0.26%)
OPTIMAL_LZ_COMPLEXITY = 35.0   # ±1.8 (58-62% compression)
```

### Phases Temporelles
```python
EXPECTED_ENTROPY_RANGES = {
    15: (0.890, 0.902),   # Fin Phase 1 (INITIALISATION)
    30: (1.184, 1.194),   # Fin Phase 2 (EXPANSION)  
    60: (1.452, 1.460)    # Fin Phase 3 (CONVERGENCE)
}
```

### Profils Comportementaux
```python
GAME_TYPE_PROFILES = {
    "TYPE_A": {  # 50% des parties
        "predictability": 0.815,      # 81.5%
        "entropy_target": 1.4582,
        "diversity_range": (0.83, 0.84),
        "lz_complexity": 36
    },
    "TYPE_B": {  # 25% des parties  
        "predictability": 0.80,       # 80.0%
        "entropy_target": 1.4495,
        "diversity_range": (0.77, 0.78),
        "lz_complexity": 34
    },
    "TYPE_C": {  # 25% des parties
        "predictability": 0.825,      # 82.5%
        "entropy_target": 1.4582,
        "diversity_range": (0.94, 0.95),
        "lz_complexity": 37
    }
}
```

---

## ⚙️ PARAMÈTRES TECHNIQUES

### Gestion Numérique
- `epsilon = 1e-12` (éviter log(0))
- `base = 2.0` (logarithme base 2)
- Normalisation automatique des probabilités

### Limites Algorithmiques
- `max_block_length = 4` (entropie métrique)
- `max_context = 3` (entropie conditionnelle)
- Approximation par valeurs finies des limites infinies

### Validation
- Vérification des identités mathématiques
- Gestion des cas limites (séquences courtes)
- Correction de biais pour estimations empiriques

---

*Analyse complète des 803 lignes de code du prédicteur et 878 lignes de l'analyseur*
