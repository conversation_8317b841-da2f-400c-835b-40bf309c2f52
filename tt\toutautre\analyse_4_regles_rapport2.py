#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR 13 RÈGLES DE PRÉDICTION INDEX3 - RAPPORT(NUMPARTIE).TXT
=================================================================

Teste 13 règles de prédiction sur les ratios DiffT/DiffEntG
pour n'importe quel rapport(numpartie).txt
"""

import re
import numpy as np
import sys
from collections import defaultdict

class AnalyseurQuatreRegles:
    def __init__(self, numero_rapport):
        self.numero_rapport = numero_rapport
        self.donnees_mains = {}
        self.resultats_regles = {
            'MOYENNE_BANKER_VS_PLAYER': {'correct': 0, 'total': 0, 'details': []},
            'MAX_BANKER_VS_PLAYER': {'correct': 0, 'total': 0, 'details': []},
            'SOMME_BANKER_VS_PLAYER': {'correct': 0, 'total': 0, 'details': []},
            'MEDIAN_BANKER_VS_PLAYER': {'correct': 0, 'total': 0, 'details': []},
            'MOYENNE_AVEC_FILTRE_ECART_TYPE': {'correct': 0, 'total': 0, 'details': [], 'exclus': 0},
            'MEDIANE_AVEC_FILTRE_ECART_TYPE': {'correct': 0, 'total': 0, 'details': [], 'exclus': 0},
            'THEIL_BANKER_VS_PLAYER': {'correct': 0, 'total': 0, 'details': []},
            'GINI_BANKER_VS_PLAYER': {'correct': 0, 'total': 0, 'details': []},
            'CV_BANKER_VS_PLAYER': {'correct': 0, 'total': 0, 'details': []},
            'THEIL_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE': {'correct': 0, 'total': 0, 'details': [], 'exclus': 0},
            'GINI_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE': {'correct': 0, 'total': 0, 'details': [], 'exclus': 0},
            'CV_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE': {'correct': 0, 'total': 0, 'details': [], 'exclus': 0},
            'DOMINANCE_RATIO_MAX_BANKER_VS_PLAYER': {'correct': 0, 'total': 0, 'details': []}
        }
        
        # INDEX5 organisés par INDEX3
        self.index5_par_index3 = {
            'BANKER': ['0_A_BANKER', '0_B_BANKER', '0_C_BANKER', '1_A_BANKER', '1_B_BANKER', '1_C_BANKER'],
            'PLAYER': ['0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER', '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER']
        }
        
        print("🎯 ANALYSEUR 13 RÈGLES DE PRÉDICTION INDEX3")
        print(f"📊 Fichier source : ratios_difft_diffentg_rapport{numero_rapport}.txt")
    
    def charger_donnees(self, fichier_path):
        """Charge les données du fichier ratios"""
        print(f"\n📂 Chargement : {fichier_path}")
        
        with open(fichier_path, 'r', encoding='utf-8') as f:
            lignes = f.readlines()
        
        for ligne in lignes:
            if ligne.startswith('Main '):
                self._parser_ligne_main(ligne.strip())
        
        print(f"✅ {len(self.donnees_mains)} mains chargées")
    
    def _parser_ligne_main(self, ligne):
        """Parse une ligne : Main X: [ratios] | OBSERVÉ: INDEX5"""
        # Extraction numéro de main
        match_main = re.match(r'Main (\d+):', ligne)
        if not match_main:
            return
        
        main = int(match_main.group(1))
        
        # Extraction INDEX5 observé
        match_observe = re.search(r'OBSERVÉ:\s*([^\s]+)', ligne)
        if not match_observe:
            return
        
        index5_observe = match_observe.group(1)
        
        # Extraction INDEX3 de l'INDEX5 observé
        if '_BANKER' in index5_observe:
            index3_observe = 'BANKER'
        elif '_PLAYER' in index5_observe:
            index3_observe = 'PLAYER'
        elif '_TIE' in index5_observe:
            index3_observe = 'TIE'
        else:
            return
        
        # Extraction des ratios
        partie_ratios = ligne.split('|')[0].replace(f'Main {main}:', '').strip()
        valeurs = partie_ratios.split()
        
        # Ordre des INDEX5 dans le fichier
        index5_ordre = [
            '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', 
            '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER',
            '1_A_BANKER', '1_B_BANKER', '1_C_BANKER', 
            '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER'
        ]
        
        # Conversion en float
        ratios = {}
        for i, val in enumerate(valeurs):
            if i < len(index5_ordre):
                if val != 'N/A':
                    try:
                        ratios[index5_ordre[i]] = float(val)
                    except ValueError:
                        pass
        
        # Stockage
        self.donnees_mains[main] = {
            'index3_observe': index3_observe,
            'index5_observe': index5_observe,
            'ratios': ratios
        }
    
    def analyser_quatre_regles(self):
        """Teste les 13 règles de prédiction INDEX3"""
        print("\n🔬 ANALYSE DES 13 RÈGLES")
        print("=" * 50)

        regles = [
            'MOYENNE_BANKER_VS_PLAYER',
            'MAX_BANKER_VS_PLAYER',
            'SOMME_BANKER_VS_PLAYER',
            'MEDIAN_BANKER_VS_PLAYER',
            'MOYENNE_AVEC_FILTRE_ECART_TYPE',
            'MEDIANE_AVEC_FILTRE_ECART_TYPE',
            'THEIL_BANKER_VS_PLAYER',
            'GINI_BANKER_VS_PLAYER',
            'CV_BANKER_VS_PLAYER',
            'THEIL_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE',
            'GINI_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE',
            'CV_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE',
            'DOMINANCE_RATIO_MAX_BANKER_VS_PLAYER'
        ]
        
        for regle in regles:
            print(f"\n🎯 Test règle : {regle}")
            self._tester_regle(regle)
        
        # Affichage des résultats
        self._afficher_resultats()
    
    def _tester_regle(self, regle):
        """Teste une règle spécifique sur toutes les mains"""
        for main, donnees in self.donnees_mains.items():
            index3_observe = donnees['index3_observe']
            ratios = donnees['ratios']
            
            # Ignorer les TIE (pas de ratios correspondants)
            if index3_observe == 'TIE':
                continue
            
            # Séparer les ratios BANKER et PLAYER
            ratios_banker = []
            ratios_player = []
            
            for index5, ratio in ratios.items():
                if 'BANKER' in index5:
                    ratios_banker.append(ratio)
                elif 'PLAYER' in index5:
                    ratios_player.append(ratio)
            
            # Vérifier qu'on a des ratios pour les deux
            if not ratios_banker or not ratios_player:
                continue
            
            # Appliquer la règle
            resultat = self._appliquer_regle(ratios_banker, ratios_player, regle)

            # Gestion spéciale pour les nouvelles règles avec filtre
            if regle in ['MOYENNE_AVEC_FILTRE_ECART_TYPE', 'MEDIANE_AVEC_FILTRE_ECART_TYPE',
                        'THEIL_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE',
                        'GINI_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE',
                        'CV_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE']:
                if resultat is None:  # Prédiction exclue par le filtre
                    self.resultats_regles[regle]['exclus'] += 1
                    continue
                else:
                    index3_predit = resultat
            else:
                index3_predit = resultat

            # Enregistrer le résultat
            self.resultats_regles[regle]['total'] += 1

            if index3_predit == index3_observe:
                self.resultats_regles[regle]['correct'] += 1
                statut = "✅"
            else:
                statut = "❌"

            # Détails pour debug
            self.resultats_regles[regle]['details'].append({
                'main': main,
                'observe': index3_observe,
                'predit': index3_predit,
                'statut': statut,
                'ratios_banker': ratios_banker,
                'ratios_player': ratios_player
            })
    
    def _appliquer_regle(self, ratios_banker, ratios_player, regle):
        """Applique une règle de prédiction"""
        if regle == 'MOYENNE_BANKER_VS_PLAYER':
            moy_banker = np.mean(ratios_banker)
            moy_player = np.mean(ratios_player)
            return 'BANKER' if moy_banker < moy_player else 'PLAYER'
        
        elif regle == 'MAX_BANKER_VS_PLAYER':
            max_banker = max(ratios_banker)
            max_player = max(ratios_player)
            return 'BANKER' if max_banker < max_player else 'PLAYER'
        
        elif regle == 'SOMME_BANKER_VS_PLAYER':
            sum_banker = sum(ratios_banker)
            sum_player = sum(ratios_player)
            return 'BANKER' if sum_banker < sum_player else 'PLAYER'
        
        elif regle == 'MEDIAN_BANKER_VS_PLAYER':
            med_banker = np.median(ratios_banker)
            med_player = np.median(ratios_player)
            return 'BANKER' if med_banker < med_player else 'PLAYER'

        elif regle == 'MOYENNE_AVEC_FILTRE_ECART_TYPE':
            # Calcul des moyennes
            moy_banker = np.mean(ratios_banker)
            moy_player = np.mean(ratios_player)

            # Déterminer quel groupe a la moyenne la plus faible (sera prédit)
            if moy_banker < moy_player:
                # On va prédire BANKER, donc vérifier l'écart-type des ratios BANKER
                if len(ratios_banker) > 1:
                    std_banker = np.std(ratios_banker)
                    # Vérifier que tous les ratios BANKER sont dans [moyenne ± 1.7σ]
                    for ratio in ratios_banker:
                        if abs(ratio - moy_banker) > 1.7 * std_banker:
                            return None  # Un ratio BANKER s'écarte trop
                return 'BANKER'
            else:
                # On va prédire PLAYER, donc vérifier l'écart-type des ratios PLAYER
                if len(ratios_player) > 1:
                    std_player = np.std(ratios_player)
                    # Vérifier que tous les ratios PLAYER sont dans [moyenne ± 1.7σ]
                    for ratio in ratios_player:
                        if abs(ratio - moy_player) > 1.7 * std_player:
                            return None  # Un ratio PLAYER s'écarte trop
                return 'PLAYER'

        elif regle == 'MEDIANE_AVEC_FILTRE_ECART_TYPE':
            # Calcul des médianes
            med_banker = np.median(ratios_banker)
            med_player = np.median(ratios_player)

            # Déterminer quel groupe a la médiane la plus faible (sera prédit)
            if med_banker < med_player:
                # On va prédire BANKER, donc vérifier l'écart-type des ratios BANKER
                if len(ratios_banker) > 1:
                    std_banker = np.std(ratios_banker)
                    # Vérifier que tous les ratios BANKER sont dans [médiane ± 1.7σ]
                    for ratio in ratios_banker:
                        if abs(ratio - med_banker) > 1.7 * std_banker:
                            return None  # Un ratio BANKER s'écarte trop
                return 'BANKER'
            else:
                # On va prédire PLAYER, donc vérifier l'écart-type des ratios PLAYER
                if len(ratios_player) > 1:
                    std_player = np.std(ratios_player)
                    # Vérifier que tous les ratios PLAYER sont dans [médiane ± 1.7σ]
                    for ratio in ratios_player:
                        if abs(ratio - med_player) > 1.7 * std_player:
                            return None  # Un ratio PLAYER s'écarte trop
                return 'PLAYER'

        elif regle == 'THEIL_BANKER_VS_PLAYER':
            # Calcul de l'indice de Theil avec μ = moyenne arithmétique
            def indice_theil(ratios):
                # μ = moyenne arithmétique = (ratio1 + ratio2 + ratio3) / 3
                mu = np.mean(ratios)

                # Calcul de l'indice de Theil : T = Σ(xi/μ) × ln(xi/μ) / n
                theil = 0
                for xi in ratios:
                    if xi > 0 and mu > 0:  # Éviter log(0)
                        ratio = xi / mu
                        theil += (ratio * np.log(ratio))

                theil = theil / len(ratios)  # Division par n (nombre d'observations)
                return theil

            theil_banker = indice_theil(ratios_banker)
            theil_player = indice_theil(ratios_player)

            # Prédire le groupe avec le Theil le plus élevé (plus d'inégalité)
            return 'BANKER' if theil_banker > theil_player else 'PLAYER'

        elif regle == 'GINI_BANKER_VS_PLAYER':
            # Calcul du coefficient de Gini pour chaque groupe
            def coefficient_gini(ratios):
                # Trier par ordre croissant
                x = np.sort(ratios)
                n = 3  # Toujours 3 observations par groupe

                # Σᵢ₌₁ⁿ Σⱼ₌₁ⁱ xⱼ = Somme des sommes cumulatives
                cumsum_values = np.cumsum(x)
                sum_of_cumsums = np.sum(cumsum_values)

                # Σᵢ₌₁ⁿ xᵢ = Somme totale
                total_sum = np.sum(x)

                # G = 1 - (2/(n+1)) × (Σᵢ₌₁ⁿ Σⱼ₌₁ⁱ xⱼ) × (Σᵢ₌₁ⁿ xᵢ)⁻¹
                gini = 1 - (2/(n+1)) * sum_of_cumsums / total_sum

                return gini

            gini_banker = coefficient_gini(ratios_banker)
            gini_player = coefficient_gini(ratios_player)

            # Prédire le groupe avec le Gini le plus faible (plus proche de 0, plus d'égalité)
            return 'BANKER' if gini_banker < gini_player else 'PLAYER'

        elif regle == 'CV_BANKER_VS_PLAYER':
            # Calcul du coefficient de variation avec μ = moyenne arithmétique
            def coefficient_variation(ratios):
                # σ = écart-type des 3 ratios du groupe
                sigma = np.std(ratios)

                # μ = moyenne arithmétique = (ratio1 + ratio2 + ratio3) / 3
                mu = np.mean(ratios)

                # CV = (σ / μ) × 100
                if mu != 0:  # Éviter division par zéro
                    cv = (sigma / mu) * 100
                else:
                    cv = 0

                return cv

            cv_banker = coefficient_variation(ratios_banker)
            cv_player = coefficient_variation(ratios_player)

            # Prédire le groupe avec le CV le plus élevé (plus de micro-variations relatives)
            return 'BANKER' if cv_banker > cv_player else 'PLAYER'

        elif regle == 'THEIL_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE':
            # Calcul de l'indice de Theil avec μ = moyenne arithmétique (même logique que THEIL_BANKER_VS_PLAYER)
            def indice_theil(ratios):
                # μ = moyenne arithmétique = (ratio1 + ratio2 + ratio3) / 3
                mu = np.mean(ratios)

                # Calcul de l'indice de Theil : T = Σ(xi/μ) × ln(xi/μ) / n
                theil = 0
                for xi in ratios:
                    if xi > 0 and mu > 0:  # Éviter log(0)
                        ratio = xi / mu
                        theil += (ratio * np.log(ratio))

                theil = theil / len(ratios)  # Division par n (nombre d'observations)
                return theil

            theil_banker = indice_theil(ratios_banker)
            theil_player = indice_theil(ratios_player)

            # Déterminer quel groupe sera prédit
            if theil_banker > theil_player:
                # On va prédire BANKER, donc vérifier l'écart-type des ratios BANKER
                if len(ratios_banker) > 1:
                    mean_banker = np.mean(ratios_banker)
                    std_banker = np.std(ratios_banker)
                    for ratio in ratios_banker:
                        if abs(ratio - mean_banker) > 1.3 * std_banker:
                            return None  # Un ratio BANKER s'écarte trop
                return 'BANKER'
            else:
                # On va prédire PLAYER, donc vérifier l'écart-type des ratios PLAYER
                if len(ratios_player) > 1:
                    mean_player = np.mean(ratios_player)
                    std_player = np.std(ratios_player)
                    for ratio in ratios_player:
                        if abs(ratio - mean_player) > 1.3 * std_player:
                            return None  # Un ratio PLAYER s'écarte trop
                return 'PLAYER'

        elif regle == 'GINI_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE':
            # Calcul du coefficient de Gini pour chaque groupe (même logique que GINI_BANKER_VS_PLAYER)
            def coefficient_gini(ratios):
                x = np.sort(ratios)
                n = 3
                cumsum_values = np.cumsum(x)
                sum_of_cumsums = np.sum(cumsum_values)
                total_sum = np.sum(x)
                gini = 1 - (2/(n+1)) * sum_of_cumsums / total_sum
                return gini

            gini_banker = coefficient_gini(ratios_banker)
            gini_player = coefficient_gini(ratios_player)

            # Déterminer quel groupe sera prédit (Gini le plus faible)
            if gini_banker < gini_player:
                # On va prédire BANKER, donc vérifier l'écart-type des ratios BANKER
                if len(ratios_banker) > 1:
                    mean_banker = np.mean(ratios_banker)
                    std_banker = np.std(ratios_banker)
                    for ratio in ratios_banker:
                        if abs(ratio - mean_banker) > 1.3 * std_banker:
                            return None  # Un ratio BANKER s'écarte trop
                return 'BANKER'
            else:
                # On va prédire PLAYER, donc vérifier l'écart-type des ratios PLAYER
                if len(ratios_player) > 1:
                    mean_player = np.mean(ratios_player)
                    std_player = np.std(ratios_player)
                    for ratio in ratios_player:
                        if abs(ratio - mean_player) > 1.3 * std_player:
                            return None  # Un ratio PLAYER s'écarte trop
                return 'PLAYER'

        elif regle == 'CV_BANKER_VS_PLAYER_AVEC_FILTRE_ECART_TYPE':
            # Calcul du coefficient de variation avec μ = moyenne arithmétique (même logique que CV_BANKER_VS_PLAYER)
            def coefficient_variation(ratios):
                # σ = écart-type des 3 ratios du groupe
                sigma = np.std(ratios)

                # μ = moyenne arithmétique = (ratio1 + ratio2 + ratio3) / 3
                mu = np.mean(ratios)

                # CV = (σ / μ) × 100
                if mu != 0:  # Éviter division par zéro
                    cv = (sigma / mu) * 100
                else:
                    cv = 0

                return cv

            cv_banker = coefficient_variation(ratios_banker)
            cv_player = coefficient_variation(ratios_player)

            # Déterminer quel groupe sera prédit (CV le plus élevé)
            if cv_banker > cv_player:
                # On va prédire BANKER, donc vérifier l'écart-type des ratios BANKER
                if len(ratios_banker) > 1:
                    mean_banker = np.mean(ratios_banker)
                    std_banker = np.std(ratios_banker)
                    for ratio in ratios_banker:
                        if abs(ratio - mean_banker) > 1.3 * std_banker:
                            return None  # Un ratio BANKER s'écarte trop
                return 'BANKER'
            else:
                # On va prédire PLAYER, donc vérifier l'écart-type des ratios PLAYER
                if len(ratios_player) > 1:
                    mean_player = np.mean(ratios_player)
                    std_player = np.std(ratios_player)
                    for ratio in ratios_player:
                        if abs(ratio - mean_player) > 1.3 * std_player:
                            return None  # Un ratio PLAYER s'écarte trop
                return 'PLAYER'

        elif regle == 'DOMINANCE_RATIO_MAX_BANKER_VS_PLAYER':
            # Calcul de la dominance du ratio max pour chaque groupe
            def dominance_ratio_max(ratios):
                # Trier les ratios par ordre décroissant pour identifier le max, 2ème et 3ème
                ratios_sorted = sorted(ratios, reverse=True)
                ratio_max = ratios_sorted[0]
                ratio_2 = ratios_sorted[1] if len(ratios_sorted) > 1 else 0
                ratio_3 = ratios_sorted[2] if len(ratios_sorted) > 2 else 0

                # Formule : |ratio_max - ratio_2 - ratio_3| + 1e-10) / somme_ratios
                numerateur = abs(ratio_max - ratio_2 - ratio_3) + 1e-10
                denominateur = sum(ratios)

                dominance = numerateur / denominateur
                return dominance

            dominance_banker = dominance_ratio_max(ratios_banker)
            dominance_player = dominance_ratio_max(ratios_player)

            # Prédire le groupe avec la dominance la plus élevée
            return 'BANKER' if dominance_banker > dominance_player else 'PLAYER'

        return None
    
    def _afficher_resultats(self):
        """Affiche les résultats de toutes les règles"""
        print("\n📊 RÉSULTATS FINAUX - PRÉDICTION INDEX3")
        print("=" * 60)
        
        # Tri par précision décroissante
        resultats_tries = []
        for regle, stats in self.resultats_regles.items():
            if stats['total'] > 0:
                precision = stats['correct'] / stats['total']
                resultat = {
                    'regle': regle,
                    'precision': precision,
                    'correct': stats['correct'],
                    'total': stats['total']
                }
                if 'exclus' in stats:
                    resultat['exclus'] = stats['exclus']
                resultats_tries.append(resultat)
        
        resultats_tries.sort(key=lambda x: x['precision'], reverse=True)
        
        print(f"{'RÈGLE':<25} {'PRÉCISION':<12} {'SUCCÈS':<12} {'EXCLUS':<8} {'STATUT':<15}")
        print("-" * 80)

        for resultat in resultats_tries:
            precision_pct = resultat['precision'] * 100
            statut = "🎯 SUPÉRIEUR" if precision_pct > 50 else "⚠️ INFÉRIEUR"
            exclus_info = f"{resultat.get('exclus', 0):>3}" if 'exclus' in resultat else "  -"

            print(f"{resultat['regle']:<25} {precision_pct:>8.2f}%    {resultat['correct']:>3}/{resultat['total']:<3}      {exclus_info:<8} {statut}")
        
        # Meilleure règle
        if resultats_tries:
            meilleure = resultats_tries[0]
            print(f"\n🏆 MEILLEURE RÈGLE : {meilleure['regle']}")
            print(f"   📊 Précision : {meilleure['precision']*100:.2f}%")
            print(f"   ✅ Succès : {meilleure['correct']}/{meilleure['total']}")
            
            if meilleure['precision'] > 0.5:
                print(f"   🎯 STATUT : SUPÉRIEUR AU HASARD (+{(meilleure['precision']-0.5)*100:.2f}%)")
            else:
                print(f"   ⚠️ STATUT : INFÉRIEUR AU HASARD ({(meilleure['precision']-0.5)*100:.2f}%)")
    
    def afficher_details_par_main(self):
        """Affiche les détails main par main pour la meilleure règle"""
        # Trouver la meilleure règle
        meilleure_regle = None
        meilleure_precision = 0
        
        for regle, stats in self.resultats_regles.items():
            if stats['total'] > 0:
                precision = stats['correct'] / stats['total']
                if precision > meilleure_precision:
                    meilleure_precision = precision
                    meilleure_regle = regle
        
        if meilleure_regle:
            print(f"\n🔍 DÉTAILS MAIN PAR MAIN : {meilleure_regle}")
            print("=" * 60)
            
            details = self.resultats_regles[meilleure_regle]['details']
            
            print(f"{'Main':<6} {'Observé':<8} {'Prédit':<8} {'Statut':<6} {'Ratios BANKER':<20} {'Ratios PLAYER':<20}")
            print("-" * 80)
            
            for detail in details:
                ratios_b_str = f"{detail['ratios_banker'][:3]}"[:18] + ".." if len(str(detail['ratios_banker'])) > 18 else str(detail['ratios_banker'])
                ratios_p_str = f"{detail['ratios_player'][:3]}"[:18] + ".." if len(str(detail['ratios_player'])) > 18 else str(detail['ratios_player'])
                
                print(f"{detail['main']:<6} {detail['observe']:<8} {detail['predit']:<8} {detail['statut']:<6} {ratios_b_str:<20} {ratios_p_str:<20}")

def main():
    """Fonction principale"""
    # Vérification des arguments
    if len(sys.argv) != 2:
        print("❌ USAGE: python analyse_4_regles_rapport2.py <numero_rapport>")
        print("📝 EXEMPLE: python analyse_4_regles_rapport2.py 6")
        print("📁 Cela analysera ratios_difft_diffentg_rapport6.txt")
        sys.exit(1)

    try:
        numero_rapport = int(sys.argv[1])
    except ValueError:
        print("❌ ERREUR: Le numéro de rapport doit être un entier")
        sys.exit(1)

    print(f"🎯 ANALYSE 13 RÈGLES DE PRÉDICTION INDEX3 - RAPPORT{numero_rapport}.TXT")
    print("=" * 60)

    # Nom du fichier à analyser
    fichier_ratios = f'ratios_difft_diffentg_rapport{numero_rapport}.txt'

    # Vérification de l'existence du fichier
    try:
        with open(fichier_ratios, 'r', encoding='utf-8') as f:
            pass
    except FileNotFoundError:
        print(f"❌ ERREUR: Le fichier {fichier_ratios} n'existe pas")
        print(f"💡 CONSEIL: Générez d'abord le fichier avec:")
        print(f"   python generateur_ratios_rapport.py {numero_rapport}")
        sys.exit(1)

    analyseur = AnalyseurQuatreRegles(numero_rapport)

    # Chargement des données
    analyseur.charger_donnees(fichier_ratios)

    # Test des 13 règles
    analyseur.analyser_quatre_regles()

    # Détails main par main
    analyseur.afficher_details_par_main()

    print("\n🎯 ANALYSE TERMINÉE")

if __name__ == "__main__":
    main()
