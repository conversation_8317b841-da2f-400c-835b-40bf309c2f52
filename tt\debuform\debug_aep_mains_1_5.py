#!/usr/bin/env python3
"""Vérification si AEP(Mains 1-5) est calculée pour le tableau prédictif Main 6"""

def debug_aep_mains_1_5():
    """Vérifie comment current_theoretical est obtenu pour position 5 (Main 6)"""
    
    print("🔍 VÉRIFICATION AEP(Mains 1-5) POUR TABLEAU PRÉDICTIF MAIN 6")
    print("=" * 60)
    
    # Simulation de la séquence
    sequence = ['Main1', 'Main2', 'Main3', 'Main4', 'Main5', 'Main6']
    
    print("📊 Séquence complète:")
    for i, main in enumerate(sequence):
        print(f"   Position {i}: {main}")
    print()
    
    # ANALYSE POUR POSITION 5 (MAIN 6)
    position = 5  # Position de Main 6
    print(f"🎯 ANALYSE POUR POSITION {position} (MAIN 6)")
    print("-" * 50)
    
    # ÉTAPE 1: Comment evolution[position] est créé
    print("📋 ÉTAPE 1: CRÉATION DE evolution[position]")
    print("Dans calculate_block_entropy_evolution():")
    print("   for n in range(1, len(sequence) + 1):")
    print("       subsequence = sequence[:n]")
    print("       simple_entropy_theoretical = self._calculate_sequence_entropy_aep(subsequence)")
    print()
    
    # Pour position 5, on regarde evolution[5]
    # evolution[5] correspond à n=6 dans la boucle (car n va de 1 à len(sequence)+1)
    # Donc subsequence = sequence[:6] = Mains 1-6
    evolution_position = 5
    n_in_loop = evolution_position + 1  # n = 6
    subsequence_for_evolution = sequence[:n_in_loop]  # sequence[:6]
    
    print(f"📊 Pour evolution[{evolution_position}] (Main 6):")
    print(f"   n dans la boucle = {n_in_loop}")
    print(f"   subsequence = sequence[:{n_in_loop}] = {subsequence_for_evolution}")
    print(f"   simple_entropy_theoretical = AEP({subsequence_for_evolution})")
    print()
    
    # ÉTAPE 2: Récupération dans calculate_predictive_differentials
    print("📋 ÉTAPE 2: RÉCUPÉRATION DANS calculate_predictive_differentials")
    print("   current_metrics = evolution[position]  # evolution[5]")
    print("   current_theoretical = current_metrics.get('simple_entropy_theoretical', 0.0)")
    print()
    print(f"   → current_theoretical = AEP({subsequence_for_evolution})")
    print(f"   → current_theoretical = AEP(Mains 1-6) ❌")
    print()
    
    # ÉTAPE 3: Problème identifié
    print("🚨 PROBLÈME IDENTIFIÉ")
    print("-" * 40)
    print("POUR LE TABLEAU PRÉDICTIF DE MAIN 6:")
    print("   On veut: current_theoretical = AEP(Mains 1-5)")
    print("   On obtient: current_theoretical = AEP(Mains 1-6)")
    print()
    print("CAUSE:")
    print("   evolution[5] contient les métriques calculées sur sequence[:6]")
    print("   Mais pour prédire Main 6, on devrait utiliser sequence[:5]")
    print()
    
    # ÉTAPE 4: Solution
    print("✅ SOLUTION")
    print("-" * 40)
    print("Pour corriger, il faudrait:")
    print("   current_theoretical = evolution[position-1].get('simple_entropy_theoretical', 0.0)")
    print("   Ou recalculer directement:")
    print("   current_theoretical = analyzer._calculate_sequence_entropy_aep(sequence[:position])")
    print()
    print(f"   → current_theoretical = AEP(sequence[:{position}])")
    print(f"   → current_theoretical = AEP({sequence[:position]})")
    print(f"   → current_theoretical = AEP(Mains 1-5) ✅")

if __name__ == "__main__":
    debug_aep_mains_1_5()
