# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2040 à 2047
# Type: Méthode de la classe INDEX5Predictor

    def filter_prediction_by_constraint(self, prediction, valid_values):
        """
        Filtre une prédiction selon les contraintes INDEX1
        Retourne la prédiction si valide, None sinon
        """
        if prediction and prediction in valid_values:
            return prediction
        return None