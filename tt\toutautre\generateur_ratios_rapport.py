#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GÉNÉRATEUR DE RATIOS DiffT/DiffEntG POUR RAPPORT(NUMPARTIE).TXT
==============================================================

Reproduit exactement la même structure que ratios_difft_diffentg_manuel.txt
mais pour les tableaux prédictifs de n'importe quel rapport(numpartie).txt
"""

import re
import numpy as np
import sys

class GenerateurRatiosRapport:
    def __init__(self, numero_rapport):
        self.numero_rapport = numero_rapport
        self.donnees_differentiels = {}
        self.index5_observes = {}
        self.ratios_calcules = {}

        # Ordre des INDEX5 dans le fichier de sortie
        self.index5_ordre = [
            '0_A_BANKER', '0_B_BANKER', '0_C_BANKER',
            '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER',
            '1_A_BANKER', '1_B_BANKER', '1_C_BANKER',
            '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER'
        ]

        print(f"🎯 GÉNÉRATEUR RATIOS DiffT/DiffEntG POUR RAPPORT{numero_rapport}.TXT")
        print("📊 Reproduction de la structure de ratios_difft_diffentg_manuel.txt")
    
    def extraire_donnees_rapport(self, fichier_path):
        """Extrait les données des tableaux prédictifs du rapport"""
        print(f"\n📂 Lecture de {fichier_path}")

        with open(fichier_path, 'r', encoding='utf-8') as f:
            contenu = f.read()

        # Extraction des INDEX5 observés
        self._extraire_index5_observes(contenu)

        # Extraction des différentiels
        self._extraire_differentiels(contenu)

        print(f"✅ {len(self.index5_observes)} INDEX5 observés extraits")
        print(f"✅ Différentiels extraits pour {len(self.donnees_differentiels)} configurations")
    
    def _extraire_index5_observes(self, contenu):
        """Extrait les INDEX5 observés des lignes OBSERVÉ"""
        # Recherche des lignes OBSERVÉ dans les deux parties
        lignes = contenu.split('\n')

        for ligne in lignes:
            if ligne.startswith('OBSERVÉ'):
                # Split par | et nettoyage
                parties = ligne.split('|')[1:]  # Ignorer la première partie "OBSERVÉ"
                index5_list = [idx.strip() for idx in parties if idx.strip() and idx.strip() != '']

                # Déterminer si c'est partie 1 (6-30) ou partie 2 (31-60)
                if len(self.index5_observes) == 0:  # Première partie (6-30)
                    for i, index5 in enumerate(index5_list):
                        main = 6 + i
                        if main <= 30 and index5 != '':
                            self.index5_observes[main] = index5
                else:  # Deuxième partie (31-60)
                    for i, index5 in enumerate(index5_list):
                        main = 31 + i
                        if main <= 60 and index5 != '':
                            self.index5_observes[main] = index5
    
    def _extraire_differentiels(self, contenu):
        """Extrait les données des tableaux différentiels"""
        # Diviser le contenu en sections
        sections = contenu.split('🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS')

        if len(sections) > 1:
            # La section après le titre contient les tableaux
            section_differentiels = sections[1]

            # Diviser en parties 1 et 2
            parties = section_differentiels.split('📊 PARTIE')

            for i, partie in enumerate(parties):
                if i == 0:
                    continue  # Ignorer la partie avant le premier "📊 PARTIE"

                # Identifier le numéro de partie
                if '1 - MAINS 6' in partie:
                    self._parser_tableau_differentiel(partie, 1)
                elif '2 - MAINS 31' in partie:
                    self._parser_tableau_differentiel(partie, 2)
    
    def _parser_tableau_differentiel(self, tableau, partie):
        """Parse un tableau différentiel"""
        lignes = tableau.split('\n')

        print(f"🔍 Parsing PARTIE {partie}")

        # Trouver les lignes de données (commencent par INDEX5)
        for ligne in lignes:
            if any(idx in ligne for idx in ['0_A_BANKER', '0_B_BANKER', '0_C_BANKER',
                                          '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER',
                                          '1_A_BANKER', '1_B_BANKER', '1_C_BANKER',
                                          '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER']):
                self._parser_ligne_differentiel(ligne, partie)
    
    def _parser_ligne_differentiel(self, ligne, partie):
        """Parse une ligne de différentiels"""
        # Extraction du nom INDEX5
        index5_match = re.match(r'([01]_[ABC]_(?:BANKER|PLAYER|TIE))', ligne)
        if not index5_match:
            return

        index5 = index5_match.group(1)

        # Extraction des groupes de 4 valeurs (DiffC|DiffT|DivEG|EntG)
        pattern_valeurs = r'\|([^|]*)\|([^|]*)\|([^|]*)\|([^|]*)'
        matches = re.findall(pattern_valeurs, ligne)

        # Traitement des valeurs par main selon la partie
        for i, match in enumerate(matches):
            if partie == 1:
                main = 6 + i  # Partie 1: mains 6-30
                if main > 30:
                    break
            else:
                main = 31 + i  # Partie 2: mains 31-60
                if main > 60:
                    break

            diff_c, diff_t, div_eg, ent_g = match

            # Conversion en float si possible
            try:
                if diff_c.strip() != 'N/A' and diff_t.strip() != 'N/A' and \
                   div_eg.strip() != 'N/A' and ent_g.strip() != 'N/A':

                    diff_c_val = float(diff_c.strip())
                    diff_t_val = float(diff_t.strip())
                    div_eg_val = float(div_eg.strip())
                    ent_g_val = float(ent_g.strip())

                    # Stockage
                    if main not in self.donnees_differentiels:
                        self.donnees_differentiels[main] = {}

                    self.donnees_differentiels[main][index5] = {
                        'DiffC': diff_c_val,
                        'DiffT': diff_t_val,
                        'DivEG': div_eg_val,
                        'EntG': ent_g_val
                    }
            except ValueError:
                continue
    
    def calculer_ratios(self):
        """Calcule les ratios DiffT/DiffEntG pour chaque main et INDEX5"""
        print("\n🔬 CALCUL DES RATIOS DiffT/DiffEntG")
        
        for main in range(6, 61):  # Mains 6 à 60
            self.ratios_calcules[main] = {}
            
            for index5 in self.index5_ordre:
                if (main in self.donnees_differentiels and 
                    index5 in self.donnees_differentiels[main]):
                    
                    donnees = self.donnees_differentiels[main][index5]
                    diff_t = donnees['DiffT']
                    ent_g = donnees['EntG']
                    
                    # Calcul du ratio DiffT/DiffEntG
                    if ent_g != 0:
                        ratio = diff_t / ent_g
                        self.ratios_calcules[main][index5] = ratio
                    else:
                        # Division par zéro
                        self.ratios_calcules[main][index5] = None
                else:
                    # Données non disponibles
                    self.ratios_calcules[main][index5] = None
        
        print(f"✅ Ratios calculés pour {len(self.ratios_calcules)} mains")
    
    def generer_fichier_ratios(self, fichier_sortie):
        """Génère le fichier de ratios avec la même structure"""
        print(f"\n📝 Génération du fichier : {fichier_sortie}")

        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("RATIOS DiffT/DiffEntG POUR LES 12 INDEX5 (HORS TIE ET N/A)\n")
            f.write("============================================================\n")
            f.write("\n")
            f.write("Format: Main X: 0_A_BANKER 0_B_BANKER 0_C_BANKER 0_A_PLAYER 0_B_PLAYER 0_C_PLAYER 1_A_BANKER 1_B_BANKER 1_C_BANKER 1_A_PLAYER 1_B_PLAYER 1_C_PLAYER\n")
            f.write("\n")

            # Données pour chaque main
            for main in range(6, 61):
                # INDEX5 observé
                index5_observe = self.index5_observes.get(main, "INCONNU")

                # LOGIQUE FONDAMENTALE : Respecter les règles INDEX1
                ratios_ligne = []

                for i, index5 in enumerate(self.index5_ordre):
                    # Colonnes 1-6 (INDEX5 0_*) et colonnes 7-12 (INDEX5 1_*)
                    if i < 6:  # Colonnes 1-6 (0_A_BANKER à 0_C_PLAYER)
                        if index5_observe.startswith('0_'):
                            # INDEX5 observé = 0_* → calculer ratio
                            if (main in self.ratios_calcules and
                                index5 in self.ratios_calcules[main] and
                                self.ratios_calcules[main][index5] is not None):
                                ratio = self.ratios_calcules[main][index5]
                                ratios_ligne.append(f"{ratio:.3f}")
                            else:
                                ratios_ligne.append("N/A")
                        else:
                            # INDEX5 observé = 1_* → N/A pour colonnes 1-6
                            ratios_ligne.append("N/A")
                    else:  # Colonnes 7-12 (1_A_BANKER à 1_C_PLAYER)
                        if index5_observe.startswith('1_'):
                            # INDEX5 observé = 1_* → calculer ratio
                            if (main in self.ratios_calcules and
                                index5 in self.ratios_calcules[main] and
                                self.ratios_calcules[main][index5] is not None):
                                ratio = self.ratios_calcules[main][index5]
                                ratios_ligne.append(f"{ratio:.3f}")
                            else:
                                ratios_ligne.append("N/A")
                        else:
                            # INDEX5 observé = 0_* → N/A pour colonnes 7-12
                            ratios_ligne.append("N/A")

                # Écriture de la ligne
                f.write(f"Main {main}: {' '.join(ratios_ligne)} | OBSERVÉ: {index5_observe}\n")

        print(f"✅ Fichier généré : {fichier_sortie}")
    
    def afficher_statistiques(self):
        """Affiche des statistiques sur les données extraites"""
        print("\n📊 STATISTIQUES")
        print("=" * 40)
        
        # Comptage des ratios calculés
        total_ratios = 0
        ratios_valides = 0
        
        for main in self.ratios_calcules:
            for index5 in self.ratios_calcules[main]:
                total_ratios += 1
                if self.ratios_calcules[main][index5] is not None:
                    ratios_valides += 1
        
        print(f"📈 Total ratios possibles : {total_ratios}")
        print(f"✅ Ratios calculés : {ratios_valides}")
        print(f"❌ Ratios N/A : {total_ratios - ratios_valides}")
        print(f"📊 Pourcentage valide : {ratios_valides/total_ratios*100:.1f}%")
        
        # Répartition par INDEX5
        print(f"\n📋 Répartition par INDEX5 :")
        for index5 in self.index5_ordre:
            count = 0
            for main in self.ratios_calcules:
                if (index5 in self.ratios_calcules[main] and 
                    self.ratios_calcules[main][index5] is not None):
                    count += 1
            print(f"   {index5:<12} : {count:2d} ratios")

def main():
    """Fonction principale"""
    # Vérification des arguments
    if len(sys.argv) != 2:
        print("❌ USAGE: python generateur_ratios_rapport.py <numero_rapport>")
        print("📝 EXEMPLE: python generateur_ratios_rapport.py 6")
        print("📁 Cela traitera rapport6.txt et créera ratios_difft_diffentg_rapport6.txt")
        sys.exit(1)

    try:
        numero_rapport = int(sys.argv[1])
    except ValueError:
        print("❌ ERREUR: Le numéro de rapport doit être un entier")
        sys.exit(1)

    print(f"🎯 GÉNÉRATION RATIOS DiffT/DiffEntG POUR RAPPORT{numero_rapport}.TXT")
    print("=" * 60)

    # Noms des fichiers
    fichier_entree = f'rapport{numero_rapport}.txt'
    fichier_sortie = f'ratios_difft_diffentg_rapport{numero_rapport}.txt'

    # Vérification de l'existence du fichier d'entrée
    try:
        with open(fichier_entree, 'r', encoding='utf-8') as f:
            pass
    except FileNotFoundError:
        print(f"❌ ERREUR: Le fichier {fichier_entree} n'existe pas")
        sys.exit(1)

    # Initialisation
    generateur = GenerateurRatiosRapport(numero_rapport)

    # Extraction des données
    generateur.extraire_donnees_rapport(fichier_entree)

    # Calcul des ratios
    generateur.calculer_ratios()

    # Génération du fichier
    generateur.generer_fichier_ratios(fichier_sortie)

    # Statistiques
    generateur.afficher_statistiques()

    print("\n🎯 GÉNÉRATION TERMINÉE")
    print(f"📁 Fichier créé : {fichier_sortie}")

if __name__ == "__main__":
    main()
