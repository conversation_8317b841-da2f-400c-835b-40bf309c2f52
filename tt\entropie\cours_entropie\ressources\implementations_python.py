"""
IMPLÉMENTATIONS PYTHON - FORMULES D'ENTROPIE
============================================

Ce module contient toutes les implémentations des formules d'entropie
étudiées dans le cours, avec validation mathématique et gestion des cas limites.

Auteur: Cours Entropie - Niveau Expert
Date: 2025-06-26
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from typing import List, Tuple, Union, Optional
import warnings

# Configuration pour éviter les warnings de log(0)
np.seterr(divide='ignore', invalid='ignore')

class EntropyCalculator:
    """
    Classe principale pour tous les calculs d'entropie.
    Implémente les formules de Shannon, KL, information mutuelle, etc.
    """
    
    def __init__(self, base: float = 2.0, epsilon: float = 1e-12):
        """
        Initialise le calculateur d'entropie.
        
        Args:
            base: Base du logarithme (2 pour bits, e pour nats)
            epsilon: Valeur minimale pour éviter log(0)
        """
        self.base = base
        self.epsilon = epsilon
        
    def _safe_log(self, x: np.ndarray) -> np.ndarray:
        """Calcul sécurisé du logarithme avec gestion de log(0)."""
        x = np.array(x)
        x = np.where(x <= 0, self.epsilon, x)
        return np.log(x) / np.log(self.base)
    
    def _validate_probabilities(self, p: np.ndarray) -> np.ndarray:
        """Valide et normalise un vecteur de probabilités."""
        p = np.array(p, dtype=float)
        
        # Vérifications de base
        if np.any(p < 0):
            raise ValueError("Les probabilités doivent être positives")
        
        # Normalisation si nécessaire
        total = np.sum(p)
        if not np.isclose(total, 1.0, rtol=1e-10):
            warnings.warn(f"Probabilités non normalisées (somme={total:.6f}). Normalisation automatique.")
            p = p / total
            
        return p

    # ==========================================
    # NIVEAU 1: FORMULES DE BASE
    # ==========================================
    
    def shannon_entropy(self, p: Union[List, np.ndarray]) -> float:
        """
        Calcule l'entropie de Shannon: H(X) = -∑ p(x) log p(x)
        
        Args:
            p: Vecteur de probabilités
            
        Returns:
            Entropie de Shannon en bits (ou nats selon la base)
            
        Example:
            >>> calc = EntropyCalculator()
            >>> calc.shannon_entropy([0.5, 0.5])  # Pièce équilibrée
            1.0
        """
        p = self._validate_probabilities(p)
        
        # Calcul avec gestion de 0*log(0) = 0
        log_p = self._safe_log(p)
        entropy_terms = p * log_p
        
        # Remplace NaN par 0 (cas 0*log(0))
        entropy_terms = np.where(p == 0, 0, entropy_terms)
        
        return -np.sum(entropy_terms)
    
    def conditional_entropy(self, joint_p: np.ndarray) -> float:
        """
        Calcule l'entropie conditionnelle H(Y|X) à partir de la distribution jointe.
        
        Args:
            joint_p: Matrice des probabilités jointes p(x,y)
            
        Returns:
            Entropie conditionnelle H(Y|X)
        """
        joint_p = np.array(joint_p)
        
        # Probabilités marginales
        p_x = np.sum(joint_p, axis=1)  # Somme sur Y
        p_y = np.sum(joint_p, axis=0)  # Somme sur X
        
        # H(X,Y) - H(X)
        joint_entropy = self.shannon_entropy(joint_p.flatten())
        marginal_entropy = self.shannon_entropy(p_x)
        
        return joint_entropy - marginal_entropy
    
    def joint_entropy(self, joint_p: np.ndarray) -> float:
        """
        Calcule l'entropie jointe H(X,Y).
        
        Args:
            joint_p: Matrice des probabilités jointes
            
        Returns:
            Entropie jointe
        """
        return self.shannon_entropy(joint_p.flatten())

    # ==========================================
    # NIVEAU 2: FORMULES INTERMÉDIAIRES
    # ==========================================
    
    def mutual_information(self, joint_p: np.ndarray) -> float:
        """
        Calcule l'information mutuelle I(X;Y) = H(X) + H(Y) - H(X,Y)
        
        Args:
            joint_p: Matrice des probabilités jointes p(x,y)
            
        Returns:
            Information mutuelle
        """
        joint_p = np.array(joint_p)
        
        # Probabilités marginales
        p_x = np.sum(joint_p, axis=1)
        p_y = np.sum(joint_p, axis=0)
        
        # Calcul via H(X) + H(Y) - H(X,Y)
        h_x = self.shannon_entropy(p_x)
        h_y = self.shannon_entropy(p_y)
        h_xy = self.joint_entropy(joint_p)
        
        return h_x + h_y - h_xy
    
    def kl_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """
        Calcule la divergence de Kullback-Leibler D(p||q) = ∑ p(x) log(p(x)/q(x))
        
        Args:
            p: Distribution "vraie"
            q: Distribution "approximative"
            
        Returns:
            Divergence KL
        """
        p = self._validate_probabilities(p)
        q = self._validate_probabilities(q)
        
        if len(p) != len(q):
            raise ValueError("Les distributions doivent avoir la même taille")
        
        # Gestion du cas q(x) = 0 mais p(x) > 0
        if np.any((q == 0) & (p > 0)):
            return np.inf
        
        # Calcul standard
        ratio = np.where(p == 0, 1, p / np.maximum(q, self.epsilon))
        log_ratio = self._safe_log(ratio)
        
        return np.sum(p * log_ratio)
    
    def cross_entropy(self, p: np.ndarray, q: np.ndarray) -> float:
        """
        Calcule l'entropie croisée H(p,q) = -∑ p(x) log q(x)
        
        Args:
            p: Distribution vraie
            q: Distribution prédite
            
        Returns:
            Entropie croisée
        """
        p = self._validate_probabilities(p)
        q = self._validate_probabilities(q)
        
        log_q = self._safe_log(q)
        return -np.sum(p * log_q)
    
    def renyi_entropy(self, p: np.ndarray, alpha: float) -> float:
        """
        Calcule l'entropie de Rényi: H_α(X) = (1/(1-α)) log(∑ p(x)^α)
        
        Args:
            p: Vecteur de probabilités
            alpha: Paramètre α (α ≠ 1)
            
        Returns:
            Entropie de Rényi
        """
        p = self._validate_probabilities(p)
        
        if alpha == 1:
            return self.shannon_entropy(p)
        
        if alpha == 0:
            # Entropie de Hartley
            return self._safe_log(np.array([len(p)]))[0]
        
        if alpha == np.inf:
            # Min-entropie
            return -self._safe_log(np.array([np.max(p)]))[0]
        
        # Cas général
        sum_p_alpha = np.sum(p ** alpha)
        return (1 / (1 - alpha)) * self._safe_log(np.array([sum_p_alpha]))[0]

    # ==========================================
    # NIVEAU 3: FORMULES AVANCÉES
    # ==========================================
    
    def metric_entropy_estimate(self, sequence: List, partition_size: int, 
                               max_length: int = 10) -> List[float]:
        """
        Estime l'entropie métrique d'une séquence symbolique.
        
        Args:
            sequence: Séquence de symboles
            partition_size: Taille de l'alphabet
            max_length: Longueur maximale des blocs
            
        Returns:
            Liste des entropies pour chaque longueur de bloc
        """
        entropies = []
        
        for n in range(1, max_length + 1):
            # Compter les blocs de longueur n
            blocks = {}
            for i in range(len(sequence) - n + 1):
                block = tuple(sequence[i:i+n])
                blocks[block] = blocks.get(block, 0) + 1
            
            # Calculer les probabilités
            total_blocks = sum(blocks.values())
            probabilities = [count / total_blocks for count in blocks.values()]
            
            # Entropie du bloc
            block_entropy = self.shannon_entropy(probabilities)
            entropies.append(block_entropy / n)  # Entropie par symbole
        
        return entropies
    
    def topological_entropy_estimate(self, sequence: List, epsilon: float = 0.1) -> float:
        """
        Estime l'entropie topologique par la méthode des ensembles séparés.
        
        Args:
            sequence: Séquence observée
            epsilon: Paramètre de séparation
            
        Returns:
            Estimation de l'entropie topologique
        """
        # Implémentation simplifiée pour démonstration
        # Dans la pratique, nécessite des méthodes plus sophistiquées
        
        n = len(sequence)
        unique_patterns = set()
        
        for length in range(1, min(10, n)):
            for i in range(n - length + 1):
                pattern = tuple(sequence[i:i+length])
                unique_patterns.add(pattern)
        
        # Estimation grossière
        return self._safe_log(np.array([len(unique_patterns)]))[0] / 10

    # ==========================================
    # UTILITAIRES ET VISUALISATION
    # ==========================================
    
    def entropy_decomposition(self, joint_p: np.ndarray) -> dict:
        """
        Décompose l'entropie jointe selon toutes les formules.
        
        Returns:
            Dictionnaire avec toutes les entropies calculées
        """
        joint_p = np.array(joint_p)
        p_x = np.sum(joint_p, axis=1)
        p_y = np.sum(joint_p, axis=0)
        
        results = {
            'H(X)': self.shannon_entropy(p_x),
            'H(Y)': self.shannon_entropy(p_y),
            'H(X,Y)': self.joint_entropy(joint_p),
            'H(Y|X)': self.conditional_entropy(joint_p),
            'H(X|Y)': self.conditional_entropy(joint_p.T),
            'I(X;Y)': self.mutual_information(joint_p)
        }
        
        # Vérifications des identités
        results['Vérification H(X,Y) = H(X) + H(Y|X)'] = np.isclose(
            results['H(X,Y)'], results['H(X)'] + results['H(Y|X)']
        )
        
        results['Vérification I(X;Y) = H(X) - H(X|Y)'] = np.isclose(
            results['I(X;Y)'], results['H(X)'] - results['H(X|Y)']
        )
        
        return results
    
    def plot_entropy_function(self, entropy_func, param_range: Tuple[float, float], 
                             param_name: str = 'p', title: str = 'Fonction d\'Entropie'):
        """
        Trace une fonction d'entropie en fonction d'un paramètre.
        """
        params = np.linspace(param_range[0], param_range[1], 1000)
        entropies = [entropy_func(p) for p in params]
        
        plt.figure(figsize=(10, 6))
        plt.plot(params, entropies, 'b-', linewidth=2)
        plt.xlabel(param_name)
        plt.ylabel('Entropie')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        plt.show()

# ==========================================
# FONCTIONS UTILITAIRES SPÉCIALISÉES
# ==========================================

def bernoulli_entropy(p: float, base: float = 2.0) -> float:
    """
    Calcule l'entropie d'une distribution de Bernoulli.
    
    Args:
        p: Probabilité de succès
        base: Base du logarithme
        
    Returns:
        Entropie de Bernoulli h(p)
    """
    if not 0 <= p <= 1:
        raise ValueError("p doit être entre 0 et 1")
    
    if p == 0 or p == 1:
        return 0.0
    
    log_base = np.log(base)
    return -(p * np.log(p) + (1-p) * np.log(1-p)) / log_base

def uniform_entropy(n: int, base: float = 2.0) -> float:
    """
    Calcule l'entropie d'une distribution uniforme sur n éléments.
    
    Args:
        n: Nombre d'éléments
        base: Base du logarithme
        
    Returns:
        Entropie uniforme log_base(n)
    """
    if n <= 0:
        raise ValueError("n doit être positif")
    
    return np.log(n) / np.log(base)

def empirical_entropy(data: List, correction: bool = True) -> float:
    """
    Calcule l'entropie empirique d'un échantillon de données.
    
    Args:
        data: Échantillon de données
        correction: Appliquer la correction de biais
        
    Returns:
        Entropie empirique (corrigée si demandé)
    """
    # Compter les occurrences
    unique, counts = np.unique(data, return_counts=True)
    n = len(data)
    k = len(unique)
    
    # Probabilités empiriques
    probabilities = counts / n
    
    # Entropie empirique
    calc = EntropyCalculator()
    entropy = calc.shannon_entropy(probabilities)
    
    # Correction de biais (Miller-Madow)
    if correction and n > 0:
        entropy += (k - 1) / (2 * n * np.log(2))
    
    return entropy

# ==========================================
# TESTS ET VALIDATION
# ==========================================

def run_entropy_tests():
    """
    Exécute une série de tests pour valider les implémentations.
    """
    calc = EntropyCalculator()
    
    print("=== TESTS DES FORMULES D'ENTROPIE ===\n")
    
    # Test 1: Entropie de Shannon
    print("Test 1: Entropie de Shannon")
    p_uniform = [0.25, 0.25, 0.25, 0.25]
    h_uniform = calc.shannon_entropy(p_uniform)
    print(f"H(uniforme 4) = {h_uniform:.3f} bits (attendu: 2.000)")
    
    # Test 2: Entropie de Bernoulli
    print("\nTest 2: Entropie de Bernoulli")
    h_bernoulli = bernoulli_entropy(0.5)
    print(f"h(0.5) = {h_bernoulli:.3f} bits (attendu: 1.000)")
    
    # Test 3: Information mutuelle
    print("\nTest 3: Information mutuelle")
    joint = np.array([[0.25, 0.25], [0.25, 0.25]])  # Variables indépendantes
    mi = calc.mutual_information(joint)
    print(f"I(X;Y) indépendantes = {mi:.6f} bits (attendu: 0.000)")
    
    # Test 4: Divergence KL
    print("\nTest 4: Divergence KL")
    p = [0.5, 0.5]
    q = [0.6, 0.4]
    kl = calc.kl_divergence(p, q)
    print(f"D(p||q) = {kl:.6f} bits")
    
    # Test 5: Propriétés
    print("\nTest 5: Vérification des propriétés")
    joint = np.array([[0.3, 0.2], [0.1, 0.4]])
    decomp = calc.entropy_decomposition(joint)
    
    for key, value in decomp.items():
        if isinstance(value, bool):
            print(f"{key}: {'✓' if value else '✗'}")
        else:
            print(f"{key}: {value:.3f}")

if __name__ == "__main__":
    # Exécuter les tests si le script est lancé directement
    run_entropy_tests()
