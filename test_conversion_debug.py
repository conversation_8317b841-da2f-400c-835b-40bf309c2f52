"""
Test de debug de la conversion Python → Julia
"""

import os
import sys

# CORRECTIF : Ajouter le répertoire bin de Julia au PATH AVANT tout import
julia_bin_path = r"C:\Users\<USER>\.julia\juliaup\julia-1.11.5+0.x64.w64.mingw32\bin"
current_path = os.environ.get('PATH', '')
if julia_bin_path not in current_path:
    os.environ['PATH'] = julia_bin_path + os.pathsep + current_path

from python_julia_bridge import AdvancedJuliaPythonBridge

print("=== DEBUG CONVERSION PYTHON JULIA ===")

bridge = AdvancedJuliaPythonBridge()

# Test de conversion
test_list = [0.5, 0.3, 0.2]
print(f"Liste Python: {test_list}")

# Générer le code Julia
items = ", ".join([str(item) for item in test_list])
julia_code = f'vec([{items}])'
print(f"Code Julia genere: {julia_code}")

# Tester la conversion
try:
    result = bridge.Main.eval(julia_code)
    print(f"Resultat Julia: {result}")
    print(f"Type Julia: {type(result)}")
    
    # Tester directement la fonction
    print("\nTest direct de la fonction...")
    shannon_result = bridge.Main.eval(f"JuliaEntropyCore.calculate_shannon_entropy_one_based({julia_code})")
    print(f"Shannon entropy: {shannon_result}")
    
except Exception as e:
    print(f"ERREUR: {e}")
    
    # Test alternatif
    print("\nTest alternatif avec collect()...")
    try:
        julia_code_alt = f'collect([{items}])'
        print(f"Code alternatif: {julia_code_alt}")
        result_alt = bridge.Main.eval(julia_code_alt)
        print(f"Resultat alternatif: {result_alt}")
        
        shannon_result_alt = bridge.Main.eval(f"JuliaEntropyCore.calculate_shannon_entropy_one_based({julia_code_alt})")
        print(f"Shannon entropy alternatif: {shannon_result_alt}")
        
    except Exception as e2:
        print(f"ERREUR alternative: {e2}")

print("\n=== FIN DEBUG ===")
