## **🧮 MÉTHODES DE CALCULS EXTRAITES DE STRAT.TXT**

Voici toutes les méthodes de strat.txt qui font intervenir des calculs, adaptées pour entropie_baccarat_analyzer.py :

---

## **🔍 MÉTHODE 1 : ANALYSE CONTEXTUELLE TEMPORELLE**

### **predict_context_level**
```python
def predict_context_level(self, sequence_history, current_metrics):
    """
    Analyse contextuelle temporelle pour prédiction INDEX5
    Utilise entropie conditionnelle et taux de répétition
    """
    # 1. Analyser les 5-10 dernières mains pour patterns courts
    recent_pattern = sequence_history[-5:]
    
    # 2. Si entropie conditionnelle < 1.5 bits → Forte prédictibilité
    if current_metrics.get('conditional_entropy', 0) < 1.5:
        # Chercher pattern exact dans l'historique
        return self.find_exact_pattern_continuation(recent_pattern, sequence_history)
    
    # 3. Si taux répétition > 15% → Tendance répétitive
    if current_metrics.get('repetition_rate', 0) > 0.15:
        return self.predict_repetition_bias(sequence_history[-1])
    
    return None

def find_exact_pattern_continuation(self, pattern, sequence_history):
    """
    Trouve les continuations d'un pattern exact dans l'historique
    """
    continuations = {}
    pattern_len = len(pattern)
    
    for i in range(len(sequence_history) - pattern_len):
        if sequence_history[i:i+pattern_len] == pattern:
            # Si il y a une continuation après ce pattern
            if i + pattern_len < len(sequence_history):
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = continuations.get(next_value, 0) + 1
    
    if continuations:
        # Retourner la continuation la plus fréquente
        best_continuation = max(continuations.items(), key=lambda x: x[1])
        return best_continuation[0]
    
    return None

def predict_repetition_bias(self, last_value):
    """
    Prédit une répétition de la dernière valeur
    """
    return last_value
```

---

## **🔬 MÉTHODE 2 : ANALYSE ENTROPIQUE AVANCÉE**

### **predict_entropy_level**
```python
def predict_entropy_level(self, sequence_history, entropy_evolution):
    """
    Prédiction basée sur l'analyse entropique avancée
    Utilise entropie métrique, complexité LZ, entropie topologique
    """
    current_metrics = entropy_evolution[-1] if entropy_evolution else {}
    
    # 1. Si entropie métrique stable → Système déterministe
    if self.is_metric_entropy_stable(entropy_evolution[-10:]):
        # Utiliser modèle déterministe basé sur transitions
        return self.predict_deterministic_model(sequence_history)
    
    # 2. Si complexité LZ faible → Séquence compressible
    if current_metrics.get('lz_complexity', 100) < 35:
        # Exploiter patterns de compression
        return self.predict_compression_patterns(sequence_history)
    
    # 3. Si entropie topologique élevée → Richesse structurelle
    if current_metrics.get('topological_entropy', 0) > 4.0:
        # Modèle sophistiqué multi-patterns
        return self.predict_rich_structure_model(sequence_history)
    
    return None

def is_metric_entropy_stable(self, recent_entropy_evolution):
    """
    Vérifie si l'entropie métrique est stable
    """
    if len(recent_entropy_evolution) < 5:
        return False
    
    metric_entropies = [item.get('metric_entropy', 0) for item in recent_entropy_evolution]
    variance = np.var(metric_entropies)
    
    return variance < 0.1  # Seuil de stabilité

def predict_deterministic_model(self, sequence_history):
    """
    Modèle déterministe basé sur les transitions
    """
    return self.predict_transition_analysis(sequence_history, {})

def predict_compression_patterns(self, sequence_history):
    """
    Exploite les patterns de compression pour prédiction
    """
    # Chercher les patterns répétitifs les plus récents
    for pattern_len in range(2, min(6, len(sequence_history))):
        recent_pattern = sequence_history[-pattern_len:]
        
        # Chercher ce pattern dans l'historique
        continuation = self.find_exact_pattern_continuation(recent_pattern, sequence_history)
        if continuation:
            return continuation
    
    return None

def predict_rich_structure_model(self, sequence_history):
    """
    Modèle sophistiqué pour structures riches
    """
    # Combiner plusieurs approches pour structures complexes
    predictions = []
    
    # Analyse de transitions
    trans_pred = self.predict_transition_analysis(sequence_history, {})
    if trans_pred:
        predictions.append(trans_pred)
    
    # Analyse de patterns
    pattern_pred = self.predict_compression_patterns(sequence_history)
    if pattern_pred:
        predictions.append(pattern_pred)
    
    # Retourner la prédiction la plus fréquente
    if predictions:
        from collections import Counter
        counter = Counter(predictions)
        return counter.most_common(1)[0][0]
    
    return None
```

---

## **🎯 MÉTHODE 3 : PRÉDICTION PROBABILISTE BAYÉSIENNE**

### **predict_bayesian_level**
```python
def predict_bayesian_level(self, sequence_history, observed_frequencies):
    """
    Prédiction bayésienne utilisant probabilités théoriques INDEX5
    """
    # Probabilités théoriques INDEX5 (adaptées depuis BaccaratEntropyAnalyzer)
    THEORETICAL_PROBS = {
        '0_A_BANKER': 0.085136, '1_A_BANKER': 0.086389,
        '0_B_BANKER': 0.064676, '1_B_BANKER': 0.065479,
        '0_C_BANKER': 0.077903, '1_C_BANKER': 0.078929,
        '0_A_PLAYER': 0.085240, '1_A_PLAYER': 0.086361,
        '0_B_PLAYER': 0.076907, '1_B_PLAYER': 0.077888,
        '0_C_PLAYER': 0.059617, '1_C_PLAYER': 0.060352,
        '0_A_TIE': 0.017719, '1_A_TIE': 0.017978,
        '0_B_TIE': 0.016281, '1_B_TIE': 0.016482,
        '0_C_TIE': 0.013241, '1_C_TIE': 0.013423
    }
    
    # 1. Calculer probabilités conditionnelles observées
    conditional_probs = self.calculate_conditional_probabilities(sequence_history)
    
    # 2. Pondérer avec probabilités théoriques (Bayes)
    bayesian_probs = {}
    for index5_value in THEORETICAL_PROBS:
        # P(Xₙ₊₁|contexte) ∝ P(contexte|Xₙ₊₁) × P(Xₙ₊₁)
        observed_prob = conditional_probs.get(index5_value, 0)
        theoretical_prob = THEORETICAL_PROBS[index5_value]
        
        bayesian_prob = observed_prob * theoretical_prob
        bayesian_probs[index5_value] = bayesian_prob
    
    # Normaliser les probabilités
    total_prob = sum(bayesian_probs.values())
    if total_prob > 0:
        normalized_probs = {k: v/total_prob for k, v in bayesian_probs.items()}
        
        # Retourner la valeur avec la plus haute probabilité
        best_prediction = max(normalized_probs.items(), key=lambda x: x[1])
        return best_prediction[0]
    
    return None

def calculate_conditional_probabilities(self, sequence_history):
    """
    Calcule les probabilités conditionnelles observées
    """
    from collections import Counter, defaultdict
    
    # Analyser les transitions depuis les 3 dernières valeurs
    context_transitions = defaultdict(Counter)
    
    for i in range(3, len(sequence_history)):
        context = tuple(sequence_history[i-3:i])
        next_value = sequence_history[i]
        context_transitions[context][next_value] += 1
    
    # Calculer probabilités conditionnelles pour le contexte actuel
    if len(sequence_history) >= 3:
        current_context = tuple(sequence_history[-3:])
        
        if current_context in context_transitions:
            transitions = context_transitions[current_context]
            total_transitions = sum(transitions.values())
            
            conditional_probs = {
                value: count / total_transitions 
                for value, count in transitions.items()
            }
            
            return conditional_probs
    
    return {}
```

---

## **🏆 MÉTHODE 4 : FUSION MULTI-ALGORITHMES**

### **predict_next_index5**
```python
def predict_next_index5(self, sequence_history, all_metrics):
    """
    Prédicteur INDEX5 principal utilisant fusion multi-algorithmes
    """
    if not sequence_history or not all_metrics:
        return None
    
    # ÉTAPE 1: Évaluation de la prédictibilité actuelle
    current_predictability = 1 - (all_metrics.get('conditional_entropy', 3.9309) / 3.9309)
    
    # ÉTAPE 2: Sélection de la stratégie optimale
    if current_predictability > 0.60:  # Très prévisible
        weight_deterministic = 0.7
        weight_bayesian = 0.2
        weight_frequency = 0.1
        
    elif current_predictability > 0.55:  # Prévisible
        weight_deterministic = 0.5
        weight_bayesian = 0.3
        weight_frequency = 0.2
        
    else:  # Moins prévisible
        weight_deterministic = 0.3
        weight_bayesian = 0.5
        weight_frequency = 0.2
    
    # ÉTAPE 3: Calcul des prédictions par chaque méthode
    pred_deterministic = self.predict_deterministic_patterns(sequence_history, all_metrics)
    pred_bayesian = self.predict_bayesian_theoretical(sequence_history, all_metrics)
    pred_frequency = self.predict_frequency_based(sequence_history, all_metrics)
    
    # ÉTAPE 4: Fusion pondérée des prédictions
    predictions = []
    if pred_deterministic:
        predictions.append(('DETERMINISTIC', pred_deterministic, weight_deterministic))
    if pred_bayesian:
        predictions.append(('BAYESIAN', pred_bayesian, weight_bayesian))
    if pred_frequency:
        predictions.append(('FREQUENCY', pred_frequency, weight_frequency))
    
    if predictions:
        # Vote pondéré
        vote_weights = {}
        for method, pred, weight in predictions:
            vote_weights[pred] = vote_weights.get(pred, 0) + weight
        
        # Retourner la prédiction avec le plus fort poids
        best_prediction = max(vote_weights.items(), key=lambda x: x[1])
        
        return {
            'predicted_index5': best_prediction[0],
            'confidence': best_prediction[1],
            'predictability_score': current_predictability,
            'contributing_methods': [p[0] for p in predictions if p[1] == best_prediction[0]]
        }
    
    return None
```

---

## **🔍 MÉTHODES SPÉCIALISÉES SUPPLÉMENTAIRES**

### **predict_deterministic_patterns**
```python
def predict_deterministic_patterns(self, sequence_history, metrics):
    """
    Exploite les patterns récurrents détectés
    """
    pattern_predictions = {}
    
    for pattern_length in range(2, 6):
        if len(sequence_history) >= pattern_length:
            current_pattern = sequence_history[-pattern_length:]
            
            # Chercher ce pattern dans l'historique
            continuations = self.find_pattern_continuations(current_pattern, sequence_history)
            
            if continuations:
                # Pondérer par fréquence et récence
                for continuation, freq in continuations.items():
                    weight = freq * (1.0 / pattern_length)  # Patterns courts = plus fiables
                    pattern_predictions[continuation] = pattern_predictions.get(continuation, 0) + weight
    
    if pattern_predictions:
        # Normaliser et retourner le meilleur
        total_weight = sum(pattern_predictions.values())
        normalized = {k: v/total_weight for k, v in pattern_predictions.items()}
        return max(normalized.items(), key=lambda x: x[1])[0]
    
    return None

def find_pattern_continuations(self, pattern, sequence_history):
    """
    Trouve toutes les continuations d'un pattern dans l'historique
    """
    continuations = {}
    pattern_len = len(pattern)
    
    for i in range(len(sequence_history) - pattern_len):
        if sequence_history[i:i+pattern_len] == pattern:
            # Si il y a une continuation après ce pattern
            if i + pattern_len < len(sequence_history):
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = continuations.get(next_value, 0) + 1
    
    return continuations
```

### **predict_bayesian_theoretical**
```python
def predict_bayesian_theoretical(self, sequence_history, metrics):
    """
    Combine observations avec probabilités théoriques INDEX5
    """
    from collections import Counter
    
    # Calculer fréquences observées récentes (20 dernières mains)
    recent_sequence = sequence_history[-20:] if len(sequence_history) >= 20 else sequence_history
    observed_freq = Counter(recent_sequence)
    
    # Probabilités théoriques
    THEORETICAL_PROBS = {
        '0_A_BANKER': 0.085136, '1_A_BANKER': 0.086389,
        '0_B_BANKER': 0.064676, '1_B_BANKER': 0.065479,
        '0_C_BANKER': 0.077903, '1_C_BANKER': 0.078929,
        '0_A_PLAYER': 0.085240, '1_A_PLAYER': 0.086361,
        '0_B_PLAYER': 0.076907, '1_B_PLAYER': 0.077888,
        '0_C_PLAYER': 0.059617, '1_C_PLAYER': 0.060352,
        '0_A_TIE': 0.017719, '1_A_TIE': 0.017978,
        '0_B_TIE': 0.016281, '1_B_TIE': 0.016482,
        '0_C_TIE': 0.013241, '1_C_TIE': 0.013423
    }
    
    bayesian_probs = {}
    
    for index5_value in THEORETICAL_PROBS.keys():
        # Probabilité théorique
        p_theoretical = THEORETICAL_PROBS[index5_value]
        
        # Probabilité observée (avec lissage de Laplace)
        observed_count = observed_freq.get(index5_value, 0)
        p_observed = (observed_count + 1) / (len(recent_sequence) + len(THEORETICAL_PROBS))
        
        # Fusion Bayésienne (pondération adaptative selon la prédictibilité)
        predictability = metrics.get('predictability_score', 0.5)
        
        # Plus c'est prévisible, plus on fait confiance aux observations
        bayesian_prob = (predictability * p_observed + 
                        (1 - predictability) * p_theoretical)
        
        bayesian_probs[index5_value] = bayesian_prob
    
    if bayesian_probs:
        return max(bayesian_probs.items(), key=lambda x: x[1])[0]
    
    return None
```

### **predict_transition_analysis**
```python
def predict_transition_analysis(self, sequence_history, metrics):
    """
    Analyse les transitions conditionnelles INDEX5
    """
    from collections import Counter
    
    # Construire matrice de transitions
    transitions = {}
    
    for i in range(len(sequence_history) - 1):
        current = sequence_history[i]
        next_val = sequence_history[i + 1]
        
        if current not in transitions:
            transitions[current] = Counter()
        transitions[current][next_val] += 1
    
    # Prédire basé sur la dernière valeur
    if sequence_history:
        last_value = sequence_history[-1]
        
        if last_value in transitions:
            # Normaliser les transitions depuis cette valeur
            total_transitions = sum(transitions[last_value].values())
            transition_probs = {
                next_val: count / total_transitions 
                for next_val, count in transitions[last_value].items()
            }
            
            if transition_probs:
                return max(transition_probs.items(), key=lambda x: x[1])[0]
    
    return None
```

### **predict_frequency_based**
```python
def predict_frequency_based(self, sequence_history, metrics):
    """
    Prédiction basée sur les fréquences observées
    """
    from collections import Counter
    
    # Analyser les fréquences récentes
    recent_sequence = sequence_history[-30:] if len(sequence_history) >= 30 else sequence_history
    freq_counter = Counter(recent_sequence)
    
    if freq_counter:
        # Retourner la valeur la plus fréquente récemment
        return freq_counter.most_common(1)[0][0]
    
    return None
```

**🎯 TOUTES CES MÉTHODES SONT ADAPTÉES POUR ÊTRE INTÉGRÉES DANS LA CLASSE INDEX5Predictor D'ENTROPIE_BACCARAT_ANALYZER.PY**
