# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 277 à 339
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def calculate_block_entropy_evolution(self, sequence: List[str], max_block_length: int = 5) -> List[Dict]:
        """
        📈 ÉVOLUTION - Calcule l'évolution de l'entropie par blocs (Kolmogorov-Sinai)

        Méthode centrale d'analyse qui calcule position par position :
        - Entropie de Shannon observée et théorique
        - Entropie conditionnelle H(Xₙ|Xₙ₋₁)
        - Entropie métrique h_μ(T)
        - Taux d'entropie asymptotique

        Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
        Méthode: entropie/cours_entropie/ressources/implementations_python.py (metric_entropy_estimate)

        Args:
            sequence: Séquence des valeurs INDEX5
            max_block_length: Longueur maximale des blocs à analyser

        Returns:
            Liste des résultats d'entropie pour chaque position
        """
        results = []

        for n in range(1, len(sequence) + 1):
            # Sous-séquence de longueur n
            subsequence = sequence[:n]

            # Calcul de l'entropie de blocs pour différentes longueurs
            block_entropies = self._calculate_block_entropies(subsequence, max_block_length)

            # Entropie conditionnelle (prédictibilité du prochain symbole)
            conditional_entropy = self._calculate_conditional_entropy(subsequence)

            # Estimation de l'entropie métrique (taux de création d'information)
            metric_entropy = self._estimate_metric_entropy(subsequence, max_block_length)

            # Comptage simple pour comparaison (fréquences observées)
            counts = Counter(subsequence)
            total = len(subsequence)
            empirical_probs = [counts[value] / total for value in counts.keys()]

            # CORRECTION: Entropie simple observée basée sur fréquences empiriques
            # Calcul de l'entropie de Shannon sur les fréquences observées dans la sous-séquence
            simple_entropy_observed = self._calculate_shannon_entropy(empirical_probs)

            # CORRECTION: Entropie simple avec probabilités théoriques (formule AEP)
            simple_entropy_theoretical = self._calculate_sequence_entropy_aep(subsequence)

            results.append({
                'position': n,
                'sequence_length': n,
                'unique_values': len(counts),
                'simple_entropy': simple_entropy_observed,  # Entropie de Shannon sur fréquences observées
                'simple_entropy_theoretical': simple_entropy_theoretical,  # Entropie AEP avec probabilités théoriques
                'block_entropies': block_entropies,  # H(blocs de longueur k)
                'conditional_entropy': conditional_entropy,  # H(Xₙ|X₁,...,Xₙ₋₁)
                'metric_entropy': metric_entropy,  # Estimation h_μ(T)
                'entropy_rate': block_entropies[-1] if block_entropies else 0,  # Taux d'entropie
                'observed_values': list(counts.keys()),
                'counts': dict(counts),
                'empirical_probabilities': dict(zip(counts.keys(), empirical_probs))
            })

        return results