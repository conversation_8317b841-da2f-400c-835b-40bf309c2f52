# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3136 à 3168
# Type: Méthode de la classe INDEX5PredictionValidator

    def get_accuracy_stats(self):
        """
        Retourne les statistiques de précision
        """
        if self.total_predictions == 0:
            return {
                'correct_predictions': 0,
                'total_predictions': 0,
                'accuracy_percentage': 0.0,
                'accuracy_ratio': "0/0",
                'correct_predictions_high_confidence': 0,
                'total_predictions_high_confidence': 0,
                'accuracy_percentage_high_confidence': 0.0,
                'accuracy_ratio_high_confidence': "0/0"
            }

        accuracy = (self.correct_predictions / self.total_predictions) * 100

        # Calculer la précision pour les prédictions haute confiance (>= 60% poids pondéré)
        accuracy_high_confidence = 0.0
        if self.total_predictions_high_confidence > 0:
            accuracy_high_confidence = (self.correct_predictions_high_confidence / self.total_predictions_high_confidence) * 100

        return {
            'correct_predictions': self.correct_predictions,
            'total_predictions': self.total_predictions,
            'accuracy_percentage': accuracy,
            'accuracy_ratio': f"{self.correct_predictions}/{self.total_predictions}",
            'correct_predictions_high_confidence': self.correct_predictions_high_confidence,
            'total_predictions_high_confidence': self.total_predictions_high_confidence,
            'accuracy_percentage_high_confidence': accuracy_high_confidence,
            'accuracy_ratio_high_confidence': f"{self.correct_predictions_high_confidence}/{self.total_predictions_high_confidence}"
        }