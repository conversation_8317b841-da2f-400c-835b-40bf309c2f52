# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2966 à 2996
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

    def generate_predictive_table(self, sequence, evolution, analyzer):
        """
        Génère le tableau prédictif complet divisé en deux parties
        """
        if not sequence or not evolution:
            return "❌ Données insuffisantes pour générer le tableau prédictif"

        # Générer la première partie (Mains 1-30)
        table_part1 = self.generate_predictive_table_part(sequence, evolution, analyzer, 1, 30, 1)

        # Générer la deuxième partie (Mains 31-60)
        table_part2 = self.generate_predictive_table_part(sequence, evolution, analyzer, 31, 60, 2)

        # Combiner les deux parties avec la légende
        complete_table = table_part1 + "\n\n" + table_part2 + f"""

📋 LÉGENDE DU TABLEAU PRÉDICTIF :
• DiffC = DiffCond (Différentiel Entropie Conditionnelle)
• DiffT = DiffTaux (Différentiel Taux d'Entropie)
• DivEG = DiffDivEntropG (Différentiel Diversité Entropique)
• EntG = DiffEntropG (Différentiel Entropie Générale)
• Seules les 9 valeurs INDEX5 respectant les règles INDEX1 sont calculées
• N/A = Valeur non calculable (ne respecte pas les règles INDEX1)
• --- = Main non disponible dans cette partie

🔄 RÈGLES INDEX1 DÉTERMINISTES :
• Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
• Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""

        return complete_table