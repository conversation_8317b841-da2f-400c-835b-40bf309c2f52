#!/usr/bin/env python3
"""Test pour vérifier le décalage temporel dans les prédictions"""

import re

def analyser_decalage():
    """Analyse si les prédictions utilisent un décalage temporel"""
    
    # Charger le fichier
    with open('ratios_difft_diffentg_rapport1.txt', 'r', encoding='utf-8') as f:
        lignes = f.readlines()
    
    # Parser toutes les mains
    mains_data = {}
    
    for ligne in lignes:
        if ligne.startswith('Main '):
            # Extraction numéro de main
            match_main = re.match(r'Main (\d+):', ligne)
            if not match_main:
                continue
            
            main = int(match_main.group(1))
            
            # Extraction INDEX5 observé
            match_observe = re.search(r'OBSERVÉ:\s*([^\s]+)', ligne)
            if not match_observe:
                continue
            
            index5_observe = match_observe.group(1)
            
            # Extraction INDEX3
            if '_BANKER' in index5_observe:
                index3_observe = 'BANKER'
            elif '_PLAYER' in index5_observe:
                index3_observe = 'PLAYER'
            elif '_TIE' in index5_observe:
                index3_observe = 'TIE'
            else:
                continue
            
            mains_data[main] = {
                'index3_observe': index3_observe,
                'index5_observe': index5_observe,
                'ligne': ligne.strip()
            }
    
    print("🔍 ANALYSE DU DÉCALAGE TEMPOREL")
    print("=" * 50)
    
    # Afficher les premières mains pour analyse
    mains_triees = sorted(mains_data.keys())
    
    print(f"📊 Mains disponibles: {mains_triees[:10]}...")
    print(f"📊 Total mains: {len(mains_triees)}")
    print()
    
    print("🎯 LOGIQUE ACTUELLE (Main N → Prédiction Main N):")
    print("-" * 50)
    
    for i, main in enumerate(mains_triees[:5]):
        data = mains_data[main]
        print(f"Main {main}: Ratios Main {main} → Prédit Main {main} = {data['index3_observe']}")
    
    print()
    print("🎯 LOGIQUE ALTERNATIVE (Main N → Prédiction Main N+1):")
    print("-" * 50)
    
    for i, main in enumerate(mains_triees[:5]):
        if i < len(mains_triees) - 1:
            main_suivante = mains_triees[i + 1]
            data_actuelle = mains_data[main]
            data_suivante = mains_data[main_suivante]
            print(f"Main {main}: Ratios Main {main} → Prédit Main {main_suivante} = {data_suivante['index3_observe']}")
        else:
            print(f"Main {main}: Ratios Main {main} → Prédit Main ? = (pas de main suivante)")
    
    print()
    print("🤔 QUESTION CLÉE:")
    print("Les ratios DiffT/DiffEntG de la Main N sont-ils calculés POUR prédire:")
    print("  A) La Main N elle-même (logique actuelle)")
    print("  B) La Main N+1 (logique prédictive)")
    print()
    
    # Test avec Main 6 et 7
    if 6 in mains_data and 7 in mains_data:
        print("📊 EXEMPLE CONCRET - Main 6 et 7:")
        print("-" * 40)
        print(f"Main 6 observé: {mains_data[6]['index3_observe']}")
        print(f"Main 7 observé: {mains_data[7]['index3_observe']}")
        print()
        print("Si logique actuelle (N→N):")
        print("  - Ratios Main 6 prédisent Main 6")
        print("  - Ratios Main 7 prédisent Main 7")
        print()
        print("Si logique prédictive (N→N+1):")
        print("  - Ratios Main 6 prédisent Main 7")
        print("  - Ratios Main 7 prédisent Main 8")
        print("  - Main 6 ne peut pas être prédite (pas de Main 5)")

if __name__ == "__main__":
    analyser_decalage()
