#!/usr/bin/env python3
"""Debug de la position temporelle dans H_conditionnelle"""

def debug_position_temporelle():
    """Vérifie précisément les positions temporelles"""
    
    # Séquence avec numéros de mains explicites
    sequence = ['0_A_BANKER', '1_B_PLAYER', '0_C_BANKER', '1_A_PLAYER', '0_B_BANKER']
    mains = [6, 7, 8, 9, 10]  # Numéros de mains correspondants
    
    print("🔍 ANALYSE POSITION TEMPORELLE - H_CONDITIONNELLE")
    print("=" * 60)
    print("📊 Séquence avec numéros de mains:")
    for i, (main, symbol) in enumerate(zip(mains, sequence)):
        print(f"   Position {i}: Main {main} = '{symbol}'")
    print()
    
    # Simulation du code exact
    context_length = 1
    
    print("🔄 TRANSITIONS CONSTRUITES:")
    print("-" * 40)
    print("i | Contexte (Main N) | Symbole_suivant (Main N+1) | Transition")
    print("-" * 40)
    
    for i in range(len(sequence) - context_length):
        context = sequence[i]           # Position i
        next_symbol = sequence[i+1]     # Position i+1
        main_context = mains[i]         # Main N
        main_next = mains[i+1]          # Main N+1
        
        print(f"{i} | '{context}' (Main {main_context}) | '{next_symbol}' (Main {main_next}) | Main {main_context} → Main {main_next}")
    
    print()
    print("🎯 CONCLUSION:")
    print("- Contexte = symbole à la Main N")
    print("- Symbole_suivant = symbole à la Main N+1")
    print("- H(X|Y) calcule la prédictibilité du symbole de la Main N+1")
    print("  sachant le symbole de la Main N")
    
    print()
    print("📋 FORMULE TEMPORELLE EXACTE:")
    print("H_conditionnelle = H(X_main_N+1 | X_main_N)")
    print("                 = ∑ P(X_main_N) × H(X_main_N+1 | X_main_N)")

if __name__ == "__main__":
    debug_position_temporelle()
