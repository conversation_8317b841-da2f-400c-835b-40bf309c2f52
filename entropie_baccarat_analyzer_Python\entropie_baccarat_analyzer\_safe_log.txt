# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 123 à 134
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def _safe_log(self, x: np.ndarray) -> np.ndarray:
        """
        🔒 SÉCURITÉ - Calcul sécurisé du logarithme avec gestion de log(0)

        Évite les erreurs mathématiques en remplaçant les valeurs nulles ou négatives
        par epsilon avant le calcul logarithmique.

        Référence: entropie/cours_entropie/ressources/implementations_python.py
        """
        x = np.array(x)
        x = np.where(x <= 0, self.epsilon, x)
        return np.log(x) / np.log(self.base)