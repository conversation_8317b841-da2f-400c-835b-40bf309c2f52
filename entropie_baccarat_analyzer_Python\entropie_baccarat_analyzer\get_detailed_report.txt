# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 3170 à 3210
# Type: Méthode de la classe INDEX5PredictionValidator

    def get_detailed_report(self):
        """
        Retourne un rapport détaillé des prédictions
        """
        stats = self.get_accuracy_stats()

        report = f"""
🎯 VALIDATION DES PRÉDICTIONS INDEX5 - COMPARAISON INDEX3
═══════════════════════════════════════════════════════

📊 STATISTIQUES GLOBALES:
• Prédictions correctes: {stats['correct_predictions']}
• Total prédictions: {stats['total_predictions']}
• Taux de réussite: {stats['accuracy_percentage']:.2f}%
• Ratio: {stats['accuracy_ratio']}

🎯 STATISTIQUES HAUTE CONFIANCE (≥ 60% poids pondéré):
• Prédictions correctes (≥60%): {stats['correct_predictions_high_confidence']}
• Total prédictions (≥60%): {stats['total_predictions_high_confidence']}
• Taux de réussite (≥60%): {stats['accuracy_percentage_high_confidence']:.2f}%
• Ratio (≥60%): {stats['accuracy_ratio_high_confidence']}

🔍 MÉTHODE DE VALIDATION:
• Format INDEX5: INDEX1_INDEX2_INDEX3
• Validation: INDEX3_prédit = INDEX3_réel
• Exemple: 0_A_BANKER → INDEX3 = BANKER
• Confiance haute: Score ≥ 0.60 (60%)
"""

        if self.prediction_details:
            report += "\n📋 DÉTAIL DES PRÉDICTIONS:\n"
            report += "Position | Prédiction → Réalité | INDEX3 Prédit → INDEX3 Réel | Confiance | Résultat\n"
            report += "---------|---------------------|---------------------------|-----------|----------\n"

            for detail in self.prediction_details[-10:]:  # Afficher les 10 dernières
                result_symbol = "✅" if detail['is_correct'] else "❌"
                confidence_symbol = "🎯" if detail.get('is_high_confidence', False) else "📊"
                confidence_display = f"{detail.get('confidence', 0.0):.2f}"
                report += f"Main {detail['position']:2d}  | {detail['predicted_index5']:12s} → {detail['actual_index5']:12s} | {detail['predicted_index3']:6s} → {detail['actual_index3']:6s} | {confidence_symbol}{confidence_display:5s} | {result_symbol}\n"

        return report