# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1669 à 1716
# Type: Méthode de la classe INDEX5Calculator

    def calculate_all_metrics(self, sequence_history, current_metrics, entropy_evolution):
        """
        Calcule toutes les métriques disponibles pour une position donnée.
        Retourne un dictionnaire avec tous les scores calculés.
        """
        if len(sequence_history) < 2:
            return {}

        metrics = {}

        # 1. Prédictibilité contextuelle
        metrics['context_predictability'] = self.calculate_context_predictability(sequence_history, current_metrics)

        # 2. Force des patterns
        metrics['pattern_strength'] = self.calculate_pattern_strength(sequence_history)

        # 3. Score de stabilité entropique
        if entropy_evolution:
            metrics['entropy_stability'] = self.calculate_entropy_stability_score(entropy_evolution)

        # 4. Score de compression
        metrics['compression_score'] = self.calculate_compression_score(sequence_history, current_metrics)

        # 5. Richesse structurelle
        metrics['structural_richness'] = self.calculate_structural_richness_score(sequence_history, current_metrics)

        # 6. Divergence bayésienne
        metrics['bayesian_divergence'] = self.calculate_bayesian_divergence_score(sequence_history)

        # 7. Entropie conditionnelle contextuelle
        metrics['conditional_entropy_context'] = self.calculate_conditional_entropy_context(sequence_history)

        # 8. Consensus multi-algorithmes
        metrics['multi_algorithm_consensus'] = self.calculate_multi_algorithm_consensus_score(sequence_history, current_metrics)

        # 9. Score de patterns déterministes
        metrics['deterministic_pattern_score'] = self.calculate_deterministic_pattern_score(sequence_history)

        # 10. Alignement bayésien théorique
        metrics['bayesian_theoretical_alignment'] = self.calculate_bayesian_theoretical_alignment(sequence_history)

        # 11. Entropie de la matrice de transitions
        metrics['transition_matrix_entropy'] = self.calculate_transition_matrix_entropy(sequence_history)

        # 12. Stabilité des fréquences
        metrics['frequency_stability'] = self.calculate_frequency_stability_score(sequence_history)

        return metrics