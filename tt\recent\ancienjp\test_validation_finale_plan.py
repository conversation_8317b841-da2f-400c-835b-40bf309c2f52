"""
VALIDATION FINALE selon plan.txt (lignes 906-936)
Test complet de l'architecture hybride selon lignes 917-935
"""

import os
import sys
import time

# CORRECTIF : Ajouter le répertoire bin de Julia au PATH AVANT tout import
julia_bin_path = r"C:\Users\<USER>\.julia\juliaup\julia-1.11.5+0.x64.w64.mingw32\bin"
current_path = os.environ.get('PATH', '')
if julia_bin_path not in current_path:
    os.environ['PATH'] = julia_bin_path + os.pathsep + current_path

from python_julia_bridge import AdvancedJuliaPythonBridge

def test_complete_hybrid_architecture():
    """
    Test complet de l'architecture hybride selon plan.txt lignes 917-935
    """
    print("=== VALIDATION FINALE SELON PLAN.TXT ===")
    
    bridge = AdvancedJuliaPythonBridge()
    test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"] * 100  # 300 éléments
    
    # 1. Test indexation 1-based (ligne 921 plan)
    print("1. Test indexation 1-based:")
    try:
        # Vérifier que Julia utilise indexation 1-based
        result = bridge.Main.eval('test_seq = ["A", "B", "C"]; test_seq[1]')
        indexation_1_based = (result == "A")  # Premier élément à l'indice 1
        print(f"   Julia utilise indexation 1-based: {indexation_1_based}")
    except:
        indexation_1_based = False
        print(f"   Julia utilise indexation 1-based: {indexation_1_based}")
    
    # 2. Test équivalence résultats (ligne 924 plan)
    print("\n2. Test équivalence résultats:")
    try:
        shannon_result = bridge.calculate_shannon_entropy_one_based([0.5, 0.3, 0.2])
        expected_shannon = 1.****************
        equivalence = abs(shannon_result - expected_shannon) < 1e-10
        print(f"   Résultats équivalents (précision 1e-10): {equivalence}")
    except:
        equivalence = False
        print(f"   Résultats équivalents (précision 1e-10): {equivalence}")
    
    # 3. Test performance (ligne 927 plan)
    print("\n3. Test performance:")
    try:
        start = time.time()
        result = bridge.analyze_complete_game_one_based(test_sequence)
        performance_time = time.time() - start
        performance_ok = performance_time < 5.0  # Objectif < 5s
        print(f"   Temps d'exécution: {performance_time:.3f}s")
        print(f"   Performance acceptable: {performance_ok}")
    except:
        performance_ok = False
        print(f"   Performance acceptable: {performance_ok}")
    
    # 4. Test interface utilisateur (ligne 930 plan)
    print("\n4. Test interface utilisateur:")
    try:
        # L'interface doit être identique à l'original
        result = bridge.analyze_complete_game_one_based(test_sequence)
        interface_unchanged = (
            'sequence_length' in result and
            'entropy_evolution' in result and
            'complexity_metrics' in result
        )
        print(f"   Interface utilisateur inchangée: {interface_unchanged}")
    except:
        interface_unchanged = False
        print(f"   Interface utilisateur inchangée: {interface_unchanged}")
    
    # 5. Test toutes les méthodes migrées (ligne 933 plan)
    print("\n5. Test méthodes migrées:")
    try:
        # Vérifier les 14 méthodes entropiques principales
        required_methods = [
            'safe_log_one_based', 'calculate_shannon_entropy_one_based',
            'calculate_sequence_entropy_aep_one_based', 'calculate_conditional_entropy_one_based',
            'estimate_metric_entropy_one_based', 'calculate_block_entropies_one_based',
            'calculate_block_entropy_evolution_one_based', 'calculate_sequence_complexity_one_based',
            'approximate_lz_complexity_one_based', 'approximate_topological_entropy_one_based',
            'calculate_repetition_rate_one_based', 'calculate_simple_entropy_theoretical_one_based',
            'validate_probabilities_one_based', 'calculate_block_entropies_raw_one_based'
        ]
        
        methods_present = sum(1 for method in required_methods if hasattr(bridge, method))
        all_methods_migrated = methods_present == len(required_methods)
        print(f"   Méthodes migrées: {methods_present}/{len(required_methods)}")
        print(f"   Toutes les méthodes migrées: {all_methods_migrated}")
    except:
        all_methods_migrated = False
        print(f"   Toutes les méthodes migrées: {all_methods_migrated}")
    
    # Critères de succès selon plan.txt lignes 908-914
    criteria = [
        ("Indexation 1-based", indexation_1_based),
        ("Équivalence résultats", equivalence),
        ("Performance", performance_ok),
        ("Interface inchangée", interface_unchanged),
        ("Méthodes migrées", all_methods_migrated)
    ]
    
    print(f"\n6. Résumé des critères de succès:")
    passed_criteria = 0
    for criterion, passed in criteria:
        status = "PASSE" if passed else "ECHEC"
        print(f"   {criterion}: {status}")
        if passed:
            passed_criteria += 1
    
    success_rate = passed_criteria / len(criteria) * 100
    print(f"\n7. Taux de succès: {success_rate:.1f}% ({passed_criteria}/{len(criteria)})")
    
    # Validation finale selon plan ligne 935
    if passed_criteria == len(criteria):
        print("\nARCHITECTURE HYBRIDE JULIA-PYTHON VALIDEE !")
        print("Tous les critères du plan.txt sont satisfaits")
        return True
    else:
        print("\nARCHITECTURE HYBRIDE INCOMPLETE")
        print("Certains critères du plan.txt ne sont pas satisfaits")
        return False

if __name__ == "__main__":
    success = test_complete_hybrid_architecture()
    sys.exit(0 if success else 1)
