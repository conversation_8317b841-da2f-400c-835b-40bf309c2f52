#!/usr/bin/env python3
"""Debug détaillé du calcul de H_conditionnelle"""

import math

def debug_h_conditionnelle():
    """Trace le calcul exact de H_conditionnelle avec un exemple concret"""
    
    # Exemple de séquence INDEX5
    sequence = ['0_A_BANKER', '1_B_PLAYER', '0_C_BANKER', '1_A_PLAYER', '0_B_BANKER']
    
    print("🔍 DEBUG CALCUL H_CONDITIONNELLE")
    print("=" * 50)
    print(f"📊 Séquence d'exemple: {sequence}")
    print(f"📊 Longueur: {len(sequence)}")
    print()
    
    # Probabilités théoriques (simplifiées pour l'exemple)
    theoretical_probs = {
        '0_A_BANKER': 0.0625, '0_B_BANKER': 0.0625, '0_C_BANKER': 0.0625,
        '0_A_PLAYER': 0.0625, '0_B_PLAYER': 0.0625, '0_C_PLAYER': 0.0625,
        '1_A_BANKER': 0.0625, '1_B_BANKER': 0.0625, '1_C_BANKER': 0.0625,
        '1_A_PLAYER': 0.0625, '1_B_PLAYER': 0.0625, '1_C_PLAYER': 0.0625
    }
    
    # ÉTAPE 1: Construire les transitions contexte → symbole suivant
    print("🔄 ÉTAPE 1: CONSTRUCTION DES TRANSITIONS")
    print("-" * 40)
    
    context_length = 1  # Contexte de longueur 1
    context_transitions = {}
    
    for i in range(len(sequence) - context_length):
        context = sequence[i]  # Contexte de longueur 1
        next_symbol = sequence[i + context_length]
        
        print(f"Position {i}: '{context}' → '{next_symbol}'")
        
        if context not in context_transitions:
            context_transitions[context] = {}
        
        if next_symbol not in context_transitions[context]:
            context_transitions[context][next_symbol] = 0
        context_transitions[context][next_symbol] += 1
    
    print()
    print("📋 TRANSITIONS COMPTÉES:")
    for context, transitions in context_transitions.items():
        print(f"  Contexte '{context}': {transitions}")
    
    # ÉTAPE 2: Calculer les probabilités des contextes
    print()
    print("📊 ÉTAPE 2: CALCUL DES PROBABILITÉS")
    print("-" * 40)
    
    total_transitions = sum(sum(transitions.values()) for transitions in context_transitions.values())
    print(f"Total transitions: {total_transitions}")
    
    context_probs = {}
    for context, transitions in context_transitions.items():
        context_count = sum(transitions.values())
        context_prob = context_count / total_transitions
        context_probs[context] = context_prob
        print(f"P('{context}') = {context_count}/{total_transitions} = {context_prob:.4f}")
    
    # ÉTAPE 3: Calculer H(X|contexte) pour chaque contexte
    print()
    print("🧮 ÉTAPE 3: CALCUL H(X|CONTEXTE) AVEC AEP")
    print("-" * 40)
    
    conditional_entropy = 0.0
    
    for context, transitions in context_transitions.items():
        print(f"\n🎯 Contexte: '{context}'")
        print(f"   Transitions: {transitions}")
        
        # Créer la séquence des symboles suivants pour ce contexte
        context_sequence = []
        for next_symbol, count in transitions.items():
            context_sequence.extend([next_symbol] * count)
        
        print(f"   Séquence contexte: {context_sequence}")
        
        # Calculer H(X|ce contexte) selon AEP
        # H = -(1/n) × ∑ log₂(p_théo(xᵢ))
        if context_sequence:
            total_log_prob = 0.0
            for value in context_sequence:
                if value in theoretical_probs:
                    p_theo = theoretical_probs[value]
                    log_prob = math.log2(p_theo)
                    total_log_prob += log_prob
                    print(f"     '{value}': p_théo={p_theo:.4f}, log₂(p)={log_prob:.4f}")
                else:
                    print(f"     '{value}': ERREUR - pas dans probabilités théoriques")
            
            context_entropy = -total_log_prob / len(context_sequence)
            print(f"   H(X|'{context}') = -({total_log_prob:.4f})/{len(context_sequence)} = {context_entropy:.4f} bits")
        else:
            context_entropy = 0.0
            print(f"   H(X|'{context}') = 0.0 bits (séquence vide)")
        
        # Pondérer par P(contexte)
        context_prob = context_probs[context]
        weighted_entropy = context_prob * context_entropy
        conditional_entropy += weighted_entropy
        
        print(f"   Contribution: {context_prob:.4f} × {context_entropy:.4f} = {weighted_entropy:.4f}")
    
    # ÉTAPE 4: Résultat final
    print()
    print("🎯 ÉTAPE 4: RÉSULTAT FINAL")
    print("-" * 40)
    print(f"H(X|Y) = ∑ P(y) × H(X|y) = {conditional_entropy:.4f} bits")
    
    # VÉRIFICATION: Calcul manuel alternatif
    print()
    print("✅ VÉRIFICATION MANUELLE")
    print("-" * 40)
    
    manual_entropy = 0.0
    for context, transitions in context_transitions.items():
        context_prob = context_probs[context]
        
        # Calculer H(X|contexte) de manière classique
        context_total = sum(transitions.values())
        context_entropy_manual = 0.0
        
        for next_symbol, count in transitions.items():
            p_next = count / context_total
            if p_next > 0:
                context_entropy_manual -= p_next * math.log2(p_next)
        
        manual_entropy += context_prob * context_entropy_manual
        print(f"Contexte '{context}': H_classique={context_entropy_manual:.4f}, contribution={context_prob * context_entropy_manual:.4f}")
    
    print(f"H_conditionnelle_classique = {manual_entropy:.4f} bits")
    print(f"H_conditionnelle_AEP = {conditional_entropy:.4f} bits")
    print(f"Différence = {abs(manual_entropy - conditional_entropy):.4f} bits")

if __name__ == "__main__":
    debug_h_conditionnelle()
