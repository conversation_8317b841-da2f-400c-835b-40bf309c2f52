# ═══════════════════════════════════════════════════════════════════════════════
# JU<PERSON><PERSON> ENTROPY CORE - INDEXATION ONE-BASED
# ═══════════════════════════════════════════════════════════════════════════════
#
# Module Julia pour tous les calculs entropiques avec indexation one-based.
# AUCUN indice 0 dans ce module - tous les tableaux commencent à 1.
#
# Auteur: Architecture Hybride Julia-Python
# Date: 2025-01-08
# ═══════════════════════════════════════════════════════════════════════════════

module JuliaEntropyCore

export calculate_all_entropies_one_based, process_evolution_one_based, 
       calculate_differentials_one_based, predict_next_value_one_based

using Statistics

"""
🎯 PROBABILITÉS THÉORIQUES INDEX5 (ONE-BASED)

Probabilités exactes pour les 18 valeurs INDEX5.
Stockées dans un dictionnaire avec clés string.
"""
const THEORETICAL_PROBS_ONE_BASED = Dict{String, Float64}(
    "0_A_BANKER" => 0.08514, "1_A_BANKER" => 0.08639,
    "0_B_BANKER" => 0.06468, "1_B_BANKER" => 0.06548,
    "0_C_BANKER" => 0.07790, "1_C_BANKER" => 0.07893,
    "0_A_PLAYER" => 0.08524, "1_A_PLAYER" => 0.08636,
    "0_B_PLAYER" => 0.07691, "1_B_PLAYER" => 0.07789,
    "0_C_PLAYER" => 0.05962, "1_C_PLAYER" => 0.06035,
    "0_A_TIE" => 0.01772, "1_A_TIE" => 0.01798,
    "0_B_TIE" => 0.01628, "1_B_TIE" => 0.01648,
    "0_C_TIE" => 0.01324, "1_C_TIE" => 0.01342
)

"""
📊 CALCUL ENTROPIE SHANNON ONE-BASED

Calcule l'entropie de Shannon avec indexation one-based.
Tous les indices commencent à 1.

Args:
    sequence: Vecteur de valeurs INDEX5 (indices 1 à n)
    position: Position finale dans la séquence (1-based)

Returns:
    Entropie de Shannon en bits
"""
function calculate_shannon_entropy_one_based(sequence::Vector{String}, position::Int)
    if position < 1 || position > length(sequence)
        error("❌ Position invalide: $position (doit être 1 ≤ pos ≤ $(length(sequence)))")
    end
    
    # Sous-séquence de 1 à position (one-based)
    sub_seq = sequence[1:position]
    n = length(sub_seq)
    
    println("📊 Calcul Shannon [1:$position] - Longueur: $n")
    
    # Comptage fréquences (one-based)
    freq_dict = Dict{String, Int}()
    for i in 1:n  # Commence à 1
        val = sub_seq[i]
        freq_dict[val] = get(freq_dict, val, 0) + 1
    end
    
    # Calcul entropie
    entropy = 0.0
    for (val, count) in freq_dict
        p = count / n
        if p > 0
            entropy -= p * log2(p)
        end
    end
    
    return entropy
end

"""
🔬 CALCUL ENTROPIE AEP ONE-BASED

Calcule l'entropie AEP avec probabilités théoriques.
Utilise indexation one-based pour tous les accès.

Args:
    sequence: Vecteur INDEX5 (indices 1 à n)
    position: Position finale (1-based)

Returns:
    Entropie AEP théorique en bits
"""
function calculate_aep_entropy_one_based(sequence::Vector{String}, position::Int)
    if position < 1 || position > length(sequence)
        error("❌ Position invalide: $position")
    end
    
    # Sous-séquence (one-based)
    sub_seq = sequence[1:position]
    n = length(sub_seq)
    
    println("🔬 Calcul AEP [1:$position] - Longueur: $n")
    
    # Calcul log-probabilité totale
    total_log_prob = 0.0
    for i in 1:n  # One-based
        val = sub_seq[i]
        if haskey(THEORETICAL_PROBS_ONE_BASED, val)
            prob = THEORETICAL_PROBS_ONE_BASED[val]
            total_log_prob += log2(prob)
        else
            println("⚠️  Valeur inconnue: $val")
        end
    end
    
    # Entropie AEP
    entropy_aep = -total_log_prob / n
    return entropy_aep
end

"""
🧠 CALCUL ENTROPIE CONDITIONNELLE ONE-BASED

Calcule H(Xₙ|Xₙ₋₁) avec indexation one-based.
Contexte de longueur 1.

Args:
    sequence: Vecteur INDEX5 (indices 1 à n)
    position: Position finale (1-based)

Returns:
    Entropie conditionnelle en bits
"""
function calculate_conditional_entropy_one_based(sequence::Vector{String}, position::Int)
    if position < 2  # Besoin d'au moins 2 éléments
        return 0.0
    end
    
    # Sous-séquence (one-based)
    sub_seq = sequence[1:position]
    n = length(sub_seq)
    
    println("🧠 Calcul Conditionnelle [1:$position] - Longueur: $n")
    
    # Comptage transitions contexte → suivant
    transitions = Dict{String, Dict{String, Int}}()
    
    for i in 1:(n-1)  # One-based, s'arrête à n-1
        context = sub_seq[i]
        next_val = sub_seq[i+1]  # i+1 est valide car i ≤ n-1
        
        if !haskey(transitions, context)
            transitions[context] = Dict{String, Int}()
        end
        
        transitions[context][next_val] = get(transitions[context], next_val, 0) + 1
    end
    
    # Calcul entropie conditionnelle
    total_entropy = 0.0
    total_count = n - 1
    
    for (context, next_counts) in transitions
        context_count = sum(values(next_counts))
        context_prob = context_count / total_count
        
        # Entropie pour ce contexte
        context_entropy = 0.0
        for (next_val, count) in next_counts
            p_next_given_context = count / context_count
            if p_next_given_context > 0
                context_entropy -= p_next_given_context * log2(p_next_given_context)
            end
        end
        
        total_entropy += context_prob * context_entropy
    end
    
    return total_entropy
end

"""
📈 CALCUL ENTROPIE MÉTRIQUE ONE-BASED

Estime l'entropie métrique h_μ avec indexation one-based.
Utilise des blocs de longueur croissante.

Args:
    sequence: Vecteur INDEX5 (indices 1 à n)
    position: Position finale (1-based)
    max_length: Longueur maximale des blocs

Returns:
    Estimation entropie métrique
"""
function calculate_metric_entropy_one_based(sequence::Vector{String}, position::Int, max_length::Int = 4)
    if position < max_length
        return calculate_shannon_entropy_one_based(sequence, position)
    end
    
    # Sous-séquence (one-based)
    sub_seq = sequence[1:position]
    
    println("📈 Calcul Métrique [1:$position] - Max longueur: $max_length")
    
    # Calcul entropies de blocs
    block_entropies = Float64[]
    
    for block_len in 1:max_length
        if block_len > length(sub_seq)
            break
        end
        
        # Extraction blocs de longueur block_len
        blocks = String[]
        for i in 1:(length(sub_seq) - block_len + 1)  # One-based
            block = join(sub_seq[i:(i + block_len - 1)], "_")
            push!(blocks, block)
        end
        
        # Calcul entropie des blocs
        if !isempty(blocks)
            freq_dict = Dict{String, Int}()
            for block in blocks
                freq_dict[block] = get(freq_dict, block, 0) + 1
            end
            
            entropy = 0.0
            n_blocks = length(blocks)
            for (block, count) in freq_dict
                p = count / n_blocks
                if p > 0
                    entropy -= p * log2(p)
                end
            end
            
            push!(block_entropies, entropy / block_len)
        end
    end
    
    # Retourne la dernière estimation
    return isempty(block_entropies) ? 0.0 : block_entropies[end]
end

"""
🎯 CALCUL TOUTES ENTROPIES ONE-BASED

Calcule toutes les métriques entropiques pour une position donnée.
Utilise exclusivement l'indexation one-based.

Args:
    sequence: Vecteur INDEX5 complet
    position: Position à analyser (1-based)

Returns:
    Dictionnaire avec toutes les métriques
"""
function calculate_all_entropies_one_based(sequence::Vector{String}, position::Int)
    println("\n🎯 CALCUL TOUTES ENTROPIES - Position $position (ONE-BASED)")
    println("=" * 60)
    
    if position < 1 || position > length(sequence)
        error("❌ Position invalide: $position (doit être 1 ≤ pos ≤ $(length(sequence)))")
    end
    
    # Calculs avec indexation one-based
    shannon = calculate_shannon_entropy_one_based(sequence, position)
    aep = calculate_aep_entropy_one_based(sequence, position)
    conditional = calculate_conditional_entropy_one_based(sequence, position)
    metric = calculate_metric_entropy_one_based(sequence, position)
    
    results = Dict{String, Any}(
        "position" => position,  # Position one-based
        "sequence_length" => position,
        "shannon_entropy" => shannon,
        "aep_entropy" => aep,
        "conditional_entropy" => conditional,
        "metric_entropy" => metric,
        "indexing" => "ONE_BASED"
    )
    
    println("✅ Calculs terminés pour position $position")
    return results
end

end  # module JuliaEntropyCore
