"""
JuliaEntropyCore.jl - Module Julia pour calculs entropiques fondamentaux
Architecture hybride Julia-Python avec indexation 1-based

Migré depuis entropie_baccarat_analyzer.py selon plan.txt
"""

module JuliaEntropyCore

export safe_log_one_based, validate_probabilities_one_based, 
       calculate_shannon_entropy_one_based, calculate_sequence_entropy_aep_one_based,
       calculate_conditional_entropy_one_based, estimate_metric_entropy_one_based,
       calculate_block_entropies_one_based, calculate_block_entropies_raw_one_based,
       calculate_block_entropy_evolution_one_based, calculate_sequence_complexity_one_based,
       approximate_lz_complexity_one_based, approximate_topological_entropy_one_based,
       calculate_repetition_rate_one_based, calculate_simple_entropy_theoretical_one_based,
       THEORETICAL_PROBS_ONE_BASED

# Probabilités théoriques INDEX5 EXACTES (ligne 100-112 source)
# CORRECTION EXPERTE: Copie exacte avec correction ligne 104
const THEORETICAL_PROBS_RAW_ONE_BASED = Dict{String, Float64}(
    "0_A_BANKER" => 8.5136, "1_A_BANKER" => 8.6389,
    "0_B_BANKER" => 6.4676, "1_B_BANKER" => 6.5479,  # CORRIGÉ: était 7.6907
    "0_C_BANKER" => 7.7903, "1_C_BANKER" => 7.8929,
    "0_A_PLAYER" => 8.5240, "1_A_PLAYER" => 8.6361,
    "0_B_PLAYER" => 7.6907, "1_B_PLAYER" => 7.7888,
    "0_C_PLAYER" => 5.9617, "1_C_PLAYER" => 6.0352,
    "0_A_TIE" => 1.7719, "1_A_TIE" => 1.7978,
    "0_B_TIE" => 1.6281, "1_B_TIE" => 1.6482,
    "0_C_TIE" => 1.3241, "1_C_TIE" => 1.3423
)

# Normalisation des probabilités (ligne 114-116 source)
const TOTAL_PROB = sum(values(THEORETICAL_PROBS_RAW_ONE_BASED))
const THEORETICAL_PROBS_ONE_BASED = Dict{String, Float64}(
    k => v/TOTAL_PROB for (k, v) in THEORETICAL_PROBS_RAW_ONE_BASED
)

"""
Calcul sécurisé du logarithme avec gestion de log(0)
Migré depuis _safe_log.txt (ligne 123-134)
"""
function safe_log_one_based(x::Vector{Float64}; epsilon::Float64=1e-10, base::Float64=2.0)::Vector{Float64}
    # Gestion des valeurs nulles/négatives
    x_safe = [val <= 0 ? epsilon : val for val in x]
    return log.(x_safe) ./ log(base)
end

"""
Valide et normalise un vecteur de probabilités
Migré depuis _validate_probabilities.txt (ligne 136-157)
"""
function validate_probabilities_one_based(p::Vector{Float64})::Vector{Float64}
    if any(x -> x < 0, p)
        throw(ArgumentError("Les probabilités doivent être positives"))
    end
    
    total = sum(p)
    if total > 0
        return p ./ total
    else
        # Distribution uniforme si toutes les probabilités sont nulles
        return ones(length(p)) ./ length(p)
    end
end

"""
Calcule l'entropie de Shannon: H(X) = -∑ p(x) log₂ p(x)
Migré depuis _calculate_shannon_entropy.txt (ligne 163-187)
"""
function calculate_shannon_entropy_one_based(probabilities::Vector{Float64})::Float64
    p = validate_probabilities_one_based(probabilities)
    log_p = safe_log_one_based(p)
    entropy_terms = p .* log_p
    
    # Gestion 0*log(0) = 0
    entropy_terms = [p[i] == 0 ? 0.0 : entropy_terms[i] for i in 1:length(p)]
    return -sum(entropy_terms)
end

"""
Calcule l'entropie d'une séquence selon la formule AEP exacte
FORMULE MAÎTRE: H_seq = -(1/n) × ∑log₂(p_théo(xᵢ))
Migré depuis _calculate_sequence_entropy_aep.txt (ligne 415-453)
"""
function calculate_sequence_entropy_aep_one_based(sequence::Vector{String})::Float64
    if isempty(sequence)
        return 0.0
    end
    
    n = length(sequence)
    log_prob_sum = 0.0
    
    for i in 1:n  # Indexation 1-based native
        symbol = sequence[i]
        prob = get(THEORETICAL_PROBS_ONE_BASED, symbol, 1e-10)
        log_prob_sum += log2(prob)
    end
    
    return -(1.0/n) * log_prob_sum
end

"""
Calcule l'entropie conditionnelle H(X|Y) = ∑ P(y) × H(X|y)
Migré depuis _calculate_conditional_entropy.txt (ligne 462-514)
"""
function calculate_conditional_entropy_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end
    
    # Construire les paires (contexte, symbole)
    pairs = [(sequence[i], sequence[i+1]) for i in 1:(length(sequence)-1)]
    
    # Compter les occurrences
    context_counts = Dict{String, Int64}()
    pair_counts = Dict{Tuple{String, String}, Int64}()
    
    for (context, symbol) in pairs
        context_counts[context] = get(context_counts, context, 0) + 1
        pair_counts[(context, symbol)] = get(pair_counts, (context, symbol), 0) + 1
    end
    
    # Calculer l'entropie conditionnelle
    total_pairs = length(pairs)
    conditional_entropy = 0.0
    
    for context in keys(context_counts)
        p_context = context_counts[context] / total_pairs
        
        # Calculer H(X|context)
        context_entropy = 0.0
        for symbol in keys(THEORETICAL_PROBS_ONE_BASED)
            pair_count = get(pair_counts, (context, symbol), 0)
            if pair_count > 0
                p_symbol_given_context = pair_count / context_counts[context]
                context_entropy -= p_symbol_given_context * log2(p_symbol_given_context)
            end
        end
        
        conditional_entropy += p_context * context_entropy
    end
    
    return conditional_entropy
end

"""
Estime l'entropie métrique h_μ(T) selon Kolmogorov-Sinai
Migré depuis _estimate_metric_entropy.txt (ligne 516-548)
"""
function estimate_metric_entropy_one_based(sequence::Vector{String}, max_length::Int64)::Float64
    if length(sequence) < 3
        return 0.0
    end

    # Calculer les entropies de blocs sans normalisation
    block_entropies_raw = calculate_block_entropies_raw_one_based(sequence, max_length)

    if length(block_entropies_raw) < 2
        return 0.0
    end

    # Estimation de la limite h_μ(T) = lim H(n)/n
    # Utiliser les deux dernières valeurs pour estimer la limite
    n = length(block_entropies_raw)
    if n >= 2
        # Approximation linéaire de la limite
        h_n_minus_1 = block_entropies_raw[n-1] / (n-1)
        h_n = block_entropies_raw[n] / n

        # Estimation de la limite par extrapolation
        metric_entropy = 2 * h_n - h_n_minus_1
        return max(0.0, metric_entropy)
    end

    return block_entropies_raw[end] / length(block_entropies_raw)
end

"""
Calcule l'entropie pour des blocs de différentes longueurs
Migré depuis _calculate_block_entropies.txt (ligne 341-379)
"""
function calculate_block_entropies_one_based(sequence::Vector{String}, max_length::Int64)::Vector{Float64}
    if length(sequence) < 2
        return [0.0]
    end

    entropies = Float64[]

    for block_len in 1:min(max_length, length(sequence))
        if block_len == 1
            # Entropie AEP pour blocs longueur 1
            block_entropy = calculate_sequence_entropy_aep_one_based(sequence)
            push!(entropies, block_entropy)
        else
            # Blocs de longueur > 1
            block_sequences = Vector{String}[]
            for i in 1:(length(sequence) - block_len + 1)  # Indexation 1-based
                block_sequence = sequence[i:(i+block_len-1)]
                push!(block_sequences, block_sequence)
            end

            # Calcul entropie des blocs
            if !isempty(block_sequences)
                # Conversion blocs en chaînes pour comptage
                block_strings = [join(block, "_") for block in block_sequences]
                counts = Dict{String, Int64}()
                for block_str in block_strings
                    counts[block_str] = get(counts, block_str, 0) + 1
                end

                probs = [count / length(block_strings) for count in values(counts)]
                block_entropy = calculate_shannon_entropy_one_based(probs)
                push!(entropies, block_entropy)
            else
                push!(entropies, 0.0)
            end
        end
    end

    return entropies
end

"""
Calcule les entropies de blocs brutes (sans normalisation)
Migré depuis _calculate_block_entropies_raw.txt
"""
function calculate_block_entropies_raw_one_based(sequence::Vector{String}, max_length::Int64)::Vector{Float64}
    entropies = calculate_block_entropies_one_based(sequence, max_length)
    # Les entropies brutes sont les entropies multipliées par leur longueur de bloc
    raw_entropies = Float64[]
    for (i, entropy) in enumerate(entropies)
        push!(raw_entropies, entropy * i)  # i = longueur du bloc (1-based)
    end
    return raw_entropies
end

"""
Calcule l'évolution de l'entropie par blocs position par position
Migré depuis calculate_block_entropy_evolution.txt (ligne 277-339)
"""
function calculate_block_entropy_evolution_one_based(sequence::Vector{String}, max_block_length::Int64=5)::Vector{Dict{String, Any}}
    results = Dict{String, Any}[]

    for n in 1:length(sequence)  # Indexation 1-based native
        # Sous-séquence de longueur n
        subsequence = sequence[1:n]

        # Calculs entropiques
        block_entropies = calculate_block_entropies_one_based(subsequence, max_block_length)
        conditional_entropy = calculate_conditional_entropy_one_based(subsequence)
        metric_entropy = estimate_metric_entropy_one_based(subsequence, max_block_length)

        # Fréquences empiriques
        counts = Dict{String, Int64}()
        for val in subsequence
            counts[val] = get(counts, val, 0) + 1
        end

        empirical_probs = [counts[key] / length(subsequence) for key in keys(counts)]
        simple_entropy_observed = calculate_shannon_entropy_one_based(empirical_probs)

        # Entropie théorique AEP
        simple_entropy_theoretical = calculate_sequence_entropy_aep_one_based(subsequence)

        push!(results, Dict{String, Any}(
            "position" => n,
            "metric_entropy" => metric_entropy,
            "conditional_entropy" => conditional_entropy,
            "entropy_rate" => length(block_entropies) > 1 ? block_entropies[2] - block_entropies[1] : 0.0,
            "simple_entropy" => simple_entropy_observed,
            "simple_entropy_theoretical" => simple_entropy_theoretical
        ))
    end

    return results
end

"""
Calcule la complexité de séquence (Lempel-Ziv approximée)
Migré depuis _calculate_sequence_complexity.txt
"""
function calculate_sequence_complexity_one_based(sequence::Vector{String})::Dict{String, Float64}
    if length(sequence) < 2
        return Dict{String, Float64}("lz_complexity" => 0.0, "topological_entropy" => 0.0, "repetition_rate" => 0.0)
    end

    lz_complexity = approximate_lz_complexity_one_based(sequence)
    topological_entropy = approximate_topological_entropy_one_based(sequence)
    repetition_rate = calculate_repetition_rate_one_based(sequence)

    return Dict{String, Float64}(
        "lz_complexity" => lz_complexity,
        "topological_entropy" => topological_entropy,
        "repetition_rate" => repetition_rate
    )
end

"""
Approximation de la complexité Lempel-Ziv
Migré depuis _approximate_lz_complexity.txt
"""
function approximate_lz_complexity_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end

    # Algorithme LZ77 simplifié
    complexity = 0
    i = 1

    while i <= length(sequence)
        # Chercher la plus longue correspondance
        max_match_length = 0

        for j in 1:(i-1)
            match_length = 0
            k = 0
            while (i + k <= length(sequence)) && (j + k <= i - 1) &&
                  (sequence[i + k] == sequence[j + k])
                match_length += 1
                k += 1
            end
            max_match_length = max(max_match_length, match_length)
        end

        if max_match_length > 0
            i += max_match_length
        else
            i += 1
        end

        complexity += 1
    end

    # Normaliser par la longueur
    return complexity / length(sequence)
end

"""
Approximation de l'entropie topologique
Migré depuis _approximate_topological_entropy.txt
"""
function approximate_topological_entropy_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 3
        return 0.0
    end

    # Compter les transitions uniques
    transitions = Set{Tuple{String, String}}()
    for i in 1:(length(sequence)-1)
        push!(transitions, (sequence[i], sequence[i+1]))
    end

    # Entropie topologique ≈ log(nombre de transitions) / longueur
    return log(length(transitions)) / length(sequence)
end

"""
Calcule le taux de répétition dans la séquence
Migré depuis _calculate_repetition_rate.txt
"""
function calculate_repetition_rate_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end

    repetitions = 0
    for i in 2:length(sequence)
        if sequence[i] == sequence[i-1]
            repetitions += 1
        end
    end

    return repetitions / (length(sequence) - 1)
end

"""
Calcule l'entropie simple théorique
Migré depuis _calculate_simple_entropy_theoretical.txt
"""
function calculate_simple_entropy_theoretical_one_based(sequence::Vector{String})::Float64
    return calculate_sequence_entropy_aep_one_based(sequence)
end

end # module JuliaEntropyCore
