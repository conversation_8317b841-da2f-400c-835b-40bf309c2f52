# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 516 à 548
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def _estimate_metric_entropy(self, sequence: List[str], max_length: int) -> float:
        """
        Estime l'entropie métrique h_μ(T) selon Kolmogorov-Sinai.
        CORRECTION EXPERTE: Calcul rigoureux de la limite h_μ(T) = lim H(n)/n.

        Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
        Formule: h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
        """
        if len(sequence) < 3:
            return 0.0

        # CORRECTION: Calculer les entropies de blocs sans normalisation par longueur
        block_entropies_raw = self._calculate_block_entropies_raw(sequence, max_length)

        if len(block_entropies_raw) < 2:
            return 0.0

        # CORRECTION EXPERTE: Calcul rigoureux de h_μ = lim_{n→∞} H(n)/n
        # Formule correcte: h_μ(T) = lim H(n)/n selon Kolmogorov-Sinai
        h_metric_estimates = []
        for i, entropy in enumerate(block_entropies_raw):
            block_length = i + 1  # Longueur du bloc (commence à 1)
            if block_length > 0:
                h_estimate = entropy / block_length  # H(n)/n
                h_metric_estimates.append(h_estimate)

        # Prendre la dernière estimation comme approximation de la limite
        if h_metric_estimates:
            h_metric = max(0.0, h_metric_estimates[-1])
        else:
            h_metric = 0.0

        return h_metric