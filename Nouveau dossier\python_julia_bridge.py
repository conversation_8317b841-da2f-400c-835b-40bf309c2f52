"""
PONT PYTHON-JULIA POUR INDEXATION ONE-BASED
==========================================

Interface Python pour utiliser les calculs Julia avec indexation one-based.
Convertit automatiquement les indices Python (0-based) vers Julia (1-based).

Auteur: Architecture Hybride
Date: 2025-01-08
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional
from julia import Main
import json

class JuliaPythonBridge:
    """
    🌉 PONT PYTHON-JULIA
    
    Interface pour utiliser Julia avec indexation one-based depuis Python.
    Gère automatiquement la conversion des indices.
    """
    
    def __init__(self):
        """
        🔧 INITIALISATION - Configuration du pont Julia-Python
        
        Charge les modules Julia et configure l'environnement one-based.
        """
        print("🌉 Initialisation Pont Python-Julia")
        print("🎯 Configuration indexation ONE-BASED")
        
        # Chargement du module Julia
        try:
            Main.include("julia_entropy_core.jl")
            Main.eval("using .JuliaEntropyCore")
            print("✅ Module Julia chargé avec succès")
        except Exception as e:
            print(f"❌ Erreur chargement Julia: {e}")
            raise
        
        # Vérification indexation one-based
        self._verify_one_based_indexing()
        
    def _verify_one_based_indexing(self):
        """
        ✅ VÉRIFICATION - Test indexation one-based
        
        Vérifie que Julia utilise bien l'indexation one-based.
        """
        print("\n🔍 Vérification indexation ONE-BASED")
        
        Main.eval("""
        test_array = ["A", "B", "C", "D", "E"]
        println("📊 Tableau test: ", test_array)
        println("🎯 Premier élément [1]: ", test_array[1])
        println("🎯 Dernier élément [end]: ", test_array[end])
        
        try
            test_array[0]
            println("❌ ERREUR: Indice 0 accepté!")
        catch BoundsError
            println("✅ Indice 0 correctement rejeté")
        end
        """)
        
    def convert_python_to_julia_sequence(self, python_sequence: List[str]) -> None:
        """
        🔄 CONVERSION - Séquence Python vers Julia
        
        Convertit une séquence Python (0-based) vers Julia (1-based).
        
        Args:
            python_sequence: Liste Python avec indices 0-based
        """
        print(f"\n🔄 Conversion séquence Python → Julia")
        print(f"📏 Longueur Python: {len(python_sequence)} (indices 0 à {len(python_sequence)-1})")
        
        # Conversion vers Julia (automatique avec PyJulia)
        Main.julia_sequence = python_sequence
        
        # Vérification en Julia
        Main.eval("""
        println("📏 Longueur Julia: ", length(julia_sequence), " (indices 1 à ", length(julia_sequence), ")")
        println("🎯 Premier élément Julia [1]: ", julia_sequence[1])
        println("🎯 Dernier élément Julia [end]: ", julia_sequence[end])
        """)
        
    def calculate_entropy_at_position(self, sequence: List[str], python_position: int) -> Dict[str, Any]:
        """
        📊 CALCUL ENTROPIE - Position spécifique avec conversion d'indices
        
        Calcule l'entropie à une position donnée en convertissant l'indice Python vers Julia.
        
        Args:
            sequence: Séquence INDEX5 (Python list)
            python_position: Position Python (0-based)
            
        Returns:
            Résultats des calculs entropiques
        """
        # Conversion Python (0-based) → Julia (1-based)
        julia_position = python_position + 1
        
        print(f"\n📊 CALCUL ENTROPIE")
        print(f"🐍 Position Python: {python_position} (0-based)")
        print(f"🔴 Position Julia: {julia_position} (1-based)")
        
        # Envoi vers Julia
        self.convert_python_to_julia_sequence(sequence)
        
        # Calcul en Julia avec indexation one-based
        Main.eval(f"""
        results = calculate_all_entropies_one_based(julia_sequence, {julia_position})
        """)
        
        # Récupération des résultats
        julia_results = Main.results
        
        # Conversion des résultats pour Python
        python_results = {
            'python_position': python_position,  # Position originale Python
            'julia_position': julia_position,    # Position convertie Julia
            'sequence_length': len(sequence),
            'shannon_entropy': float(julia_results['shannon_entropy']),
            'aep_entropy': float(julia_results['aep_entropy']),
            'conditional_entropy': float(julia_results['conditional_entropy']),
            'metric_entropy': float(julia_results['metric_entropy']),
            'indexing_system': 'ONE_BASED_JULIA'
        }
        
        return python_results
        
    def calculate_evolution_one_based(self, sequence: List[str]) -> List[Dict[str, Any]]:
        """
        📈 ÉVOLUTION ENTROPIQUE - Calcul complet avec indexation one-based
        
        Calcule l'évolution entropique pour toute la séquence.
        Utilise Julia pour tous les calculs avec indexation one-based.
        
        Args:
            sequence: Séquence INDEX5 complète
            
        Returns:
            Liste des résultats pour chaque position
        """
        print(f"\n📈 CALCUL ÉVOLUTION ENTROPIQUE ONE-BASED")
        print(f"📏 Séquence longueur: {len(sequence)}")
        print(f"🎯 Calculs Julia avec indices 1 à {len(sequence)}")
        
        results = []
        
        # Conversion séquence vers Julia
        self.convert_python_to_julia_sequence(sequence)
        
        # Calcul pour chaque position (Julia 1-based)
        for python_pos in range(len(sequence)):
            julia_pos = python_pos + 1
            
            print(f"🔄 Position {python_pos} (Python) → {julia_pos} (Julia)")
            
            # Calcul en Julia
            position_results = self.calculate_entropy_at_position(sequence, python_pos)
            results.append(position_results)
            
        print(f"✅ Évolution calculée: {len(results)} positions")
        return results
        
    def compare_indexing_systems(self, sequence: List[str], position: int):
        """
        🔍 COMPARAISON - Systèmes d'indexation
        
        Compare les résultats entre indexation 0-based et 1-based.
        
        Args:
            sequence: Séquence test
            position: Position à comparer
        """
        print(f"\n🔍 COMPARAISON SYSTÈMES D'INDEXATION")
        print(f"📊 Position testée: {position}")
        print("=" * 50)
        
        # Python 0-based (simulation)
        print("🐍 PYTHON (0-based):")
        print(f"   Indices: 0 à {len(sequence)-1}")
        print(f"   Élément à position {position}: {sequence[position] if position < len(sequence) else 'HORS LIMITES'}")
        
        # Julia 1-based
        print("🔴 JULIA (1-based):")
        julia_pos = position + 1
        print(f"   Indices: 1 à {len(sequence)}")
        
        self.convert_python_to_julia_sequence(sequence)
        Main.eval(f"""
        try
            element = julia_sequence[{julia_pos}]
            println("   Élément à position {julia_pos}: ", element)
        catch BoundsError
            println("   Position {julia_pos}: HORS LIMITES")
        end
        """)
        
        # Calcul entropie avec les deux systèmes
        if position < len(sequence):
            results = self.calculate_entropy_at_position(sequence, position)
            print(f"\n📊 RÉSULTATS ENTROPIQUES:")
            print(f"   Shannon: {results['shannon_entropy']:.4f} bits")
            print(f"   AEP: {results['aep_entropy']:.4f} bits")
            print(f"   Conditionnelle: {results['conditional_entropy']:.4f} bits")
            print(f"   Métrique: {results['metric_entropy']:.4f} bits")

def demo_one_based_architecture():
    """
    🎬 DÉMONSTRATION - Architecture one-based
    
    Démontre l'utilisation de l'architecture hybride avec indexation one-based.
    """
    print("🎬 DÉMONSTRATION ARCHITECTURE HYBRIDE ONE-BASED")
    print("=" * 60)
    
    # Initialisation du pont
    bridge = JuliaPythonBridge()
    
    # Séquence test INDEX5
    test_sequence = [
        "1_A_BANKER", "0_B_PLAYER", "1_C_TIE", "0_A_BANKER", "1_B_PLAYER",
        "0_C_BANKER", "1_A_PLAYER", "0_B_TIE", "1_C_BANKER", "0_A_PLAYER"
    ]
    
    print(f"\n📊 SÉQUENCE TEST:")
    for i, val in enumerate(test_sequence):
        print(f"   Python[{i}] = Julia[{i+1}] = {val}")
    
    # Test calcul à une position
    test_position = 5  # Python 0-based
    print(f"\n🧪 TEST CALCUL À POSITION {test_position}")
    results = bridge.calculate_entropy_at_position(test_sequence, test_position)
    
    print(f"\n📊 RÉSULTATS:")
    for key, value in results.items():
        print(f"   {key}: {value}")
    
    # Comparaison systèmes d'indexation
    bridge.compare_indexing_systems(test_sequence, test_position)
    
    print(f"\n🎉 DÉMONSTRATION TERMINÉE")
    print(f"✅ Architecture hybride Julia-Python opérationnelle")
    print(f"🎯 Tous les calculs utilisent l'indexation ONE-BASED")

if __name__ == "__main__":
    demo_one_based_architecture()
