"""
JuliaMetricsCalculator.jl - Module Julia pour métriques INDEX5 spécialisées
PHASE 3.2 PRIORITÉ 2 selon plan.txt (lignes 823-827)

Migré depuis INDEX5Calculator (lignes 1167-1716) avec indexation 1-based
"""

module JuliaMetricsCalculator

using ..JuliaEntropyCore

export calculate_context_predictability_one_based, count_pattern_occurrences_one_based,
       calculate_pattern_strength_one_based, calculate_entropy_stability_score_one_based,
       calculate_compression_score_one_based, calculate_structural_richness_score_one_based,
       calculate_bayesian_divergence_score_one_based, calculate_conditional_entropy_context_one_based,
       calculate_multi_algorithm_consensus_score_one_based, calculate_deterministic_pattern_score_one_based,
       find_pattern_continuations_one_based, calculate_bayesian_theoretical_alignment_one_based,
       calculate_transition_matrix_entropy_one_based, calculate_frequency_stability_score_one_based,
       calculate_all_metrics_one_based, calculate_simulated_metrics_one_based

"""
Calcule le niveau de prédictibilité contextuelle basé sur l'entropie conditionnelle
Migré depuis calculate_context_predictability.txt (lignes 1210-1235)
"""
function calculate_context_predictability_one_based(sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64
    if length(sequence_history) < 5
        return 0.0
    end

    # 1. Score basé sur l'entropie conditionnelle (plus faible = plus prévisible)
    # CORRECTION AEP: Nouvelle normalisation basée sur les vraies plages observées
    conditional_entropy = get(current_metrics, "conditional_entropy", 6.2192)
    entropy_score = max(0.0, (6.2192 - conditional_entropy) / 6.2192)  # Normalisation corrigée

    # 2. Score basé sur la répétition des patterns récents
    recent_pattern = sequence_history[end-4:end]  # 5 derniers éléments (indexation 1-based)
    pattern_matches = count_pattern_occurrences_one_based(recent_pattern, sequence_history)
    pattern_score = min(pattern_matches / 10.0, 1.0)  # Normalisation sur 10 occurrences max

    # 3. Score basé sur le taux de répétition
    repetition_rate = get(current_metrics, "repetition_rate", 0.0)
    repetition_score = min(repetition_rate * 5, 1.0)  # Normalisation

    # 4. Score composite pondéré
    context_predictability = (0.5 * entropy_score + 0.3 * pattern_score + 0.2 * repetition_score)

    return round(context_predictability, digits=4)
end

"""
Compte les occurrences de patterns dans une séquence
Migré depuis count_pattern_occurrences.txt
"""
function count_pattern_occurrences_one_based(sequence::Vector{String}, pattern::Vector{String})::Int64
    if length(pattern) == 0 || length(sequence) < length(pattern)
        return 0
    end
    
    count = 0
    pattern_length = length(pattern)
    
    for i in 1:(length(sequence) - pattern_length + 1)  # Indexation 1-based
        if sequence[i:(i+pattern_length-1)] == pattern
            count += 1
        end
    end
    
    return count
end

"""
Calcule la force d'un pattern dans une séquence
Migré depuis calculate_pattern_strength.txt
"""
function calculate_pattern_strength_one_based(sequence::Vector{String}, pattern::Vector{String})::Float64
    if length(pattern) == 0 || length(sequence) < length(pattern)
        return 0.0
    end
    
    occurrences = count_pattern_occurrences_one_based(sequence, pattern)
    max_possible = length(sequence) - length(pattern) + 1
    
    return max_possible > 0 ? occurrences / max_possible : 0.0
end

"""
Calcule le score de stabilité entropique
Migré depuis calculate_entropy_stability_score.txt
"""
function calculate_entropy_stability_score_one_based(sequence::Vector{String}, window_size::Int64=10)::Float64
    if length(sequence) < window_size * 2
        return 0.0
    end
    
    entropies = Float64[]
    
    # Calculer l'entropie pour chaque fenêtre glissante
    for i in 1:(length(sequence) - window_size + 1)  # Indexation 1-based
        window = sequence[i:(i+window_size-1)]
        
        # Calculer fréquences dans la fenêtre
        counts = Dict{String, Int64}()
        for symbol in window
            counts[symbol] = get(counts, symbol, 0) + 1
        end
        
        # Calculer entropie de Shannon
        probs = [count / window_size for count in values(counts)]
        entropy = calculate_shannon_entropy_one_based(probs)
        push!(entropies, entropy)
    end
    
    # Calculer la stabilité (inverse de la variance)
    if length(entropies) < 2
        return 0.0
    end
    
    mean_entropy = sum(entropies) / length(entropies)
    variance = sum((e - mean_entropy)^2 for e in entropies) / length(entropies)
    
    return variance > 0 ? 1.0 / (1.0 + variance) : 1.0
end

"""
Calcule le score de compression de la séquence
Migré depuis calculate_compression_score.txt
"""
function calculate_compression_score_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end
    
    # Approximation simple de la compression (ratio de répétitions)
    unique_elements = length(Set(sequence))
    total_elements = length(sequence)
    
    compression_ratio = unique_elements / total_elements
    return 1.0 - compression_ratio  # Plus de répétitions = plus de compression
end

"""
Calcule le score de richesse structurelle
Migré depuis calculate_structural_richness_score.txt
"""
function calculate_structural_richness_score_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 3
        return 0.0
    end
    
    # Analyser les transitions uniques
    transitions = Set{Tuple{String, String}}()
    for i in 1:(length(sequence)-1)  # Indexation 1-based
        push!(transitions, (sequence[i], sequence[i+1]))
    end
    
    # Analyser les triplets uniques
    triplets = Set{Tuple{String, String, String}}()
    for i in 1:(length(sequence)-2)  # Indexation 1-based
        push!(triplets, (sequence[i], sequence[i+1], sequence[i+2]))
    end
    
    # Score basé sur la diversité des structures
    max_transitions = length(sequence) - 1
    max_triplets = length(sequence) - 2
    
    transition_diversity = length(transitions) / max_transitions
    triplet_diversity = length(triplets) / max_triplets
    
    return (transition_diversity + triplet_diversity) / 2.0
end

"""
Calcule la divergence entre les probabilités observées et théoriques INDEX5
Migré depuis calculate_bayesian_divergence_score.txt (lignes 1369-1398)
"""
function calculate_bayesian_divergence_score_one_based(sequence_history::Vector{String})::Float64
    if length(sequence_history) < 10
        return 0.0
    end

    # 1. Calculer les fréquences observées
    observed_counts = Dict{String, Int64}()
    for symbol in sequence_history
        observed_counts[symbol] = get(observed_counts, symbol, 0) + 1
    end
    total_observations = length(sequence_history)

    # 2. Calculer la divergence KL: D_KL(P_obs || P_theo) = Σ P_obs(x) log(P_obs(x) / P_theo(x))
    kl_divergence = 0.0

    for (index5_value, p_theoretical) in THEORETICAL_PROBS_ONE_BASED
        p_observed = get(observed_counts, index5_value, 0) / total_observations

        if p_observed > 0  # Éviter log(0)
            kl_divergence += p_observed * log2(p_observed / p_theoretical)
        end
    end

    # 3. Normaliser le score (divergence KL peut être très élevée)
    normalized_score = min(kl_divergence / 5.0, 1.0)  # Normalisation empirique

    return round(normalized_score, digits=4)
end

"""
Calcule l'entropie conditionnelle dans un contexte spécifique
Migré depuis calculate_conditional_entropy_context.txt
"""
function calculate_conditional_entropy_context_one_based(sequence::Vector{String}, context_length::Int64=2)::Float64
    if length(sequence) < context_length + 1
        return 0.0
    end

    # Construire les paires (contexte, symbole suivant)
    context_symbol_pairs = Tuple{String, String}[]

    for i in 1:(length(sequence) - context_length)  # Indexation 1-based
        context = join(sequence[i:(i+context_length-1)], "_")
        next_symbol = sequence[i+context_length]
        push!(context_symbol_pairs, (context, next_symbol))
    end

    # Calculer l'entropie conditionnelle H(X|Y)
    context_counts = Dict{String, Int64}()
    pair_counts = Dict{Tuple{String, String}, Int64}()

    for (context, symbol) in context_symbol_pairs
        context_counts[context] = get(context_counts, context, 0) + 1
        pair_counts[(context, symbol)] = get(pair_counts, (context, symbol), 0) + 1
    end

    total_pairs = length(context_symbol_pairs)
    conditional_entropy = 0.0

    for context in keys(context_counts)
        p_context = context_counts[context] / total_pairs

        # Calculer H(X|context)
        context_entropy = 0.0
        for symbol in Set([pair[2] for pair in keys(pair_counts) if pair[1] == context])
            pair_count = get(pair_counts, (context, symbol), 0)
            if pair_count > 0
                p_symbol_given_context = pair_count / context_counts[context]
                context_entropy -= p_symbol_given_context * log2(p_symbol_given_context)
            end
        end

        conditional_entropy += p_context * context_entropy
    end

    return conditional_entropy
end

"""
Calcule le score de consensus multi-algorithmes
Migré depuis calculate_multi_algorithm_consensus_score.txt
"""
function calculate_multi_algorithm_consensus_score_one_based(predictions::Vector{String})::Float64
    if length(predictions) < 2
        return 0.0
    end

    # Compter les occurrences de chaque prédiction
    prediction_counts = Dict{String, Int64}()
    for pred in predictions
        prediction_counts[pred] = get(prediction_counts, pred, 0) + 1
    end

    # Calculer le score de consensus (proportion de la prédiction majoritaire)
    max_count = maximum(values(prediction_counts))
    consensus_score = max_count / length(predictions)

    return round(consensus_score, digits=4)
end

"""
Calcule le score de pattern déterministe
Migré depuis calculate_deterministic_pattern_score.txt
"""
function calculate_deterministic_pattern_score_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 4
        return 0.0
    end

    # Analyser les patterns déterministes (séquences répétitives)
    deterministic_score = 0.0
    pattern_lengths = [2, 3, 4]

    for pattern_len in pattern_lengths
        if length(sequence) >= pattern_len * 2
            # Chercher des répétitions de patterns
            for i in 1:(length(sequence) - pattern_len * 2 + 1)  # Indexation 1-based
                pattern1 = sequence[i:(i+pattern_len-1)]
                pattern2 = sequence[(i+pattern_len):(i+2*pattern_len-1)]

                if pattern1 == pattern2
                    deterministic_score += 1.0 / pattern_len  # Pondération inverse de la longueur
                end
            end
        end
    end

    # Normaliser par la longueur de la séquence
    max_possible_score = length(sequence) / 2.0
    normalized_score = min(deterministic_score / max_possible_score, 1.0)

    return round(normalized_score, digits=4)
end

"""
Calcule la divergence entre les probabilités observées et théoriques INDEX5
Migré depuis calculate_bayesian_divergence_score.txt (lignes 1369-1398)
"""
function calculate_bayesian_divergence_score_one_based(sequence_history::Vector{String})::Float64
    if length(sequence_history) < 10
        return 0.0
    end

    # 1. Calculer les fréquences observées
    observed_counts = Dict{String, Int64}()
    for symbol in sequence_history
        observed_counts[symbol] = get(observed_counts, symbol, 0) + 1
    end
    total_observations = length(sequence_history)

    # 2. Calculer la divergence KL: D_KL(P_obs || P_theo) = Σ P_obs(x) log(P_obs(x) / P_theo(x))
    kl_divergence = 0.0

    for (index5_value, p_theoretical) in THEORETICAL_PROBS_ONE_BASED
        p_observed = get(observed_counts, index5_value, 0) / total_observations

        if p_observed > 0  # Éviter log(0)
            kl_divergence += p_observed * log2(p_observed / p_theoretical)
        end
    end

    # 3. Normaliser le score (la divergence KL peut être très élevée)
    # Utiliser une fonction sigmoïde pour normaliser entre 0 et 1
    normalized_score = 2 / (1 + exp(-kl_divergence)) - 1

    return round(max(0.0, normalized_score), digits=4)
end

"""
Calcule l'entropie conditionnelle pour différents contextes
Migré depuis calculate_conditional_entropy_context.txt (lignes 1400-1446)
"""
function calculate_conditional_entropy_context_one_based(sequence_history::Vector{String}, context_length::Int64=3)::Float64
    if length(sequence_history) <= context_length
        return 0.0
    end

    # Analyser les transitions depuis les contextes de longueur donnée
    context_transitions = Dict{Vector{String}, Dict{String, Int64}}()

    for i in (context_length+1):length(sequence_history)  # Indexation 1-based
        context = sequence_history[(i-context_length):(i-1)]
        next_value = sequence_history[i]

        if !haskey(context_transitions, context)
            context_transitions[context] = Dict{String, Int64}()
        end
        context_transitions[context][next_value] = get(context_transitions[context], next_value, 0) + 1
    end

    # Calculer l'entropie conditionnelle H(X|Context)
    total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))

    if total_transitions == 0
        return 0.0
    end

    conditional_entropy = 0.0

    for (context, transitions) in context_transitions
        context_total = sum(values(transitions))
        context_prob = context_total / total_transitions

        # CORRECTION AEP: Entropie pour ce contexte spécifique selon AEP
        # Créer la séquence des valeurs suivantes pour ce contexte
        context_sequence = String[]
        for (next_value, count) in transitions
            append!(context_sequence, fill(next_value, count))
        end

        # Calculer l'entropie AEP pour ce contexte
        context_entropy = calculate_sequence_entropy_aep_one_based(context_sequence)

        # Pondérer par la probabilité du contexte
        conditional_entropy += context_prob * context_entropy
    end

    return round(conditional_entropy, digits=4)
end

"""
Calcule un score de consensus entre différents algorithmes d'analyse
Migré depuis calculate_multi_algorithm_consensus_score.txt (lignes 1448-1487)
"""
function calculate_multi_algorithm_consensus_score_one_based(sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Float64
    if isempty(sequence_history) || length(sequence_history) < 5
        return 0.0
    end

    # 1. Calculer différents scores avec les méthodes disponibles
    scores = Dict{String, Float64}()

    # Score de prédictibilité contextuelle
    context_score = calculate_context_predictability_one_based(sequence_history, all_metrics)
    scores["context"] = context_score

    # Score de force des patterns (utiliser pattern récent)
    recent_pattern = sequence_history[max(1, end-4):end]
    pattern_score = calculate_pattern_strength_one_based(sequence_history, recent_pattern)
    scores["pattern"] = pattern_score

    # Score de compression
    compression_score = calculate_compression_score_one_based(sequence_history)
    scores["compression"] = compression_score

    # Score de divergence bayésienne (inverser pour cohérence)
    divergence_score = calculate_bayesian_divergence_score_one_based(sequence_history)
    scores["bayesian"] = 1.0 - divergence_score

    # 2. Calculer la variance des scores (faible variance = consensus élevé)
    score_values = collect(values(scores))
    if length(score_values) < 2
        return 0.0
    end

    mean_score = sum(score_values) / length(score_values)
    variance = sum((score - mean_score)^2 for score in score_values) / length(score_values)

    # 3. Convertir en score de consensus (faible variance = consensus élevé)
    consensus_score = 1.0 / (1.0 + variance * 10)  # Normalisation

    return round(consensus_score, digits=4)
end

"""
Calcule un score de déterminisme basé sur la récurrence des patterns
Migré depuis calculate_deterministic_pattern_score.txt (lignes 1489-1522)
"""
function calculate_deterministic_pattern_score_one_based(sequence_history::Vector{String})::Float64
    if length(sequence_history) < 4
        return 0.0
    end

    pattern_scores = Float64[]

    # Analyser patterns de longueur 2 à 5
    for pattern_length in 2:min(5, length(sequence_history))
        current_pattern = sequence_history[end-pattern_length+1:end]

        # Chercher ce pattern dans l'historique (excluant la fin)
        search_sequence = sequence_history[1:end-pattern_length]
        continuations = find_pattern_continuations_one_based(current_pattern, search_sequence)

        if !isempty(continuations)
            # Calculer la prévisibilité de ce pattern
            total_continuations = sum(values(continuations))
            max_continuation = maximum(values(continuations))

            # Score = (fréquence max / total) * poids de longueur
            pattern_predictability = max_continuation / total_continuations
            length_weight = pattern_length / 5.0  # Normalisation

            pattern_score = pattern_predictability * length_weight
            push!(pattern_scores, pattern_score)
        end
    end

    if !isempty(pattern_scores)
        # Retourner le score maximum (meilleur pattern déterministe)
        return round(maximum(pattern_scores), digits=4)
    end

    return 0.0
end

"""
Trouve toutes les continuations d'un pattern dans l'historique
Migré depuis find_pattern_continuations.txt (lignes 1524-1538)
"""
function find_pattern_continuations_one_based(pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int64}
    continuations = Dict{String, Int64}()
    pattern_len = length(pattern)

    for i in 1:(length(sequence_history) - pattern_len + 1)  # Indexation 1-based
        if sequence_history[i:(i+pattern_len-1)] == pattern
            # Si il y a une continuation après ce pattern
            if i + pattern_len <= length(sequence_history)
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = get(continuations, next_value, 0) + 1
            end
        end
    end

    return continuations
end

"""
Calcule l'alignement entre les observations récentes et les probabilités théoriques
Migré depuis calculate_bayesian_theoretical_alignment.txt (lignes 1540-1582)
"""
function calculate_bayesian_theoretical_alignment_one_based(sequence_history::Vector{String})::Float64
    if length(sequence_history) < 10
        return 0.0
    end

    # Analyser les 20 dernières observations
    recent_sequence = length(sequence_history) >= 20 ? sequence_history[end-19:end] : sequence_history
    observed_freq = Dict{String, Int64}()
    for symbol in recent_sequence
        observed_freq[symbol] = get(observed_freq, symbol, 0) + 1
    end

    # Calculer le score d'alignement bayésien
    alignment_scores = Float64[]

    for (index5_value, p_theoretical) in THEORETICAL_PROBS_ONE_BASED
        observed_count = get(observed_freq, index5_value, 0)
        p_observed = observed_count / length(recent_sequence)

        # Score d'alignement pour cette valeur (1 - différence relative)
        if p_theoretical > 0
            relative_diff = abs(p_observed - p_theoretical) / p_theoretical
            alignment_score = max(0.0, 1.0 - relative_diff)
            push!(alignment_scores, alignment_score)
        end
    end

    if !isempty(alignment_scores)
        # Score moyen pondéré par les probabilités théoriques
        weighted_alignment = 0.0
        total_weight = 0.0

        i = 1
        for (index5_value, p_theoretical) in THEORETICAL_PROBS_ONE_BASED
            if i <= length(alignment_scores)
                weighted_alignment += alignment_scores[i] * p_theoretical
                total_weight += p_theoretical
                i += 1
            end
        end

        if total_weight > 0
            return round(weighted_alignment / total_weight, digits=4)
        end
    end

    return 0.0
end

"""
Calcule l'entropie de la matrice de transitions INDEX5
Migré depuis calculate_transition_matrix_entropy.txt (lignes 1584-1629)
"""
function calculate_transition_matrix_entropy_one_based(sequence_history::Vector{String})::Float64
    if length(sequence_history) < 3
        return 0.0
    end

    # Construire la matrice de transitions
    transitions = Dict{String, Dict{String, Int64}}()

    for i in 1:(length(sequence_history) - 1)  # Indexation 1-based
        current = sequence_history[i]
        next_val = sequence_history[i + 1]

        if !haskey(transitions, current)
            transitions[current] = Dict{String, Int64}()
        end
        transitions[current][next_val] = get(transitions[current], next_val, 0) + 1
    end

    # Calculer l'entropie de chaque ligne de la matrice
    total_entropy = 0.0
    total_weight = 0.0

    for (current_state, next_states) in transitions
        state_total = sum(values(next_states))
        state_weight = state_total / (length(sequence_history) - 1)

        # CORRECTION AEP: Entropie pour cet état selon AEP
        # Créer la séquence des états suivants pour cet état
        state_sequence = String[]
        for (next_state, count) in next_states
            append!(state_sequence, fill(next_state, count))
        end

        # Calculer l'entropie AEP pour cet état
        state_entropy = calculate_sequence_entropy_aep_one_based(state_sequence)

        total_entropy += state_entropy * state_weight
        total_weight += state_weight
    end

    if total_weight > 0
        return round(total_entropy / total_weight, digits=4)
    end

    return 0.0
end

"""
Calcule un score de stabilité des fréquences INDEX5
Migré depuis calculate_frequency_stability_score.txt (lignes 1631-1667)
"""
function calculate_frequency_stability_score_one_based(sequence_history::Vector{String})::Float64
    if length(sequence_history) < 20
        return 0.0
    end

    # Fréquences globales
    global_freq = Dict{String, Int64}()
    for symbol in sequence_history
        global_freq[symbol] = get(global_freq, symbol, 0) + 1
    end
    global_total = length(sequence_history)

    # Fréquences récentes (30 dernières observations)
    recent_sequence = length(sequence_history) >= 30 ? sequence_history[end-29:end] : sequence_history[end-div(length(sequence_history),2)+1:end]
    recent_freq = Dict{String, Int64}()
    for symbol in recent_sequence
        recent_freq[symbol] = get(recent_freq, symbol, 0) + 1
    end
    recent_total = length(recent_sequence)

    # Calculer la stabilité pour chaque valeur INDEX5
    stability_scores = Float64[]

    for index5_value in keys(THEORETICAL_PROBS_ONE_BASED)
        global_prob = get(global_freq, index5_value, 0) / global_total
        recent_prob = get(recent_freq, index5_value, 0) / recent_total

        # Score de stabilité = 1 - différence relative
        if global_prob > 0
            relative_diff = abs(recent_prob - global_prob) / global_prob
            stability_score = max(0.0, 1.0 - relative_diff)
            push!(stability_scores, stability_score)
        end
    end

    if !isempty(stability_scores)
        return round(sum(stability_scores) / length(stability_scores), digits=4)
    end

    return 0.0
end

"""
Calcule toutes les métriques disponibles pour une position donnée
Migré depuis calculate_all_metrics.txt (lignes 1669-1716)
"""
function calculate_all_metrics_one_based(sequence_history::Vector{String}, current_metrics::Dict{String, Any}, entropy_evolution::Vector{Dict{String, Any}})::Dict{String, Any}
    if length(sequence_history) < 2
        return Dict{String, Any}()
    end

    metrics = Dict{String, Any}()

    # 1. Prédictibilité contextuelle
    metrics["context_predictability"] = calculate_context_predictability_one_based(sequence_history, current_metrics)

    # 2. Force des patterns (utiliser pattern récent)
    recent_pattern = sequence_history[max(1, end-4):end]
    metrics["pattern_strength"] = calculate_pattern_strength_one_based(sequence_history, recent_pattern)

    # 3. Score de stabilité entropique
    if !isempty(entropy_evolution)
        # Extraire les entropies pour le calcul de stabilité
        entropies = [get(evo, "simple_entropy", 0.0) for evo in entropy_evolution]
        metrics["entropy_stability"] = calculate_entropy_stability_score_one_based(sequence_history, 10)
    end

    # 4. Score de compression
    metrics["compression_score"] = calculate_compression_score_one_based(sequence_history)

    # 5. Richesse structurelle
    metrics["structural_richness"] = calculate_structural_richness_score_one_based(sequence_history)

    # 6. Divergence bayésienne
    metrics["bayesian_divergence"] = calculate_bayesian_divergence_score_one_based(sequence_history)

    # 7. Entropie conditionnelle contextuelle
    metrics["conditional_entropy_context"] = calculate_conditional_entropy_context_one_based(sequence_history)

    # 8. Consensus multi-algorithmes
    metrics["multi_algorithm_consensus"] = calculate_multi_algorithm_consensus_score_one_based(sequence_history, current_metrics)

    # 9. Score de patterns déterministes
    metrics["deterministic_pattern_score"] = calculate_deterministic_pattern_score_one_based(sequence_history)

    # 10. Alignement bayésien théorique
    metrics["bayesian_theoretical_alignment"] = calculate_bayesian_theoretical_alignment_one_based(sequence_history)

    # 11. Entropie de la matrice de transitions
    metrics["transition_matrix_entropy"] = calculate_transition_matrix_entropy_one_based(sequence_history)

    # 12. Stabilité des fréquences
    metrics["frequency_stability"] = calculate_frequency_stability_score_one_based(sequence_history)

    return metrics
end

"""
Calcule les métriques pour une séquence simulée avec possible_index5 ajouté
Migré depuis calculate_simulated_metrics.txt (lignes 2711-2756)
"""
function calculate_simulated_metrics_one_based(sequence::Vector{String}, position::Int64, possible_index5::String)::Dict{String, Any}
    if position >= length(sequence)
        return Dict{String, Any}()
    end

    # Créer séquence simulée avec la valeur possible ajoutée
    simulated_sequence = vcat(sequence[1:position+1], [possible_index5])

    # Calculer les métriques de base pour la nouvelle position
    metrics = Dict{String, Any}()

    try
        # Entropie conditionnelle
        if length(simulated_sequence) >= 2
            metrics["conditional_entropy"] = calculate_conditional_entropy_one_based(simulated_sequence)
        else
            metrics["conditional_entropy"] = 0.0
        end

        # Entropie simple (Shannon) - approximation avec fréquences
        freq_counts = Dict{String, Int64}()
        for symbol in simulated_sequence
            freq_counts[symbol] = get(freq_counts, symbol, 0) + 1
        end
        probs = [count / length(simulated_sequence) for count in values(freq_counts)]
        metrics["simple_entropy"] = calculate_shannon_entropy_one_based(probs)

        # Entropie théorique (AEP)
        metrics["simple_entropy_theoretical"] = calculate_sequence_entropy_aep_one_based(simulated_sequence)

        # Taux d'entropie (approximation)
        if length(simulated_sequence) >= 3
            block_entropies = calculate_block_entropies_one_based(simulated_sequence, min(5, length(simulated_sequence)))
            if !isempty(block_entropies)
                metrics["entropy_rate"] = block_entropies[end]
            else
                metrics["entropy_rate"] = metrics["simple_entropy"]
            end
        else
            metrics["entropy_rate"] = metrics["simple_entropy"]
        end

    catch e
        # En cas d'erreur, retourner des valeurs par défaut
        metrics = Dict{String, Any}(
            "conditional_entropy" => 0.0,
            "simple_entropy" => 0.0,
            "simple_entropy_theoretical" => 0.0,
            "entropy_rate" => 0.0
        )
    end

    return metrics
end

end # module JuliaMetricsCalculator
