"""
JuliaMetricsCalculator.jl - Module Julia pour métriques INDEX5 spécialisées
PHASE 3.2 PRIORITÉ 2 selon plan.txt (lignes 823-827)

Migré depuis INDEX5Calculator (lignes 1167-1716) avec indexation 1-based
"""

module JuliaMetricsCalculator

using ..JuliaEntropyCore

export calculate_context_predictability_one_based, count_pattern_occurrences_one_based,
       calculate_pattern_strength_one_based, calculate_entropy_stability_score_one_based,
       calculate_compression_score_one_based, calculate_structural_richness_score_one_based,
       calculate_bayesian_divergence_score_one_based, calculate_conditional_entropy_context_one_based,
       calculate_multi_algorithm_consensus_score_one_based, calculate_deterministic_pattern_score_one_based,
       find_pattern_continuations_one_based, calculate_bayesian_theoretical_alignment_one_based,
       calculate_transition_matrix_entropy_one_based, calculate_frequency_stability_score_one_based,
       calculate_all_metrics_one_based, calculate_simulated_metrics_one_based

"""
Calcule la prédictibilité contextuelle d'une séquence
Migré depuis calculate_context_predictability.txt
"""
function calculate_context_predictability_one_based(sequence::Vector{String}, context_length::Int64=3)::Float64
    if length(sequence) < context_length + 1
        return 0.0
    end
    
    # Construire les contextes et leurs continuations
    context_continuations = Dict{String, Dict{String, Int64}}()
    
    for i in 1:(length(sequence) - context_length)  # Indexation 1-based
        context = join(sequence[i:(i+context_length-1)], "_")
        continuation = sequence[i+context_length]
        
        if !haskey(context_continuations, context)
            context_continuations[context] = Dict{String, Int64}()
        end
        
        context_continuations[context][continuation] = get(context_continuations[context], continuation, 0) + 1
    end
    
    # Calculer la prédictibilité moyenne
    total_predictability = 0.0
    total_contexts = 0
    
    for (context, continuations) in context_continuations
        total_count = sum(values(continuations))
        max_count = maximum(values(continuations))
        predictability = max_count / total_count
        
        total_predictability += predictability
        total_contexts += 1
    end
    
    return total_contexts > 0 ? total_predictability / total_contexts : 0.0
end

"""
Compte les occurrences de patterns dans une séquence
Migré depuis count_pattern_occurrences.txt
"""
function count_pattern_occurrences_one_based(sequence::Vector{String}, pattern::Vector{String})::Int64
    if length(pattern) == 0 || length(sequence) < length(pattern)
        return 0
    end
    
    count = 0
    pattern_length = length(pattern)
    
    for i in 1:(length(sequence) - pattern_length + 1)  # Indexation 1-based
        if sequence[i:(i+pattern_length-1)] == pattern
            count += 1
        end
    end
    
    return count
end

"""
Calcule la force d'un pattern dans une séquence
Migré depuis calculate_pattern_strength.txt
"""
function calculate_pattern_strength_one_based(sequence::Vector{String}, pattern::Vector{String})::Float64
    if length(pattern) == 0 || length(sequence) < length(pattern)
        return 0.0
    end
    
    occurrences = count_pattern_occurrences_one_based(sequence, pattern)
    max_possible = length(sequence) - length(pattern) + 1
    
    return max_possible > 0 ? occurrences / max_possible : 0.0
end

"""
Calcule le score de stabilité entropique
Migré depuis calculate_entropy_stability_score.txt
"""
function calculate_entropy_stability_score_one_based(sequence::Vector{String}, window_size::Int64=10)::Float64
    if length(sequence) < window_size * 2
        return 0.0
    end
    
    entropies = Float64[]
    
    # Calculer l'entropie pour chaque fenêtre glissante
    for i in 1:(length(sequence) - window_size + 1)  # Indexation 1-based
        window = sequence[i:(i+window_size-1)]
        
        # Calculer fréquences dans la fenêtre
        counts = Dict{String, Int64}()
        for symbol in window
            counts[symbol] = get(counts, symbol, 0) + 1
        end
        
        # Calculer entropie de Shannon
        probs = [count / window_size for count in values(counts)]
        entropy = calculate_shannon_entropy_one_based(probs)
        push!(entropies, entropy)
    end
    
    # Calculer la stabilité (inverse de la variance)
    if length(entropies) < 2
        return 0.0
    end
    
    mean_entropy = sum(entropies) / length(entropies)
    variance = sum((e - mean_entropy)^2 for e in entropies) / length(entropies)
    
    return variance > 0 ? 1.0 / (1.0 + variance) : 1.0
end

"""
Calcule le score de compression de la séquence
Migré depuis calculate_compression_score.txt
"""
function calculate_compression_score_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end
    
    # Approximation simple de la compression (ratio de répétitions)
    unique_elements = length(Set(sequence))
    total_elements = length(sequence)
    
    compression_ratio = unique_elements / total_elements
    return 1.0 - compression_ratio  # Plus de répétitions = plus de compression
end

"""
Calcule le score de richesse structurelle
Migré depuis calculate_structural_richness_score.txt
"""
function calculate_structural_richness_score_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 3
        return 0.0
    end
    
    # Analyser les transitions uniques
    transitions = Set{Tuple{String, String}}()
    for i in 1:(length(sequence)-1)  # Indexation 1-based
        push!(transitions, (sequence[i], sequence[i+1]))
    end
    
    # Analyser les triplets uniques
    triplets = Set{Tuple{String, String, String}}()
    for i in 1:(length(sequence)-2)  # Indexation 1-based
        push!(triplets, (sequence[i], sequence[i+1], sequence[i+2]))
    end
    
    # Score basé sur la diversité des structures
    max_transitions = length(sequence) - 1
    max_triplets = length(sequence) - 2
    
    transition_diversity = length(transitions) / max_transitions
    triplet_diversity = length(triplets) / max_triplets
    
    return (transition_diversity + triplet_diversity) / 2.0
end

end # module JuliaMetricsCalculator
