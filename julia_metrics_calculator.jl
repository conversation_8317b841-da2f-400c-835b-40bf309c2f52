"""
JuliaMetricsCalculator.jl - Module Julia pour métriques INDEX5 spécialisées
PHASE 3.2 PRIORITÉ 2 selon plan.txt (lignes 823-827)

Migré depuis INDEX5Calculator (lignes 1167-1716) avec indexation 1-based
"""

module JuliaMetricsCalculator

using ..JuliaEntropyCore

export calculate_context_predictability_one_based, count_pattern_occurrences_one_based,
       calculate_pattern_strength_one_based, calculate_entropy_stability_score_one_based,
       calculate_compression_score_one_based, calculate_structural_richness_score_one_based,
       calculate_bayesian_divergence_score_one_based, calculate_conditional_entropy_context_one_based,
       calculate_multi_algorithm_consensus_score_one_based, calculate_deterministic_pattern_score_one_based,
       find_pattern_continuations_one_based, calculate_bayesian_theoretical_alignment_one_based,
       calculate_transition_matrix_entropy_one_based, calculate_frequency_stability_score_one_based,
       calculate_all_metrics_one_based, calculate_simulated_metrics_one_based

"""
Calcule la prédictibilité contextuelle d'une séquence
Migré depuis calculate_context_predictability.txt
"""
function calculate_context_predictability_one_based(sequence::Vector{String}, context_length::Int64=3)::Float64
    if length(sequence) < context_length + 1
        return 0.0
    end
    
    # Construire les contextes et leurs continuations
    context_continuations = Dict{String, Dict{String, Int64}}()
    
    for i in 1:(length(sequence) - context_length)  # Indexation 1-based
        context = join(sequence[i:(i+context_length-1)], "_")
        continuation = sequence[i+context_length]
        
        if !haskey(context_continuations, context)
            context_continuations[context] = Dict{String, Int64}()
        end
        
        context_continuations[context][continuation] = get(context_continuations[context], continuation, 0) + 1
    end
    
    # Calculer la prédictibilité moyenne
    total_predictability = 0.0
    total_contexts = 0
    
    for (context, continuations) in context_continuations
        total_count = sum(values(continuations))
        max_count = maximum(values(continuations))
        predictability = max_count / total_count
        
        total_predictability += predictability
        total_contexts += 1
    end
    
    return total_contexts > 0 ? total_predictability / total_contexts : 0.0
end

"""
Compte les occurrences de patterns dans une séquence
Migré depuis count_pattern_occurrences.txt
"""
function count_pattern_occurrences_one_based(sequence::Vector{String}, pattern::Vector{String})::Int64
    if length(pattern) == 0 || length(sequence) < length(pattern)
        return 0
    end
    
    count = 0
    pattern_length = length(pattern)
    
    for i in 1:(length(sequence) - pattern_length + 1)  # Indexation 1-based
        if sequence[i:(i+pattern_length-1)] == pattern
            count += 1
        end
    end
    
    return count
end

"""
Calcule la force d'un pattern dans une séquence
Migré depuis calculate_pattern_strength.txt
"""
function calculate_pattern_strength_one_based(sequence::Vector{String}, pattern::Vector{String})::Float64
    if length(pattern) == 0 || length(sequence) < length(pattern)
        return 0.0
    end
    
    occurrences = count_pattern_occurrences_one_based(sequence, pattern)
    max_possible = length(sequence) - length(pattern) + 1
    
    return max_possible > 0 ? occurrences / max_possible : 0.0
end

"""
Calcule le score de stabilité entropique
Migré depuis calculate_entropy_stability_score.txt
"""
function calculate_entropy_stability_score_one_based(sequence::Vector{String}, window_size::Int64=10)::Float64
    if length(sequence) < window_size * 2
        return 0.0
    end
    
    entropies = Float64[]
    
    # Calculer l'entropie pour chaque fenêtre glissante
    for i in 1:(length(sequence) - window_size + 1)  # Indexation 1-based
        window = sequence[i:(i+window_size-1)]
        
        # Calculer fréquences dans la fenêtre
        counts = Dict{String, Int64}()
        for symbol in window
            counts[symbol] = get(counts, symbol, 0) + 1
        end
        
        # Calculer entropie de Shannon
        probs = [count / window_size for count in values(counts)]
        entropy = calculate_shannon_entropy_one_based(probs)
        push!(entropies, entropy)
    end
    
    # Calculer la stabilité (inverse de la variance)
    if length(entropies) < 2
        return 0.0
    end
    
    mean_entropy = sum(entropies) / length(entropies)
    variance = sum((e - mean_entropy)^2 for e in entropies) / length(entropies)
    
    return variance > 0 ? 1.0 / (1.0 + variance) : 1.0
end

"""
Calcule le score de compression de la séquence
Migré depuis calculate_compression_score.txt
"""
function calculate_compression_score_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end
    
    # Approximation simple de la compression (ratio de répétitions)
    unique_elements = length(Set(sequence))
    total_elements = length(sequence)
    
    compression_ratio = unique_elements / total_elements
    return 1.0 - compression_ratio  # Plus de répétitions = plus de compression
end

"""
Calcule le score de richesse structurelle
Migré depuis calculate_structural_richness_score.txt
"""
function calculate_structural_richness_score_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 3
        return 0.0
    end
    
    # Analyser les transitions uniques
    transitions = Set{Tuple{String, String}}()
    for i in 1:(length(sequence)-1)  # Indexation 1-based
        push!(transitions, (sequence[i], sequence[i+1]))
    end
    
    # Analyser les triplets uniques
    triplets = Set{Tuple{String, String, String}}()
    for i in 1:(length(sequence)-2)  # Indexation 1-based
        push!(triplets, (sequence[i], sequence[i+1], sequence[i+2]))
    end
    
    # Score basé sur la diversité des structures
    max_transitions = length(sequence) - 1
    max_triplets = length(sequence) - 2
    
    transition_diversity = length(transitions) / max_transitions
    triplet_diversity = length(triplets) / max_triplets
    
    return (transition_diversity + triplet_diversity) / 2.0
end

"""
Calcule la divergence entre les probabilités observées et théoriques INDEX5
Migré depuis calculate_bayesian_divergence_score.txt (lignes 1369-1398)
"""
function calculate_bayesian_divergence_score_one_based(sequence_history::Vector{String})::Float64
    if length(sequence_history) < 10
        return 0.0
    end

    # 1. Calculer les fréquences observées
    observed_counts = Dict{String, Int64}()
    for symbol in sequence_history
        observed_counts[symbol] = get(observed_counts, symbol, 0) + 1
    end
    total_observations = length(sequence_history)

    # 2. Calculer la divergence KL: D_KL(P_obs || P_theo) = Σ P_obs(x) log(P_obs(x) / P_theo(x))
    kl_divergence = 0.0

    for (index5_value, p_theoretical) in THEORETICAL_PROBS_ONE_BASED
        p_observed = get(observed_counts, index5_value, 0) / total_observations

        if p_observed > 0  # Éviter log(0)
            kl_divergence += p_observed * log2(p_observed / p_theoretical)
        end
    end

    # 3. Normaliser le score (divergence KL peut être très élevée)
    normalized_score = min(kl_divergence / 5.0, 1.0)  # Normalisation empirique

    return round(normalized_score, digits=4)
end

"""
Calcule l'entropie conditionnelle dans un contexte spécifique
Migré depuis calculate_conditional_entropy_context.txt
"""
function calculate_conditional_entropy_context_one_based(sequence::Vector{String}, context_length::Int64=2)::Float64
    if length(sequence) < context_length + 1
        return 0.0
    end

    # Construire les paires (contexte, symbole suivant)
    context_symbol_pairs = Tuple{String, String}[]

    for i in 1:(length(sequence) - context_length)  # Indexation 1-based
        context = join(sequence[i:(i+context_length-1)], "_")
        next_symbol = sequence[i+context_length]
        push!(context_symbol_pairs, (context, next_symbol))
    end

    # Calculer l'entropie conditionnelle H(X|Y)
    context_counts = Dict{String, Int64}()
    pair_counts = Dict{Tuple{String, String}, Int64}()

    for (context, symbol) in context_symbol_pairs
        context_counts[context] = get(context_counts, context, 0) + 1
        pair_counts[(context, symbol)] = get(pair_counts, (context, symbol), 0) + 1
    end

    total_pairs = length(context_symbol_pairs)
    conditional_entropy = 0.0

    for context in keys(context_counts)
        p_context = context_counts[context] / total_pairs

        # Calculer H(X|context)
        context_entropy = 0.0
        for symbol in Set([pair[2] for pair in keys(pair_counts) if pair[1] == context])
            pair_count = get(pair_counts, (context, symbol), 0)
            if pair_count > 0
                p_symbol_given_context = pair_count / context_counts[context]
                context_entropy -= p_symbol_given_context * log2(p_symbol_given_context)
            end
        end

        conditional_entropy += p_context * context_entropy
    end

    return conditional_entropy
end

"""
Calcule le score de consensus multi-algorithmes
Migré depuis calculate_multi_algorithm_consensus_score.txt
"""
function calculate_multi_algorithm_consensus_score_one_based(predictions::Vector{String})::Float64
    if length(predictions) < 2
        return 0.0
    end

    # Compter les occurrences de chaque prédiction
    prediction_counts = Dict{String, Int64}()
    for pred in predictions
        prediction_counts[pred] = get(prediction_counts, pred, 0) + 1
    end

    # Calculer le score de consensus (proportion de la prédiction majoritaire)
    max_count = maximum(values(prediction_counts))
    consensus_score = max_count / length(predictions)

    return round(consensus_score, digits=4)
end

"""
Calcule le score de pattern déterministe
Migré depuis calculate_deterministic_pattern_score.txt
"""
function calculate_deterministic_pattern_score_one_based(sequence::Vector{String})::Float64
    if length(sequence) < 4
        return 0.0
    end

    # Analyser les patterns déterministes (séquences répétitives)
    deterministic_score = 0.0
    pattern_lengths = [2, 3, 4]

    for pattern_len in pattern_lengths
        if length(sequence) >= pattern_len * 2
            # Chercher des répétitions de patterns
            for i in 1:(length(sequence) - pattern_len * 2 + 1)  # Indexation 1-based
                pattern1 = sequence[i:(i+pattern_len-1)]
                pattern2 = sequence[(i+pattern_len):(i+2*pattern_len-1)]

                if pattern1 == pattern2
                    deterministic_score += 1.0 / pattern_len  # Pondération inverse de la longueur
                end
            end
        end
    end

    # Normaliser par la longueur de la séquence
    max_possible_score = length(sequence) / 2.0
    normalized_score = min(deterministic_score / max_possible_score, 1.0)

    return round(normalized_score, digits=4)
end

end # module JuliaMetricsCalculator
