# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1820 à 1846
# Type: Méthode de la classe INDEX5Predictor

    def predict_entropy_level(self, sequence_history, entropy_evolution):
        """
        Prédiction basée sur l'analyse entropique avancée
        Utilise entropie métrique, complexité LZ, entropie topologique
        """
        if not entropy_evolution:
            return None

        current_metrics = entropy_evolution[-1] if entropy_evolution else {}

        # 1. Si entropie métrique stable → Système déterministe
        if self.is_metric_entropy_stable(entropy_evolution[-10:]):
            # Utiliser modèle déterministe basé sur transitions
            return self.predict_deterministic_model(sequence_history)

        # 2. Si complexité LZ faible → Séquence compressible
        if current_metrics.get('lz_complexity', 100) < 35:
            # Exploiter patterns de compression
            return self.predict_compression_patterns(sequence_history)

        # 3. Si entropie topologique élevée → Richesse structurelle
        # CORRECTION AEP: Nouveau seuil basé sur les valeurs observées (3.8-4.1 bits)
        if current_metrics.get('topological_entropy', 0) > 4.05:
            # Modèle sophistiqué multi-patterns
            return self.predict_rich_structure_model(sequence_history)

        return None