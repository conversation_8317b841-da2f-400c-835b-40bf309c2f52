#!/usr/bin/env python3
"""Vérification de evolution[position-1] pour le tableau prédictif"""

def debug_evolution_position():
    """Vérifie à quoi correspond evolution[position-1] pour position=5 (Main 6)"""
    
    print("🔍 VÉRIFICATION DE evolution[position-1]")
    print("=" * 50)
    
    # Séquence d'exemple
    sequence = ['Main1', 'Main2', 'Main3', 'Main4', 'Main5', 'Main6']
    
    print("📊 Séquence complète:")
    for i, main in enumerate(sequence):
        print(f"   Position {i}: {main}")
    print()
    
    # SIMULATION DE LA CRÉATION DE evolution
    print("🔄 SIMULATION DE calculate_block_entropy_evolution()")
    print("-" * 50)
    print("for n in range(1, len(sequence) + 1):")
    print("    subsequence = sequence[:n]")
    print("    simple_entropy_theoretical = AEP(subsequence)")
    print("    results.append({'position': n, 'simple_entropy_theoretical': ...})")
    print()
    
    evolution = []
    for n in range(1, len(sequence) + 1):
        subsequence = sequence[:n]
        print(f"📊 n={n}: subsequence = sequence[:{n}] = {subsequence}")
        print(f"   → evolution[{n-1}] = {{'position': {n}, 'simple_entropy_theoretical': AEP({subsequence})}}")
        
        evolution.append({
            'position': n,
            'subsequence': subsequence,
            'simple_entropy_theoretical': f"AEP({subsequence})"
        })
    
    print()
    print("📋 TABLEAU evolution COMPLET:")
    print("-" * 50)
    for i, item in enumerate(evolution):
        print(f"evolution[{i}] = position:{item['position']}, AEP:{item['simple_entropy_theoretical']}")
    
    print()
    print("🎯 ANALYSE POUR TABLEAU PRÉDICTIF MAIN 6")
    print("-" * 50)
    position = 5  # Position de Main 6
    print(f"Position analysée: {position} (Main 6)")
    print()
    
    print("ACTUEL DANS LE CODE:")
    print(f"   current_theoretical = evolution[{position}].get('simple_entropy_theoretical')")
    if position < len(evolution):
        print(f"   → evolution[{position}] = {evolution[position]}")
        print(f"   → current_theoretical = {evolution[position]['simple_entropy_theoretical']}")
    print()
    
    print("PROPOSITION evolution[position-1]:")
    print(f"   current_theoretical = evolution[{position-1}].get('simple_entropy_theoretical')")
    if position-1 < len(evolution):
        print(f"   → evolution[{position-1}] = {evolution[position-1]}")
        print(f"   → current_theoretical = {evolution[position-1]['simple_entropy_theoretical']}")
    print()
    
    print("🔍 VÉRIFICATION LOGIQUE:")
    print("-" * 40)
    print("Pour prédire Main 6:")
    print("   - On veut l'entropie de la séquence AVANT Main 6")
    print("   - Donc AEP(Mains 1-5)")
    print()
    print(f"evolution[{position-1}] contient:")
    if position-1 < len(evolution):
        print(f"   - position: {evolution[position-1]['position']} (Main 5)")
        print(f"   - subsequence: {evolution[position-1]['subsequence']} (Mains 1-5)")
        print(f"   - AEP: {evolution[position-1]['simple_entropy_theoretical']}")
    print()
    print("✅ evolution[position-1] correspond bien à AEP(Mains 1-5) !")

if __name__ == "__main__":
    debug_evolution_position()
