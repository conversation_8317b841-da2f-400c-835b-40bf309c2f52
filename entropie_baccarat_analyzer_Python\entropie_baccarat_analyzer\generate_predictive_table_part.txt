# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2863 à 2964
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

    def generate_predictive_table_part(self, sequence, evolution, analyzer, start_main, end_main, part_number):
        """
        Génère une partie du tableau prédictif avec différentiels et séparateurs verticaux
        """
        if not sequence or not evolution:
            return "❌ Données insuffisantes pour générer le tableau prédictif"

        # Pour le premier tableau, commencer à la main 6 au lieu de la main 1
        if part_number == 1:
            actual_start = max(start_main, 6)  # Commencer à la main 6
        else:
            actual_start = start_main

        # Ajuster les limites selon la longueur de la séquence
        actual_end = min(end_main, len(sequence))
        if actual_start > len(sequence):
            return f"❌ Main {actual_start} dépasse la longueur de la séquence ({len(sequence)} mains)"

        # En-tête du tableau avec séparateurs verticaux
        header_line1 = "INDEX5 n+1".ljust(15)
        header_line2 = " " * 15
        header_separator = " " * 15  # Séparateur horizontal sous les en-têtes Main
        separator_line = "=" * 15

        # Générer l'en-tête pour la plage de mains spécifiée avec séparateurs
        for i in range(actual_start, actual_end + 1):
            header_line1 += "|" + f"Main {i}".ljust(24)
            # Construction exacte du format souhaité : |DiffC|DiffT|DivEG|EntG  |
            header_line2 += "|DiffC|DiffT|DivEG|EntG  ".ljust(25)
            # Séparateur horizontal sous chaque "Main X" mais pas sous "INDEX5 n+1"
            header_separator += "|" + "-" * 24
            separator_line += "|" + "=" * 24

        # Ajouter le séparateur final
        header_line1 += "|"
        header_line2 += "|"
        header_separator += "|"
        separator_line += "|"

        table = f"📊 PARTIE {part_number} - MAINS {actual_start} À {actual_end}\n"
        table += separator_line + "\n"
        table += header_line1 + "\n"
        table += header_separator + "\n"  # Séparateur horizontal entre Main et DiffC DiffT DivEG EntG
        table += header_line2 + "\n"
        table += separator_line + "\n"

        # Générer les lignes pour chaque valeur INDEX5 possible avec séparateurs
        for i, index5_value in enumerate(self.all_index5_values):
            line = index5_value.ljust(15)

            for position in range(actual_start - 1, actual_end):  # -1 car les indices commencent à 0
                line += "|"  # Séparateur vertical avant chaque main

                if position < len(sequence):
                    # Calculer les différentiels prédictifs
                    predictive_diffs = self.calculate_predictive_differentials(
                        sequence, evolution, position, analyzer
                    )

                    if index5_value in predictive_diffs:
                        diffs = predictive_diffs[index5_value]
                        # Ajouter des séparateurs verticaux entre les valeurs
                        cell_content = f"{diffs['DiffCond']:5.3f}|{diffs['DiffTaux']:5.3f}|{diffs['DiffDivEntropG']:5.3f}|{diffs['DiffEntropG']:5.3f}"
                    else:
                        cell_content = "N/A  |N/A  |N/A  |N/A   "
                else:
                    cell_content = "---  |---  |---  |---   "

                line += cell_content.center(24)

            line += "|"  # Séparateur final
            table += line + "\n"

            # Ajouter un séparateur horizontal entre 0_C_TIE et 1_A_BANKER
            if index5_value == "0_C_TIE":
                table += separator_line + "\n"

        # Ajouter une ligne de séparation finale
        table += separator_line + "\n"

        # NOUVELLE LIGNE : Valeur INDEX5 réellement observée pour chaque main
        observed_line = "OBSERVÉ        "
        for position in range(actual_start - 1, actual_end):  # -1 car les indices commencent à 0
            observed_line += "|"  # Séparateur vertical avant chaque main

            if position < len(sequence):
                # Récupérer la valeur INDEX5 réellement observée à cette position
                observed_index5 = sequence[position]
                # Aligner la valeur observée à gauche dans la cellule
                cell_content = observed_index5
            else:
                cell_content = "---"

            observed_line += cell_content.ljust(24)

        observed_line += "|\n"
        table += observed_line

        # Ligne de séparation finale après la ligne observée
        table += separator_line + "\n"

        return table