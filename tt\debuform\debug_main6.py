#!/usr/bin/env python3
"""Debug pour vérifier pourquoi Main 6 n'apparaît pas"""

import re
import numpy as np

def debug_main6():
    """Debug spécifique pour Main 6"""
    
    # Charger le fichier
    with open('ratios_difft_diffentg_rapport1.txt', 'r', encoding='utf-8') as f:
        lignes = f.readlines()
    
    # Trouver Main 6
    ligne_main6 = None
    for ligne in lignes:
        if ligne.startswith('Main 6:'):
            ligne_main6 = ligne.strip()
            break
    
    if not ligne_main6:
        print("❌ Main 6 non trouvée")
        return
    
    print(f"📄 Ligne Main 6: {ligne_main6}")
    
    # Parser comme dans le code original
    match_main = re.match(r'Main (\d+):', ligne_main6)
    if not match_main:
        print("❌ Regex main échoue")
        return
    
    main = int(match_main.group(1))
    print(f"✅ Main extraite: {main}")
    
    # Extraction INDEX5 observé
    match_observe = re.search(r'OBSERVÉ:\s*([^\s]+)', ligne_main6)
    if not match_observe:
        print("❌ Regex observé échoue")
        return
    
    index5_observe = match_observe.group(1)
    print(f"✅ INDEX5 observé: {index5_observe}")
    
    # Extraction INDEX3 de l'INDEX5 observé
    if '_BANKER' in index5_observe:
        index3_observe = 'BANKER'
    elif '_PLAYER' in index5_observe:
        index3_observe = 'PLAYER'
    elif '_TIE' in index5_observe:
        index3_observe = 'TIE'
    else:
        print("❌ INDEX3 non reconnu")
        return
    
    print(f"✅ INDEX3 observé: {index3_observe}")
    
    # Test condition TIE
    if index3_observe == 'TIE':
        print("❌ Main 6 ignorée car TIE")
        return
    
    print("✅ Main 6 n'est pas TIE")
    
    # Extraction des ratios
    partie_ratios = ligne_main6.split('|')[0].replace(f'Main {main}:', '').strip()
    valeurs = partie_ratios.split()
    
    print(f"📊 Valeurs extraites: {valeurs}")
    
    # Ordre des INDEX5 dans le fichier
    index5_ordre = [
        '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', 
        '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER',
        '1_A_BANKER', '1_B_BANKER', '1_C_BANKER', 
        '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER'
    ]
    
    # Conversion en float
    ratios = {}
    for i, val in enumerate(valeurs):
        if i < len(index5_ordre):
            if val != 'N/A':
                try:
                    ratios[index5_ordre[i]] = float(val)
                except ValueError:
                    pass
    
    print(f"📊 Ratios valides: {ratios}")
    
    # Séparer les ratios BANKER et PLAYER
    ratios_banker = []
    ratios_player = []
    
    for index5, ratio in ratios.items():
        if 'BANKER' in index5:
            ratios_banker.append(ratio)
        elif 'PLAYER' in index5:
            ratios_player.append(ratio)
    
    print(f"🏦 Ratios BANKER: {ratios_banker}")
    print(f"👤 Ratios PLAYER: {ratios_player}")
    
    # Test condition critique
    if not ratios_banker or not ratios_player:
        print("❌ Main 6 ignorée car ratios manquants")
        print(f"   BANKER vide: {not ratios_banker}")
        print(f"   PLAYER vide: {not ratios_player}")
        return
    
    print("✅ Main 6 a des ratios pour BANKER ET PLAYER")
    print("🎯 Main 6 DEVRAIT être traitée !")
    
    # Test d'une règle simple
    moy_banker = np.mean(ratios_banker)
    moy_player = np.mean(ratios_player)
    prediction = 'BANKER' if moy_banker < moy_player else 'PLAYER'
    
    print(f"📊 Test MOYENNE_BANKER_VS_PLAYER:")
    print(f"   Moyenne BANKER: {moy_banker:.3f}")
    print(f"   Moyenne PLAYER: {moy_player:.3f}")
    print(f"   Prédiction: {prediction}")
    print(f"   Observé: {index3_observe}")
    print(f"   Résultat: {'✅' if prediction == index3_observe else '❌'}")

if __name__ == "__main__":
    debug_main6()
