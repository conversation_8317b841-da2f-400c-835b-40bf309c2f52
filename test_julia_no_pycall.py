"""
Test Julia sans PyCall pour éviter les conflits
"""

import os
import sys

# Configurer l'environnement pour éviter PyCall
os.environ['JULIA_PYTHONCALL_EXE'] = ''
os.environ['JULIA_LOAD_PATH'] = '@stdlib'

print("Test Julia sans PyCall...")

try:
    # Importer julia avec configuration spéciale
    import julia
    
    # Initialiser Julia avec options spéciales
    julia.install()  # Réinstaller si nécessaire
    
    # Importer Main
    from julia import Main
    print("OK Julia Main initialise sans PyCall")
    
    # Test simple
    result = Main.eval("2 + 2")
    print(f"OK Test simple: 2 + 2 = {result}")
    
    # Charger notre module
    Main.include("julia_entropy_core.jl")
    print("OK Module charge")
    
    # Utiliser le module
    Main.eval("using .JuliaEntropyCore")
    print("OK Module utilise")
    
    # Test fonction
    result = Main.eval("JuliaEntropyCore.safe_log_one_based([0.5, 0.3, 0.2])")
    print(f"OK Fonction testee: {result}")
    
    print("SUCCES: Julia fonctionne sans PyCall!")
    
except Exception as e:
    print(f"ERREUR: {e}")
    
    # Diagnostic détaillé
    print("\nDiagnostic:")
    print(f"Python executable: {sys.executable}")
    print(f"JULIA_PYTHONCALL_EXE: {os.environ.get('JULIA_PYTHONCALL_EXE', 'Non defini')}")
    print(f"JULIA_LOAD_PATH: {os.environ.get('JULIA_LOAD_PATH', 'Non defini')}")
