# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 600 à 629
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def _calculate_sequence_complexity(self, sequence: List[str]) -> Dict:
        """
        Calcule diverses métriques de complexité de la séquence.

        Référence: entropie/cours_entropie/niveau_expert/02_entropie_topologique.md
        """
        n = len(sequence)

        # Nombre de motifs uniques de différentes longueurs
        unique_patterns = {}
        for length in range(1, min(6, n + 1)):
            patterns = set()
            for i in range(n - length + 1):
                pattern = tuple(sequence[i:i+length])
                patterns.add(pattern)
            unique_patterns[f'length_{length}'] = len(patterns)

        # Complexité de Lempel-Ziv (approximation)
        lz_complexity = self._approximate_lz_complexity(sequence)

        # Entropie topologique approximée
        topological_entropy = self._approximate_topological_entropy(sequence)

        return {
            'unique_patterns': unique_patterns,
            'lz_complexity': lz_complexity,
            'topological_entropy': topological_entropy,
            'sequence_diversity': len(set(sequence)) / len(self.theoretical_probs),  # Diversité relative
            'repetition_rate': self._calculate_repetition_rate(sequence)
        }