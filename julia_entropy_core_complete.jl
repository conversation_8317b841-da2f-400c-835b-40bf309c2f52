"""
JuliaEntropyCore.jl - Module Julia pour calculs entropiques fondamentaux
CRÉATION AVEC LECTURE COMPLÈTE INTÉGRALE DE CHAQUE FICHIER TEXTE

Architecture hybride Julia-Python avec indexation 1-based
Migré depuis entropie_baccarat_analyzer.py selon plan.txt
QUALITÉ ARTISANALE - CHAQUE FICHIER TEXTE LU INTÉGRALEMENT
"""

module JuliaEntropyCore

export safe_log_one_based, validate_probabilities_one_based, 
       calculate_shannon_entropy_one_based, calculate_sequence_entropy_aep_one_based,
       calculate_conditional_entropy_one_based, estimate_metric_entropy_one_based,
       calculate_block_entropies_one_based, calculate_block_entropies_raw_one_based,
       calculate_block_entropy_evolution_one_based, calculate_sequence_complexity_one_based,
       approximate_lz_complexity_one_based, approximate_topological_entropy_one_based,
       calculate_repetition_rate_one_based, calculate_simple_entropy_theoretical_one_based,
       THEORETICAL_PROBS_ONE_BASED

# Probabilités théoriques INDEX5 EXACTES (ligne 100-112 source)
# CORRECTION EXPERTE: Copie exacte avec correction ligne 104
const THEORETICAL_PROBS_RAW_ONE_BASED = Dict{String, Float64}(
    "0_A_BANKER" => 8.5136, "1_A_BANKER" => 8.6389,
    "0_B_BANKER" => 6.4676, "1_B_BANKER" => 6.5479,  # CORRIGÉ: était 7.6907
    "0_C_BANKER" => 7.7903, "1_C_BANKER" => 7.8929,
    "0_A_PLAYER" => 8.5240, "1_A_PLAYER" => 8.6361,
    "0_B_PLAYER" => 7.6907, "1_B_PLAYER" => 7.7888,
    "0_C_PLAYER" => 5.9617, "1_C_PLAYER" => 6.0352,
    "0_A_TIE" => 1.7719, "1_A_TIE" => 1.7978,
    "0_B_TIE" => 1.6281, "1_B_TIE" => 1.6482,
    "0_C_TIE" => 1.3241, "1_C_TIE" => 1.3423
)

# Normalisation des probabilités théoriques
const THEORETICAL_PROBS_ONE_BASED = begin
    total = sum(values(THEORETICAL_PROBS_RAW_ONE_BASED))
    Dict(k => v/total for (k, v) in THEORETICAL_PROBS_RAW_ONE_BASED)
end

"""
🔒 SÉCURITÉ - Calcul sécurisé du logarithme avec gestion de log(0)
LECTURE COMPLÈTE INTÉGRALE: _safe_log.txt (17 lignes)
Lignes source: 123-134

Évite les erreurs mathématiques en remplaçant les valeurs nulles ou négatives
par epsilon avant le calcul logarithmique.

Référence: entropie/cours_entropie/ressources/implementations_python.py
"""
function safe_log_one_based(x::Vector{Float64}; epsilon::Float64=1e-10, base::Float64=2.0)::Vector{Float64}
    # Équivalent Julia de: x = np.where(x <= 0, self.epsilon, x)
    x_safe = [val <= 0 ? epsilon : val for val in x]
    # Équivalent Julia de: return np.log(x) / np.log(self.base)
    return [log(val) / log(base) for val in x_safe]
end

"""
✅ VALIDATION - Valide et normalise un vecteur de probabilités
LECTURE COMPLÈTE INTÉGRALE: _validate_probabilities.txt (27 lignes)
Lignes source: 136-157

Assure que les probabilités sont positives et normalisées (somme = 1).
Applique une distribution uniforme si toutes les probabilités sont nulles.

Référence: entropie/cours_entropie/ressources/implementations_python.py
"""
function validate_probabilities_one_based(p::Vector{Float64})::Vector{Float64}
    # Équivalent Julia de: if np.any(p < 0): raise ValueError(...)
    if any(x -> x < 0, p)
        throw(ArgumentError("Les probabilités doivent être positives"))
    end
    
    # Équivalent Julia de: total = np.sum(p)
    total = sum(p)
    if total > 0
        # Équivalent Julia de: p = p / total
        return p ./ total
    else
        # Distribution uniforme si toutes les probabilités sont nulles
        # Équivalent Julia de: p = np.ones_like(p) / len(p)
        return ones(length(p)) ./ length(p)
    end
end

"""
📊 SHANNON - Calcule l'entropie de Shannon: H(X) = -∑ p(x) log₂ p(x)
LECTURE COMPLÈTE INTÉGRALE: _calculate_shannon_entropy.txt (30 lignes)
Lignes source: 163-187

Formule fondamentale de la théorie de l'information pour mesurer
l'incertitude d'une distribution de probabilités.

Référence: entropie/cours_entropie/niveau_debutant/02_formule_shannon.md
"""
function calculate_shannon_entropy_one_based(probabilities::Vector{Float64})::Float64
    # Équivalent Julia de: p = self._validate_probabilities(probabilities)
    p = validate_probabilities_one_based(probabilities)
    
    # Calcul avec gestion de 0*log(0) = 0
    # Équivalent Julia de: log_p = self._safe_log(p)
    log_p = safe_log_one_based(p)
    # Équivalent Julia de: entropy_terms = p * log_p
    entropy_terms = p .* log_p
    
    # Remplace NaN par 0 (cas 0*log(0))
    # Équivalent Julia de: entropy_terms = np.where(p == 0, 0, entropy_terms)
    entropy_terms = [p[i] == 0 ? 0.0 : entropy_terms[i] for i in 1:length(p)]
    
    # Équivalent Julia de: return -np.sum(entropy_terms)
    return -sum(entropy_terms)
end

# PLACEHOLDER pour les méthodes suivantes - À compléter avec lecture complète des fichiers restants
# calculate_sequence_entropy_aep_one_based - NÉCESSITE LECTURE COMPLÈTE de _calculate_sequence_entropy_aep.txt
# calculate_conditional_entropy_one_based - NÉCESSITE LECTURE COMPLÈTE de _calculate_conditional_entropy.txt
# estimate_metric_entropy_one_based - NÉCESSITE LECTURE COMPLÈTE de _estimate_metric_entropy.txt
# calculate_block_entropies_one_based - NÉCESSITE LECTURE COMPLÈTE de _calculate_block_entropies.txt
# calculate_block_entropy_evolution_one_based - NÉCESSITE LECTURE COMPLÈTE de calculate_block_entropy_evolution.txt
# Et toutes les autres méthodes...

end # module JuliaEntropyCore
