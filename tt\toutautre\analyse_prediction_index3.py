#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSE SYSTÉMATIQUE LIGNE PAR LIGNE POUR PRÉDICTION INDEX3
===========================================================

Objectif : Tester si les ratios DiffT/DiffEntG permettent de prédire INDEX3 (BANKER/PLAYER/TIE)
Méthode : Analyse de chaque main individuellement avec différentes règles
"""

import re
import numpy as np
from collections import defaultdict

class AnalyseurPredictionIndex3:
    def __init__(self):
        self.donnees_mains = {}
        self.resultats_regles = defaultdict(lambda: {'correct': 0, 'total': 0, 'details': []})
        
        # INDEX5 organisés par INDEX3
        self.index5_par_index3 = {
            'BANKER': ['0_A_BANKER', '0_B_BANKER', '0_C_BANKER', '1_A_BANKER', '1_B_BANKER', '1_C_BANKER'],
            'PLAYER': ['0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER', '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER'],
            'TIE': []  # Pas de ratios TIE dans nos données
        }
        
        print("🎯 ANALYSEUR PRÉDICTION INDEX3")
        print("📊 Test des règles sur ratios DiffT/DiffEntG")
    
    def charger_donnees(self, fichier_path):
        """Charge les données du fichier ratios"""
        print(f"\n📂 Chargement : {fichier_path}")
        
        with open(fichier_path, 'r', encoding='utf-8') as f:
            lignes = f.readlines()
        
        for ligne in lignes:
            if ligne.startswith('Main '):
                self._parser_ligne_main(ligne.strip())
        
        print(f"✅ {len(self.donnees_mains)} mains chargées")
    
    def _parser_ligne_main(self, ligne):
        """Parse une ligne : Main X: [ratios] | OBSERVÉ: INDEX5"""
        # Extraction numéro de main
        match_main = re.match(r'Main (\d+):', ligne)
        if not match_main:
            return
        
        main = int(match_main.group(1))
        
        # Extraction INDEX5 observé
        match_observe = re.search(r'OBSERVÉ:\s*([^\s]+)', ligne)
        if not match_observe:
            return
        
        index5_observe = match_observe.group(1)
        
        # Extraction INDEX3 de l'INDEX5 observé
        if '_BANKER' in index5_observe:
            index3_observe = 'BANKER'
        elif '_PLAYER' in index5_observe:
            index3_observe = 'PLAYER'
        elif '_TIE' in index5_observe:
            index3_observe = 'TIE'
        else:
            return
        
        # Extraction des ratios
        partie_ratios = ligne.split('|')[0].replace(f'Main {main}:', '').strip()
        valeurs = partie_ratios.split()
        
        # Ordre des INDEX5 dans le fichier
        index5_ordre = [
            '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', 
            '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER',
            '1_A_BANKER', '1_B_BANKER', '1_C_BANKER', 
            '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER'
        ]
        
        # Conversion en float
        ratios = {}
        for i, val in enumerate(valeurs):
            if i < len(index5_ordre):
                if val != 'N/A':
                    try:
                        ratios[index5_ordre[i]] = float(val)
                    except ValueError:
                        pass
        
        # Stockage
        self.donnees_mains[main] = {
            'index3_observe': index3_observe,
            'index5_observe': index5_observe,
            'ratios': ratios
        }
    
    def analyser_toutes_regles(self):
        """Teste toutes les règles de prédiction INDEX3"""
        print("\n🔬 ANALYSE SYSTÉMATIQUE DES RÈGLES")
        print("=" * 50)
        
        # Règles à tester
        regles = [
            'MIN_GLOBAL',
            'MAX_GLOBAL', 
            'MEDIAN_GLOBAL',
            'MOYENNE_BANKER_VS_PLAYER',
            'MIN_BANKER_VS_PLAYER',
            'MAX_BANKER_VS_PLAYER',
            'MEDIAN_BANKER_VS_PLAYER',
            'SOMME_BANKER_VS_PLAYER'
        ]
        
        for regle in regles:
            print(f"\n🎯 Test règle : {regle}")
            self._tester_regle(regle)
        
        # Affichage des résultats
        self._afficher_resultats()
    
    def _tester_regle(self, regle):
        """Teste une règle spécifique sur toutes les mains"""
        for main, donnees in self.donnees_mains.items():
            index3_observe = donnees['index3_observe']
            ratios = donnees['ratios']
            
            # Ignorer les TIE (pas de ratios correspondants)
            if index3_observe == 'TIE':
                continue
            
            # Appliquer la règle
            index3_predit = self._appliquer_regle(ratios, regle)
            
            # Enregistrer le résultat
            self.resultats_regles[regle]['total'] += 1
            
            if index3_predit == index3_observe:
                self.resultats_regles[regle]['correct'] += 1
                statut = "✅"
            else:
                statut = "❌"
            
            # Détails pour debug
            self.resultats_regles[regle]['details'].append({
                'main': main,
                'observe': index3_observe,
                'predit': index3_predit,
                'statut': statut
            })
    
    def _appliquer_regle(self, ratios, regle):
        """Applique une règle de prédiction"""
        if not ratios:
            return None
        
        # Séparation BANKER vs PLAYER
        ratios_banker = {k: v for k, v in ratios.items() if 'BANKER' in k}
        ratios_player = {k: v for k, v in ratios.items() if 'PLAYER' in k}
        
        if regle == 'MIN_GLOBAL':
            # INDEX3 de la valeur minimale globale
            min_key = min(ratios.keys(), key=lambda k: ratios[k])
            return 'BANKER' if 'BANKER' in min_key else 'PLAYER'
        
        elif regle == 'MAX_GLOBAL':
            # INDEX3 de la valeur maximale globale
            max_key = max(ratios.keys(), key=lambda k: ratios[k])
            return 'BANKER' if 'BANKER' in max_key else 'PLAYER'
        
        elif regle == 'MEDIAN_GLOBAL':
            # INDEX3 de la valeur la plus proche de la médiane globale
            valeurs = list(ratios.values())
            mediane = np.median(valeurs)
            closest_key = min(ratios.keys(), key=lambda k: abs(ratios[k] - mediane))
            return 'BANKER' if 'BANKER' in closest_key else 'PLAYER'
        
        elif regle == 'MOYENNE_BANKER_VS_PLAYER':
            if ratios_banker and ratios_player:
                moy_banker = np.mean(list(ratios_banker.values()))
                moy_player = np.mean(list(ratios_player.values()))
                return 'BANKER' if moy_banker < moy_player else 'PLAYER'
        
        elif regle == 'MIN_BANKER_VS_PLAYER':
            if ratios_banker and ratios_player:
                min_banker = min(ratios_banker.values())
                min_player = min(ratios_player.values())
                return 'BANKER' if min_banker < min_player else 'PLAYER'
        
        elif regle == 'MAX_BANKER_VS_PLAYER':
            if ratios_banker and ratios_player:
                max_banker = max(ratios_banker.values())
                max_player = max(ratios_player.values())
                return 'BANKER' if max_banker < max_player else 'PLAYER'
        
        elif regle == 'MEDIAN_BANKER_VS_PLAYER':
            if ratios_banker and ratios_player:
                med_banker = np.median(list(ratios_banker.values()))
                med_player = np.median(list(ratios_player.values()))
                return 'BANKER' if med_banker < med_player else 'PLAYER'
        
        elif regle == 'SOMME_BANKER_VS_PLAYER':
            if ratios_banker and ratios_player:
                sum_banker = sum(ratios_banker.values())
                sum_player = sum(ratios_player.values())
                return 'BANKER' if sum_banker < sum_player else 'PLAYER'
        
        return None
    
    def _afficher_resultats(self):
        """Affiche les résultats de toutes les règles"""
        print("\n📊 RÉSULTATS FINAUX - PRÉDICTION INDEX3")
        print("=" * 60)
        
        # Tri par précision décroissante
        resultats_tries = []
        for regle, stats in self.resultats_regles.items():
            if stats['total'] > 0:
                precision = stats['correct'] / stats['total']
                resultats_tries.append({
                    'regle': regle,
                    'precision': precision,
                    'correct': stats['correct'],
                    'total': stats['total']
                })
        
        resultats_tries.sort(key=lambda x: x['precision'], reverse=True)
        
        print(f"{'RÈGLE':<25} {'PRÉCISION':<12} {'SUCCÈS':<12} {'STATUT':<15}")
        print("-" * 70)
        
        for resultat in resultats_tries:
            precision_pct = resultat['precision'] * 100
            statut = "🎯 SUPÉRIEUR" if precision_pct > 50 else "⚠️ INFÉRIEUR"
            
            print(f"{resultat['regle']:<25} {precision_pct:>8.2f}%    {resultat['correct']:>3}/{resultat['total']:<3}      {statut}")
        
        # Meilleure règle
        if resultats_tries:
            meilleure = resultats_tries[0]
            print(f"\n🏆 MEILLEURE RÈGLE : {meilleure['regle']}")
            print(f"   📊 Précision : {meilleure['precision']*100:.2f}%")
            print(f"   ✅ Succès : {meilleure['correct']}/{meilleure['total']}")
            
            if meilleure['precision'] > 0.5:
                print(f"   🎯 STATUT : SUPÉRIEUR AU HASARD (+{(meilleure['precision']-0.5)*100:.2f}%)")
            else:
                print(f"   ⚠️ STATUT : INFÉRIEUR AU HASARD ({(meilleure['precision']-0.5)*100:.2f}%)")
    
    def analyser_details_meilleure_regle(self):
        """Analyse détaillée de la meilleure règle"""
        # Trouver la meilleure règle
        meilleure_regle = None
        meilleure_precision = 0
        
        for regle, stats in self.resultats_regles.items():
            if stats['total'] > 0:
                precision = stats['correct'] / stats['total']
                if precision > meilleure_precision:
                    meilleure_precision = precision
                    meilleure_regle = regle
        
        if meilleure_regle:
            print(f"\n🔍 ANALYSE DÉTAILLÉE : {meilleure_regle}")
            print("=" * 50)
            
            details = self.resultats_regles[meilleure_regle]['details']
            
            # Affichage des premiers résultats
            print("Premiers résultats :")
            for i, detail in enumerate(details[:10]):
                print(f"Main {detail['main']:2d}: Observé={detail['observe']:<7} Prédit={detail['predit']:<7} {detail['statut']}")
            
            if len(details) > 10:
                print(f"... et {len(details)-10} autres mains")

def main():
    """Fonction principale"""
    print("🎯 ANALYSE PRÉDICTION INDEX3 AVEC RATIOS DiffT/DiffEntG")
    print("=" * 60)
    
    analyseur = AnalyseurPredictionIndex3()
    
    # Chargement des données
    analyseur.charger_donnees('ratios_difft_diffentg_manuel.txt')
    
    # Test de toutes les règles
    analyseur.analyser_toutes_regles()
    
    # Analyse détaillée de la meilleure règle
    analyseur.analyser_details_meilleure_regle()
    
    print("\n🎯 ANALYSE TERMINÉE")

if __name__ == "__main__":
    main()
