# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2409 à 2428
# Type: Méthode de la classe INDEX5PredictiveScoreCalculator

    def calculate_predictive_score(self, diff_c: float, diff_t: float, div_eg: float, ent_g: float) -> float:
        """
        Calcule le score prédictif selon la formule:
        SCORE = (DiffC + EntG) / (DiffT + DivEG)

        Args:
            diff_c: Différentiel Entropie Conditionnelle
            diff_t: Différentiel Taux d'Entropie
            div_eg: Différentiel Diversité Entropique
            ent_g: Différentiel Entropie Générale

        Returns:
            float: Score prédictif (peut être infini si dénominateur = 0)
        """
        denominator = diff_t + div_eg

        if denominator == 0:
            return float('inf')
        else:
            return (diff_c + ent_g) / denominator