"""
python_julia_bridge.py - Pont Python-Julia pour architecture hybride
Selon plan.txt Phase 2

Permet l'appel des fonctions Julia depuis Python avec conversion automatique
des types et indexation 0-based → 1-based
"""

import julia
from julia import Main
import numpy as np
from typing import List, Dict, Any, Optional, Union
import os

class AdvancedJuliaPythonBridge:
    """
    Pont avancé Python-Julia pour l'architecture hybride
    Gère la conversion automatique des types et l'indexation
    """
    
    def __init__(self):
        """
        Initialise le pont Python-Julia et charge tous les modules
        """
        print("Initialisation du pont Python-Julia...")

        # Initialiser Julia avec configuration spéciale pour éviter PyCall
        try:
            # Configurer Julia pour éviter les conflits PyCall
            os.environ['JULIA_PYTHONCALL_EXE'] = sys.executable

            # Charger le module Julia
            current_dir = os.path.dirname(os.path.abspath(__file__))
            julia_file = os.path.join(current_dir, "julia_entropy_core.jl")

            if os.path.exists(julia_file):
                Main.include(julia_file)
                Main.eval("using .JuliaEntropyCore")
                print("Module JuliaEntropyCore charge avec succes")
            else:
                raise FileNotFoundError(f"Fichier Julia non trouve: {julia_file}")

        except Exception as e:
            print(f"Erreur lors du chargement de Julia: {e}")
            raise
    
    def _convert_to_julia_vector_string(self, python_list: List[str]) -> Any:
        """
        Convertit une liste Python en Vector{String} Julia
        """
        items = ', '.join([f'"{item}"' for item in python_list])
        return Main.eval(f'[{items}]')
    
    def _convert_to_julia_vector_float(self, python_list: List[float]) -> Any:
        """
        Convertit une liste Python en Vector{Float64} Julia
        """
        return Main.eval(f'[{", ".join([str(item) for item in python_list])}]')
    
    def _convert_from_julia_dict(self, julia_dict: Any) -> Dict[str, Any]:
        """
        Convertit un Dict Julia en dictionnaire Python
        """
        if julia_dict is None:
            return {}
        
        # Conversion basique - à améliorer selon les besoins
        try:
            return dict(julia_dict)
        except:
            return {}
    
    # ==================== MÉTHODES ENTROPIQUES FONDAMENTALES ====================
    
    def safe_log_one_based(self, x: List[float], epsilon: float = 1e-10, base: float = 2.0) -> List[float]:
        """
        Appel Julia: safe_log_one_based()
        """
        julia_x = self._convert_to_julia_vector_float(x)
        result = Main.eval(f"JuliaEntropyCore.safe_log_one_based({julia_x}, epsilon={epsilon}, base={base})")
        return list(result)
    
    def validate_probabilities_one_based(self, probabilities: List[float]) -> List[float]:
        """
        Appel Julia: validate_probabilities_one_based()
        """
        julia_probs = self._convert_to_julia_vector_float(probabilities)
        result = Main.eval(f"JuliaEntropyCore.validate_probabilities_one_based({julia_probs})")
        return list(result)
    
    def calculate_shannon_entropy_one_based(self, probabilities: List[float]) -> float:
        """
        Appel Julia: calculate_shannon_entropy_one_based()
        """
        julia_probs = self._convert_to_julia_vector_float(probabilities)
        result = Main.eval(f"JuliaEntropyCore.calculate_shannon_entropy_one_based({julia_probs})")
        return float(result)
    
    def calculate_sequence_entropy_aep_one_based(self, sequence: List[str]) -> float:
        """
        Appel Julia: calculate_sequence_entropy_aep_one_based()
        FORMULE MAÎTRE AEP
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = Main.eval(f"JuliaEntropyCore.calculate_sequence_entropy_aep_one_based({julia_sequence})")
        return float(result)
    
    def calculate_conditional_entropy_one_based(self, sequence: List[str]) -> float:
        """
        Appel Julia: calculate_conditional_entropy_one_based()
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = Main.eval(f"JuliaEntropyCore.calculate_conditional_entropy_one_based({julia_sequence})")
        return float(result)
    
    def estimate_metric_entropy_one_based(self, sequence: List[str], max_length: int) -> float:
        """
        Appel Julia: estimate_metric_entropy_one_based()
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = Main.eval(f"JuliaEntropyCore.estimate_metric_entropy_one_based({julia_sequence}, {max_length})")
        return float(result)
    
    def calculate_block_entropies_one_based(self, sequence: List[str], max_length: int) -> List[float]:
        """
        Appel Julia: calculate_block_entropies_one_based()
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = Main.eval(f"JuliaEntropyCore.calculate_block_entropies_one_based({julia_sequence}, {max_length})")
        return list(result)
    
    def calculate_block_entropy_evolution_one_based(self, sequence: List[str], max_block_length: int = 5) -> List[Dict[str, Any]]:
        """
        Appel Julia: calculate_block_entropy_evolution_one_based()
        Méthode centrale d'analyse position par position
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = Main.eval(f"JuliaEntropyCore.calculate_block_entropy_evolution_one_based({julia_sequence}, {max_block_length})")
        
        # Conversion du résultat Julia en liste de dictionnaires Python
        python_result = []
        for item in result:
            python_dict = self._convert_from_julia_dict(item)
            python_result.append(python_dict)
        
        return python_result
    
    def calculate_sequence_complexity_one_based(self, sequence: List[str]) -> Dict[str, float]:
        """
        Appel Julia: calculate_sequence_complexity_one_based()
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = Main.eval(f"JuliaEntropyCore.calculate_sequence_complexity_one_based({julia_sequence})")
        return self._convert_from_julia_dict(result)
    
    # ==================== MÉTHODE D'ANALYSE COMPLÈTE ====================
    
    def analyze_complete_game_one_based(self, sequence: List[str]) -> Dict[str, Any]:
        """
        Analyse complète d'une partie avec TOUS les calculs en Julia
        Correspond à analyze_single_game() ligne 550-598 du plan
        """
        if not sequence:
            return {'error': 'Aucune séquence INDEX5 trouvée'}
        
        # Calcul évolution entropique (méthode centrale)
        entropy_evolution = self.calculate_block_entropy_evolution_one_based(sequence, max_block_length=4)
        
        if not entropy_evolution:
            return {'error': 'Impossible de calculer l\'évolution d\'entropie'}
        
        # Extraction des métriques finales
        final_analysis = entropy_evolution[-1]
        
        # Calcul de la complexité de la séquence
        complexity_metrics = self.calculate_sequence_complexity_one_based(sequence)
        
        return {
            'sequence_length': len(sequence),
            'full_sequence': sequence,
            'entropy_evolution': entropy_evolution,
            
            # Métriques finales (nouvelle approche Julia)
            'final_metric_entropy': final_analysis.get('metric_entropy', 0.0),
            'final_conditional_entropy': final_analysis.get('conditional_entropy', 0.0),
            'final_entropy_rate': final_analysis.get('entropy_rate', 0.0),
            'final_simple_entropy': final_analysis.get('simple_entropy', 0.0),
            'final_simple_entropy_theoretical': final_analysis.get('simple_entropy_theoretical', 0.0),
            
            # Analyse de complexité
            'complexity_metrics': complexity_metrics,
            
            # Positions d'intérêt
            'max_metric_entropy_position': max(entropy_evolution, key=lambda x: x.get('metric_entropy', 0))['position'] if entropy_evolution else 0,
            'max_conditional_entropy_position': max(entropy_evolution, key=lambda x: x.get('conditional_entropy', 0))['position'] if entropy_evolution else 0
        }

# ==================== TESTS DE VALIDATION ====================

def test_julia_python_bridge():
    """
    Tests de validation du pont Python-Julia
    Selon plan.txt Phase 1.2
    """
    print("🧪 Tests de validation du pont Python-Julia...")
    
    bridge = AdvancedJuliaPythonBridge()
    
    # Test indexation 1-based
    test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"]
    
    # Test calculs entropiques
    shannon_result = bridge.calculate_shannon_entropy_one_based([0.5, 0.3, 0.2])
    aep_result = bridge.calculate_sequence_entropy_aep_one_based(test_sequence)
    
    print(f"✅ Shannon entropy: {shannon_result}")
    print(f"✅ AEP entropy: {aep_result}")
    
    # Test analyse complète
    complete_result = bridge.analyze_complete_game_one_based(test_sequence)
    print(f"✅ Analyse complète: {len(complete_result)} métriques calculées")
    
    return True

if __name__ == "__main__":
    test_julia_python_bridge()
