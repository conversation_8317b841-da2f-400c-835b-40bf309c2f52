#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSE ENTROPIQUE EXPERTE DES RATIOS DiffT/DiffEntG
===================================================

Maître de l'Entropie - Analyse selon la théorie de l'information avancée
Méthodologie : Shannon → Kolmogorov-Sinai → Information Mutuelle → Classification Optimale
"""

import numpy as np
import pandas as pd
from collections import defaultdict, Counter
import re
from typing import Dict, List, Tuple, Optional

class AnalyseurEntropiqueRatios:
    """
    Analyseur expert basé sur la théorie de l'information pour les ratios DiffT/DiffEntG
    """
    
    def __init__(self):
        self.donnees_ratios = {}
        self.index5_observes = {}
        self.statistiques_globales = {}
        
        # INDEX5 possibles (ordre du fichier)
        self.index5_ordre = [
            '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', 
            '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER',
            '1_A_BANKER', '1_B_BANKER', '1_C_BANKER', 
            '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER'
        ]
        
        print("🎓 MAÎTRE DE L'ENTROPIE - Analyseur des Ratios DiffT/DiffEntG")
        print("📊 Théorie appliquée : Information Mutuelle + Classification Entropique")
    
    def charger_donnees_fichier(self, fichier_path: str):
        """
        Charge et parse les données du fichier ratios
        """
        print(f"\n📂 Chargement des données : {fichier_path}")
        
        with open(fichier_path, 'r', encoding='utf-8') as f:
            lignes = f.readlines()
        
        # Parse chaque ligne de données
        for ligne in lignes:
            if ligne.startswith('Main '):
                self._parser_ligne_main(ligne.strip())
        
        print(f"✅ {len(self.donnees_ratios)} mains chargées")
        print(f"📊 {len(self.index5_observes)} INDEX5 observés")
    
    def _parser_ligne_main(self, ligne: str):
        """
        Parse une ligne : Main X: [ratios] | OBSERVÉ: INDEX5
        """
        # Extraction du numéro de main
        match_main = re.match(r'Main (\d+):', ligne)
        if not match_main:
            return
        
        main = int(match_main.group(1))
        
        # Extraction de l'INDEX5 observé
        match_observe = re.search(r'OBSERVÉ:\s*([^\s]+)', ligne)
        if not match_observe:
            return
        
        index5_observe = match_observe.group(1)
        self.index5_observes[main] = index5_observe
        
        # Extraction des ratios
        partie_ratios = ligne.split('|')[0].replace(f'Main {main}:', '').strip()
        valeurs = partie_ratios.split()
        
        # Conversion en float (gestion N/A)
        ratios = []
        for val in valeurs:
            if val == 'N/A':
                ratios.append(None)
            else:
                try:
                    ratios.append(float(val))
                except ValueError:
                    ratios.append(None)
        
        # Stockage avec association INDEX5
        self.donnees_ratios[main] = {}
        for i, index5 in enumerate(self.index5_ordre):
            if i < len(ratios):
                self.donnees_ratios[main][index5] = ratios[i]
    
    def analyser_information_mutuelle(self):
        """
        Calcule l'information mutuelle entre ratios et INDEX5 observés
        Théorie : I(X;Y) = H(X) + H(Y) - H(X,Y)
        """
        print("\n🔬 ANALYSE D'INFORMATION MUTUELLE")
        print("=" * 50)
        
        # Préparation des données pour chaque INDEX5
        resultats_im = {}
        
        for index5 in self.index5_ordre:
            # Extraction des ratios disponibles pour cet INDEX5
            ratios_disponibles = []
            index5_correspondants = []
            
            for main, ratios in self.donnees_ratios.items():
                if index5 in ratios and ratios[index5] is not None:
                    ratios_disponibles.append(ratios[index5])
                    index5_correspondants.append(self.index5_observes[main])
            
            if len(ratios_disponibles) > 5:  # Minimum pour analyse statistique
                # Discrétisation des ratios en quartiles
                ratios_array = np.array(ratios_disponibles)
                quartiles = np.percentile(ratios_array, [25, 50, 75])
                
                # Classification en 4 catégories
                ratios_discretises = []
                for ratio in ratios_disponibles:
                    if ratio <= quartiles[0]:
                        ratios_discretises.append('Q1')
                    elif ratio <= quartiles[1]:
                        ratios_discretises.append('Q2')
                    elif ratio <= quartiles[2]:
                        ratios_discretises.append('Q3')
                    else:
                        ratios_discretises.append('Q4')
                
                # Calcul de l'information mutuelle
                im = self._calculer_information_mutuelle(ratios_discretises, index5_correspondants)
                
                resultats_im[index5] = {
                    'information_mutuelle': im,
                    'nb_echantillons': len(ratios_disponibles),
                    'quartiles': quartiles,
                    'entropie_ratios': self._calculer_entropie_shannon(ratios_discretises),
                    'entropie_index5': self._calculer_entropie_shannon(index5_correspondants)
                }
        
        # Tri par information mutuelle décroissante
        resultats_tries = sorted(resultats_im.items(), 
                               key=lambda x: x[1]['information_mutuelle'], 
                               reverse=True)
        
        print(f"{'INDEX5':<12} {'I.M.':<8} {'H(R)':<8} {'H(I5)':<8} {'Échant.':<8}")
        print("-" * 50)
        
        for index5, stats in resultats_tries:
            print(f"{index5:<12} {stats['information_mutuelle']:<8.4f} "
                  f"{stats['entropie_ratios']:<8.4f} {stats['entropie_index5']:<8.4f} "
                  f"{stats['nb_echantillons']:<8}")
        
        return resultats_im
    
    def _calculer_information_mutuelle(self, X: List, Y: List) -> float:
        """
        Calcule I(X;Y) = H(X) + H(Y) - H(X,Y)
        """
        # Entropies marginales
        h_x = self._calculer_entropie_shannon(X)
        h_y = self._calculer_entropie_shannon(Y)
        
        # Entropie jointe
        couples = [(x, y) for x, y in zip(X, Y)]
        h_xy = self._calculer_entropie_shannon(couples)
        
        return h_x + h_y - h_xy
    
    def _calculer_entropie_shannon(self, sequence: List) -> float:
        """
        Calcule H(X) = -∑ p(x) log₂ p(x)
        """
        compteur = Counter(sequence)
        total = len(sequence)
        
        entropie = 0.0
        for count in compteur.values():
            p = count / total
            if p > 0:
                entropie -= p * np.log2(p)
        
        return entropie
    
    def analyser_regles_classification(self):
        """
        Teste différentes règles de classification basées sur les ratios
        """
        print("\n🎯 ANALYSE DES RÈGLES DE CLASSIFICATION")
        print("=" * 50)
        
        regles = ['MIN', 'MAX', 'MEDIAN', 'Q1', 'Q3']
        resultats_regles = {}
        
        for regle in regles:
            correct = 0
            total = 0
            
            for main, ratios in self.donnees_ratios.items():
                index5_observe = self.index5_observes[main]
                
                # Extraction des ratios non-None
                ratios_valides = {k: v for k, v in ratios.items() if v is not None}
                
                if len(ratios_valides) >= 2:  # Minimum pour classification
                    index5_predit = self._appliquer_regle_classification(ratios_valides, regle)
                    
                    total += 1
                    if index5_predit == index5_observe:
                        correct += 1
            
            if total > 0:
                precision = correct / total
                resultats_regles[regle] = {
                    'precision': precision,
                    'correct': correct,
                    'total': total
                }
        
        # Affichage des résultats
        print(f"{'RÈGLE':<10} {'PRÉCISION':<12} {'SUCCÈS':<12} {'STATUT':<15}")
        print("-" * 55)
        
        for regle, stats in sorted(resultats_regles.items(), 
                                 key=lambda x: x[1]['precision'], 
                                 reverse=True):
            precision_pct = stats['precision'] * 100
            statut = "🎯 SUPÉRIEUR" if precision_pct > 50 else "⚠️ INFÉRIEUR"
            
            print(f"{regle:<10} {precision_pct:>8.2f}%    {stats['correct']:>3}/{stats['total']:<3}      {statut}")
        
        return resultats_regles
    
    def _appliquer_regle_classification(self, ratios_valides: Dict, regle: str) -> str:
        """
        Applique une règle de classification sur les ratios
        """
        valeurs = list(ratios_valides.values())
        index5_list = list(ratios_valides.keys())
        
        if regle == 'MIN':
            idx = np.argmin(valeurs)
        elif regle == 'MAX':
            idx = np.argmax(valeurs)
        elif regle == 'MEDIAN':
            mediane = np.median(valeurs)
            idx = np.argmin([abs(v - mediane) for v in valeurs])
        elif regle == 'Q1':
            q1 = np.percentile(valeurs, 25)
            idx = np.argmin([abs(v - q1) for v in valeurs])
        elif regle == 'Q3':
            q3 = np.percentile(valeurs, 75)
            idx = np.argmin([abs(v - q3) for v in valeurs])
        else:
            return index5_list[0]  # Fallback
        
        return index5_list[idx]
    
    def analyser_distribution_ratios(self):
        """
        Analyse statistique des distributions de ratios
        """
        print("\n📊 ANALYSE DES DISTRIBUTIONS DE RATIOS")
        print("=" * 50)
        
        for index5 in self.index5_ordre:
            ratios = []
            for main, ratios_main in self.donnees_ratios.items():
                if index5 in ratios_main and ratios_main[index5] is not None:
                    ratios.append(ratios_main[index5])
            
            if len(ratios) > 3:
                ratios_array = np.array(ratios)
                
                print(f"\n{index5}:")
                print(f"  📊 Échantillons: {len(ratios)}")
                print(f"  📈 Moyenne: {np.mean(ratios_array):.3f}")
                print(f"  📉 Médiane: {np.median(ratios_array):.3f}")
                print(f"  🎯 Écart-type: {np.std(ratios_array):.3f}")
                print(f"  ⬇️ Min: {np.min(ratios_array):.3f}")
                print(f"  ⬆️ Max: {np.max(ratios_array):.3f}")
                
                # Détection d'outliers (méthode IQR)
                q1, q3 = np.percentile(ratios_array, [25, 75])
                iqr = q3 - q1
                outliers = ratios_array[(ratios_array < q1 - 1.5*iqr) | 
                                      (ratios_array > q3 + 1.5*iqr)]
                
                if len(outliers) > 0:
                    print(f"  ⚠️ Outliers: {len(outliers)} ({len(outliers)/len(ratios)*100:.1f}%)")

def main():
    """
    Fonction principale d'analyse entropique experte
    """
    print("🎓 MAÎTRE DE L'ENTROPIE - ANALYSE EXPERTE DES RATIOS")
    print("=" * 60)
    print("📚 Théorie appliquée : Shannon + Information Mutuelle + Classification")
    print()
    
    # Initialisation de l'analyseur expert
    analyseur = AnalyseurEntropiqueRatios()
    
    # Chargement des données
    analyseur.charger_donnees_fichier('ratios_difft_diffentg_manuel.txt')
    
    # Phase 1 : Analyse d'information mutuelle
    resultats_im = analyseur.analyser_information_mutuelle()
    
    # Phase 2 : Test des règles de classification
    resultats_regles = analyseur.analyser_regles_classification()
    
    # Phase 3 : Analyse des distributions
    analyseur.analyser_distribution_ratios()
    
    print("\n🎯 ANALYSE ENTROPIQUE TERMINÉE")
    print("📊 Résultats basés sur la théorie de l'information avancée")

if __name__ == "__main__":
    main()
