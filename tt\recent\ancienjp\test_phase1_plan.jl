# Tests de validation PHASE 1 selon plan.txt (lignes 685-698)
include("julia_entropy_core.jl")
using .JuliaEntropyCore

println("=== TESTS PHASE 1 SELON PLAN.TXT ===")

# Test indexation 1-based (lignes 687-691 plan)
test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"]
println("1. Test indexation 1-based:")
println("   length(test_sequence) == 3: ", length(test_sequence) == 3)
println("   test_sequence[1] == \"1_A_BANKER\": ", test_sequence[1] == "1_A_BANKER")
println("   test_sequence[end] == \"1_C_TIE\": ", test_sequence[end] == "1_C_TIE")

# Test calculs entropiques (lignes 693-697 plan)
println("\n2. Test calculs entropiques:")
try
    # Test probabilités normalisées
    total_prob = sum(values(THEORETICAL_PROBS_ONE_BASED))
    println("   Probabilités normalisées: ", abs(total_prob - 1.0) < 1e-10)
    
    # Test Shannon entropy
    shannon_result = calculate_shannon_entropy_one_based([0.5, 0.3, 0.2])
    println("   Shannon entropy > 0: ", shannon_result > 0.0)
    println("   Shannon entropy: ", shannon_result)
    
    # Test AEP entropy
    aep_result = calculate_sequence_entropy_aep_one_based(test_sequence)
    println("   AEP entropy > 0: ", aep_result > 0.0)
    println("   AEP entropy: ", aep_result)
    
    println("\n✅ PHASE 1 VALIDÉE SELON PLAN.TXT")
    
catch e
    println("❌ ERREUR PHASE 1: ", e)
end
