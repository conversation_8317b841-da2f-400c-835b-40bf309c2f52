"""
JuliaValidationEngine.jl - Module Julia pour validation et statistiques INDEX5
CRÉATION AVEC LECTURE COMPLÈTE INTÉGRALE DE CHAQUE FICHIER TEXTE

PRIORITÉ 5 selon plan.txt (lignes 841-845)
Migré depuis INDEX5PredictionValidator (lignes 3041-3220) avec indexation 1-based
QUALITÉ ARTISANALE - CHAQUE FICHIER TEXTE LU INTÉGRALEMENT
"""

module JuliaValidationEngine

using Printf  # Import nécessaire pour @sprintf
using ..JuliaEntropyCore
using ..JuliaMetricsCalculator
using ..JuliaPredictionEngine
using ..JuliaDifferentialAnalyzer

export validate_prediction_one_based, get_accuracy_stats_one_based,
       get_detailed_report_one_based, reset_validation_one_based,
       extract_confidence_one_based, extract_index3_one_based,
       apply_index1_constraint_one_based

# Structure pour stocker les statistiques de validation
mutable struct ValidationStats
    correct_predictions::Int64
    total_predictions::Int64
    correct_predictions_high_confidence::Int64
    total_predictions_high_confidence::Int64
    prediction_details::Vector{Dict{String, Any}}
    
    ValidationStats() = new(0, 0, 0, 0, Dict{String, Any}[])
end

# Instance globale pour les statistiques
const VALIDATION_STATS = ValidationStats()

"""
Extrait INDEX3 d'une valeur INDEX5
LECTURE COMPLÈTE INTÉGRALE: extract_index3.txt (30 lignes)
Lignes source: 3041-3065

Format: INDEX1_INDEX2_INDEX3 → retourne INDEX3
"""
function extract_index3_one_based(index5_value::Union{String, Nothing})::Union{String, Nothing}
    if index5_value === nothing || index5_value == "N/A"
        return nothing
    end

    # Nettoyer la valeur (enlever score de confiance si présent)
    clean_value = occursin('(', string(index5_value)) ? split(string(index5_value), '(')[1] : string(index5_value)

    # Diviser par underscore et prendre le dernier élément (INDEX3)
    parts = split(clean_value, '_')
    if length(parts) >= 3
        index3 = parts[3]  # INDEX3 (BANKER, PLAYER, TIE) - Indexation 1-based

        # Normaliser les abréviations vers les formes complètes
        if index3 == "BANK"
            return "BANKER"
        elseif index3 == "PLAY"
            return "PLAYER"
        else
            return index3  # TIE reste TIE
        end
    end

    return nothing
end

"""
Extrait le score de confiance d'une prédiction
LECTURE COMPLÈTE INTÉGRALE: extract_confidence.txt (19 lignes)
Lignes source: 3067-3080

Format: INDEX5(0.XX) → retourne 0.XX comme float
"""
function extract_confidence_one_based(predicted_index5::Union{String, Nothing})::Float64
    if predicted_index5 === nothing || !occursin('(', string(predicted_index5))
        return 0.0
    end

    try
        # Extraire la partie entre parenthèses
        confidence_part = split(split(string(predicted_index5), '(')[2], ')')[1]  # Indexation 1-based
        return parse(Float64, confidence_part)
    catch
        return 0.0
    end
end

"""
Valide une prédiction en comparant les INDEX3
LECTURE COMPLÈTE INTÉGRALE: validate_prediction.txt (58 lignes)
Lignes source: 3082-3134

Ignore les prédictions "WAIT"
"""
function validate_prediction_one_based(predicted_index5::Union{String, Nothing}, actual_index5::String, position::Int64)::Union{Bool, Nothing}
    # Ignorer les prédictions WAIT
    if predicted_index5 === nothing || predicted_index5 == "WAIT"
        return nothing
    end

    # Extraire INDEX3 de la prédiction (le nettoyage est fait dans extract_index3)
    predicted_index3 = extract_index3_one_based(predicted_index5)

    # Extraire INDEX3 de la valeur réelle
    actual_index3 = extract_index3_one_based(actual_index5)

    # Vérifier si les deux INDEX3 sont valides
    if predicted_index3 !== nothing && actual_index3 !== nothing
        # RÈGLE TIE: Si réalité = TIE et prédiction ≠ TIE → NE PAS COMPTER
        if actual_index3 == "TIE" && predicted_index3 != "TIE"
            # Ne pas compter cette prédiction (ni valide ni invalide)
            return nothing
        end

        # Extraire le score de confiance
        confidence = extract_confidence_one_based(predicted_index5)

        VALIDATION_STATS.total_predictions += 1
        is_correct = predicted_index3 == actual_index3

        if is_correct
            VALIDATION_STATS.correct_predictions += 1
        end

        # Compteur spécial pour poids pondéré >= 60% (seuil haute confiance)
        # RESTAURATION: Seuil basé sur poids pondéré cumulé comme dans l'ancien code
        if confidence >= 0.60
            VALIDATION_STATS.total_predictions_high_confidence += 1
            if is_correct
                VALIDATION_STATS.correct_predictions_high_confidence += 1
            end
        end

        # Enregistrer les détails
        push!(VALIDATION_STATS.prediction_details, Dict{String, Any}(
            "position" => position,
            "predicted_index5" => predicted_index5,
            "actual_index5" => actual_index5,
            "predicted_index3" => predicted_index3,
            "actual_index3" => actual_index3,
            "confidence" => confidence,
            "is_correct" => is_correct,
            "is_high_confidence" => confidence >= 0.60
        ))

        return is_correct
    end

    return nothing  # Prédiction non validable
end

"""
Retourne les statistiques de précision
LECTURE COMPLÈTE INTÉGRALE: get_accuracy_stats.txt (38 lignes)
Lignes source: 3136-3168
"""
function get_accuracy_stats_one_based()::Dict{String, Any}
    if VALIDATION_STATS.total_predictions == 0
        return Dict{String, Any}(
            "correct_predictions" => 0,
            "total_predictions" => 0,
            "accuracy_percentage" => 0.0,
            "accuracy_ratio" => "0/0",
            "correct_predictions_high_confidence" => 0,
            "total_predictions_high_confidence" => 0,
            "accuracy_percentage_high_confidence" => 0.0,
            "accuracy_ratio_high_confidence" => "0/0"
        )
    end

    accuracy = (VALIDATION_STATS.correct_predictions / VALIDATION_STATS.total_predictions) * 100

    # Calculer la précision pour les prédictions haute confiance (>= 60% poids pondéré)
    accuracy_high_confidence = 0.0
    if VALIDATION_STATS.total_predictions_high_confidence > 0
        accuracy_high_confidence = (VALIDATION_STATS.correct_predictions_high_confidence / VALIDATION_STATS.total_predictions_high_confidence) * 100
    end

    return Dict{String, Any}(
        "correct_predictions" => VALIDATION_STATS.correct_predictions,
        "total_predictions" => VALIDATION_STATS.total_predictions,
        "accuracy_percentage" => accuracy,
        "accuracy_ratio" => "$(VALIDATION_STATS.correct_predictions)/$(VALIDATION_STATS.total_predictions)",
        "correct_predictions_high_confidence" => VALIDATION_STATS.correct_predictions_high_confidence,
        "total_predictions_high_confidence" => VALIDATION_STATS.total_predictions_high_confidence,
        "accuracy_percentage_high_confidence" => accuracy_high_confidence,
        "accuracy_ratio_high_confidence" => "$(VALIDATION_STATS.correct_predictions_high_confidence)/$(VALIDATION_STATS.total_predictions_high_confidence)"
    )
end

"""
Retourne un rapport détaillé des prédictions
LECTURE COMPLÈTE INTÉGRALE: get_detailed_report.txt (46 lignes)
Lignes source: 3170-3210
"""
function get_detailed_report_one_based()::String
    stats = get_accuracy_stats_one_based()

    report = """
🎯 VALIDATION DES PRÉDICTIONS INDEX5 - COMPARAISON INDEX3
═══════════════════════════════════════════════════════

📊 STATISTIQUES GLOBALES:
• Prédictions correctes: $(stats["correct_predictions"])
• Total prédictions: $(stats["total_predictions"])
• Taux de réussite: $(round(stats["accuracy_percentage"], digits=2))%
• Ratio: $(stats["accuracy_ratio"])

🎯 STATISTIQUES HAUTE CONFIANCE (≥ 60% poids pondéré):
• Prédictions correctes (≥60%): $(stats["correct_predictions_high_confidence"])
• Total prédictions (≥60%): $(stats["total_predictions_high_confidence"])
• Taux de réussite (≥60%): $(round(stats["accuracy_percentage_high_confidence"], digits=2))%
• Ratio (≥60%): $(stats["accuracy_ratio_high_confidence"])

🔍 MÉTHODE DE VALIDATION:
• Format INDEX5: INDEX1_INDEX2_INDEX3
• Validation: INDEX3_prédit = INDEX3_réel
• Exemple: 0_A_BANKER → INDEX3 = BANKER
• Confiance haute: Score ≥ 0.60 (60%)
"""

    if !isempty(VALIDATION_STATS.prediction_details)
        report *= "\n📋 DÉTAIL DES PRÉDICTIONS:\n"
        report *= "Position | Prédiction → Réalité | INDEX3 Prédit → INDEX3 Réel | Confiance | Résultat\n"
        report *= "---------|---------------------|---------------------------|-----------|----------\n"

        # Afficher les 10 dernières
        last_details = length(VALIDATION_STATS.prediction_details) > 10 ?
                      VALIDATION_STATS.prediction_details[end-9:end] :
                      VALIDATION_STATS.prediction_details

        for detail in last_details
            result_symbol = detail["is_correct"] ? "✅" : "❌"
            confidence_symbol = get(detail, "is_high_confidence", false) ? "🎯" : "📊"
            confidence_display = @sprintf("%.2f", get(detail, "confidence", 0.0))
            report *= @sprintf("Main %2d  | %12s → %12s | %6s → %6s | %s%5s | %s\n",
                detail["position"], detail["predicted_index5"], detail["actual_index5"],
                detail["predicted_index3"], detail["actual_index3"],
                confidence_symbol, confidence_display, result_symbol)
        end
    end

    return report
end

"""
Remet à zéro les compteurs
LECTURE COMPLÈTE INTÉGRALE: reset.txt (14 lignes)
Lignes source: 3212-3220
"""
function reset_validation_one_based()::Nothing
    VALIDATION_STATS.correct_predictions = 0
    VALIDATION_STATS.total_predictions = 0
    VALIDATION_STATS.correct_predictions_high_confidence = 0
    VALIDATION_STATS.total_predictions_high_confidence = 0
    empty!(VALIDATION_STATS.prediction_details)
    return nothing
end

"""
Applique la contrainte INDEX1 déterministe à la prédiction
LECTURE COMPLÈTE INTÉGRALE: apply_index1_constraint.txt (42 lignes)
Lignes source: 1965-2001

Règles INDEX1:
- Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
"""
function apply_index1_constraint_one_based(current_index5::Union{String, Nothing}, predicted_index5::Union{String, Nothing})::Union{String, Nothing}
    if current_index5 === nothing || predicted_index5 === nothing
        return predicted_index5
    end

    try
        # Extraire INDEX1 et INDEX2 actuels
        current_parts = split(current_index5, '_')
        current_index1 = parse(Int64, current_parts[1])  # Indexation 1-based
        current_index2 = current_parts[2]  # Indexation 1-based

        # Calculer INDEX1 obligatoire pour n+1 selon les règles déterministes
        if current_index2 == "C"
            required_index1 = 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            required_index1 = current_index1      # Conservation obligatoire
        end

        # Extraire INDEX2 et INDEX3 de la prédiction
        predicted_parts = split(predicted_index5, '_')
        if length(predicted_parts) >= 3
            predicted_index2 = predicted_parts[2]  # Indexation 1-based
            predicted_index3 = predicted_parts[3]  # Indexation 1-based

            # Construire INDEX5 contraint avec INDEX1 déterministe
            constrained_index5 = "$(required_index1)_$(predicted_index2)_$(predicted_index3)"
            return constrained_index5
        end

    catch
        # En cas d'erreur, retourner la prédiction originale
    end

    return predicted_index5
end

# ============================================================================
# FIN DU MODULE JuliaValidationEngine - PRIORITÉ 5 COMPLÈTE
# ============================================================================
#
# RÉSUMÉ DE L'IMPLÉMENTATION :
# - 7 méthodes de validation et statistiques INDEX5 implémentées
# - 247 lignes de fichiers texte lues intégralement
# - Indexation 1-based native Julia respectée
# - Qualité artisanale avec lecture complète de chaque fichier source
#
# MÉTHODES PRINCIPALES :
# - validate_prediction_one_based : Validation prédictions INDEX3
# - get_accuracy_stats_one_based : Statistiques de précision
# - get_detailed_report_one_based : Rapport détaillé formaté
# - extract_index3_one_based : Extraction INDEX3 depuis INDEX5
# - extract_confidence_one_based : Extraction score de confiance
#
# MÉTHODES UTILITAIRES :
# - reset_validation_one_based : Remise à zéro des compteurs
# - apply_index1_constraint_one_based : Application contraintes INDEX1
#
# ARCHITECTURE HYBRIDE JULIA-PYTHON COMPLÈTE - TOUTES PRIORITÉS TERMINÉES
# ============================================================================

end # module JuliaValidationEngine
