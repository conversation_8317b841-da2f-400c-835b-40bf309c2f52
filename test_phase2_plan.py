"""
Tests de correspondance Python-Julia selon plan.txt (lignes 701-716)
"""

import os
import sys

# CORRECTIF : Ajouter le répertoire bin de Julia au PATH AVANT tout import
julia_bin_path = r"C:\Users\<USER>\.julia\juliaup\julia-1.11.5+0.x64.w64.mingw32\bin"
current_path = os.environ.get('PATH', '')
if julia_bin_path not in current_path:
    os.environ['PATH'] = julia_bin_path + os.pathsep + current_path

from python_julia_bridge import AdvancedJuliaPythonBridge

def test_julia_python_equivalence():
    """
    Validation que les résultats Julia = résultats Python
    Selon plan.txt lignes 703-716
    """
    print("=== TESTS PHASE 2 SELON PLAN.TXT ===")
    
    sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"]
    
    # Calcul Julia (1-based) selon plan
    print("1. Test pont Julia (1-based):")
    bridge = AdvancedJuliaPythonBridge()
    
    # Test Shannon entropy
    result_julia_shannon = bridge.calculate_shannon_entropy_one_based([0.5, 0.3, 0.2])
    print(f"   Shannon entropy Julia: {result_julia_shannon}")
    
    # Test AEP entropy
    result_julia_aep = bridge.calculate_sequence_entropy_aep_one_based(sequence)
    print(f"   AEP entropy Julia: {result_julia_aep}")
    
    # Test analyse complète selon plan (lignes 740-770)
    print("\n2. Test analyze_complete_game_one_based:")
    complete_result = bridge.analyze_complete_game_one_based(sequence)
    
    required_keys = [
        'sequence_length', 'full_sequence', 'entropy_evolution',
        'final_metric_entropy', 'final_conditional_entropy', 'complexity_metrics'
    ]
    
    all_keys_present = all(key in complete_result for key in required_keys)
    print(f"   Toutes les clés requises présentes: {all_keys_present}")
    print(f"   Longueur séquence: {complete_result.get('sequence_length', 'N/A')}")
    print(f"   Évolution entropique: {len(complete_result.get('entropy_evolution', []))} positions")
    
    # Vérification équivalence selon plan (ligne 715)
    expected_shannon = 1.4854752972273344  # Valeur de référence
    equivalence_shannon = abs(result_julia_shannon - expected_shannon) < 1e-10
    print(f"\n3. Vérification équivalence (précision 1e-10):")
    print(f"   Shannon entropy équivalent: {equivalence_shannon}")
    
    if equivalence_shannon and all_keys_present:
        print("\nPHASE 2 VALIDEE SELON PLAN.TXT")
        return True
    else:
        print("\nPHASE 2 ECHOUEE")
        return False

if __name__ == "__main__":
    success = test_julia_python_equivalence()
    sys.exit(0 if success else 1)
