include("julia_entropy_core.jl")
include("julia_metrics_calculator.jl")

using .JuliaEntropyCore
using .JuliaMetricsCalculator

println("=== TEST MODULE JULIA METRICS CALCULATOR ===")

# Test avec une séquence d'exemple
test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE", "0_A_BANKER", "1_B_PLAYER"]
test_metrics = Dict{String, Any}(
    "conditional_entropy" => 3.5,
    "repetition_rate" => 0.1
)

println("1. Test calculate_context_predictability_one_based:")
result1 = calculate_context_predictability_one_based(test_sequence, test_metrics)
println("   Résultat: ", result1)

println("2. Test count_pattern_occurrences_one_based:")
pattern = ["1_A_BANKER", "0_B_PLAYER"]
result2 = count_pattern_occurrences_one_based(pattern, test_sequence)
println("   Résultat: ", result2)

println("3. Test calculate_pattern_strength_one_based:")
result3 = calculate_pattern_strength_one_based(test_sequence, pattern)
println("   Résultat: ", result3)

println("4. Test calculate_bayesian_divergence_score_one_based:")
result4 = calculate_bayesian_divergence_score_one_based(test_sequence)
println("   Résultat: ", result4)

println("5. Test calculate_all_metrics_one_based:")
entropy_evolution = [
    Dict{String, Any}("simple_entropy" => 2.1),
    Dict{String, Any}("simple_entropy" => 2.3)
]
result5 = calculate_all_metrics_one_based(test_sequence, test_metrics, entropy_evolution)
println("   Nombre de métriques calculées: ", length(result5))

println("\n✅ PRIORITÉ 2 - 16 MÉTHODES JULIA METRICS CALCULATOR TESTÉES")
println("Module JuliaMetricsCalculator opérationnel avec indexation 1-based")
