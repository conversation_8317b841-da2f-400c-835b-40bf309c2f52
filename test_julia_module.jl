include("julia_entropy_core.jl")
using .JuliaEntropyCore

println("=== TEST MODULE JULIA SELON PLAN.TXT ===")

println("1. Module charge avec succes")

println("2. Test probabilites normalisees:")
total_prob = sum(values(THEORETICAL_PROBS_ONE_BASED))
println("Somme des probabilites: ", total_prob)

if abs(total_prob - 1.0) < 1e-10
    println("OK: Probabilites correctement normalisees")
else
    println("ERREUR: Probabilites mal normalisees")
end

println("3. Test fonction safe_log_one_based:")
test_result = safe_log_one_based([0.5, 0.3, 0.2])
println("Resultat: ", test_result)

println("4. Test fonction calculate_shannon_entropy_one_based:")
shannon_result = calculate_shannon_entropy_one_based([0.5, 0.3, 0.2])
println("Shannon entropy: ", shannon_result)

println("5. Test fonction calculate_sequence_entropy_aep_one_based:")
test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"]
aep_result = calculate_sequence_entropy_aep_one_based(test_sequence)
println("AEP entropy: ", aep_result)

println("=== TESTS TERMINES ===")
