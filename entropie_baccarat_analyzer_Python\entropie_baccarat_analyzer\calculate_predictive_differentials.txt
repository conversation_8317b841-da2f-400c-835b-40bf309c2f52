# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2758 à 2861
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

    def calculate_predictive_differentials(self, sequence, evolution, position, analyzer):
        """
        Calcule les différentiels prédictifs pour les 9 valeurs INDEX5 possibles à la main n+1

        ALGORITHME CORRIGÉ :
        1. À la main n : Récupérer les métriques actuelles (Conditionnelle, Taux, DivEntropG, EntropG)
        2. Pour chacune des 9 valeurs INDEX5 possibles à n+1 (selon règles INDEX1) :
           - Simuler l'ajout de cette valeur à la séquence
           - Calculer les nouvelles métriques pour la main n+1 simulée
           - Calculer |métrique(n+1) - métrique(n)| pour chaque métrique

        CORRECTION : Utilise un cache pour éviter les recalculs entre tableaux différentiel et scores
        """
        if position >= len(sequence) or position >= len(evolution):
            return {}

        # CORRECTION : Vérifier le cache d'abord
        cache_key = (tuple(sequence), position, id(analyzer))
        if cache_key in self._differential_cache:
            return self._differential_cache[cache_key]

        # CORRECTION : Pour prédire la main n+1, il faut utiliser la main n-1 comme référence pour les règles INDEX1
        if position == 0:
            return {}  # Pas de prédiction possible pour la première main (pas de main précédente)

        # ÉTAPE 1: Obtenir l'INDEX5 de référence (main précédente) et les métriques actuelles
        reference_index5 = sequence[position - 1]  # ✅ CORRECTION : Main précédente pour les règles INDEX1
        current_metrics = evolution[position]       # Métriques de la main actuelle

        # Métriques actuelles à la main n
        current_conditional = current_metrics.get('conditional_entropy', 0.0)

        # CORRECTION HOMOGÈNE: Calculer le taux actuel sur bloc local [n-3,n-2,n-1,n]
        if len(sequence) >= 4:
            local_block_current = sequence[position-3:position+1]  # [n-3,n-2,n-1,n]
            current_rate = analyzer._calculate_sequence_entropy_aep(local_block_current)
        else:
            current_rate = current_metrics.get('entropy_rate', 0.0)

        current_simple = current_metrics.get('simple_entropy', 0.0)
        current_theoretical = current_metrics.get('simple_entropy_theoretical', 0.0)

        # ÉTAPE 2: Calculer INDEX1 requis pour la main n+1 selon les règles déterministes
        # CORRECTION : Utiliser la main de référence (n-1) pour appliquer les règles INDEX1
        required_index1 = self.calculate_required_index1(reference_index5)
        if required_index1 is None:
            return {}

        # ÉTAPE 3: Obtenir les 9 valeurs INDEX5 possibles pour la main n+1
        valid_index5_values = self.get_valid_index5_values(required_index1)

        # ÉTAPE 4: Calculer les différentiels pour chaque valeur INDEX5 possible à n+1
        predictive_differentials = {}

        for possible_index5 in valid_index5_values:
            try:
                # ÉTAPE 4.1: Créer une séquence simulée avec la valeur possible ajoutée à n+1
                simulated_sequence = sequence[:position+1] + [possible_index5]

                # ÉTAPE 4.2: Calculer les métriques pour la main n+1 simulée

                # Entropie conditionnelle pour la séquence simulée complète
                simulated_conditional = analyzer._calculate_conditional_entropy(simulated_sequence)

                # Entropie simple (Shannon) pour la séquence simulée complète
                # Calculer les fréquences de la séquence simulée
                from collections import Counter
                counts = Counter(simulated_sequence)
                total = len(simulated_sequence)
                probabilities = [counts[value] / total for value in counts.keys()]
                simulated_simple = analyzer._calculate_shannon_entropy(probabilities)

                # Entropie théorique (AEP) pour la séquence simulée complète
                simulated_theoretical = analyzer._calculate_sequence_entropy_aep(simulated_sequence)

                # CORRECTION HOMOGÈNE: Taux d'entropie sur bloc local de longueur 4
                # Bloc actuel: [n-3,n-2,n-1,n] vs Bloc simulé: [n-2,n-1,n,n+1simulé]
                if len(simulated_sequence) >= 4:
                    # Calculer l'entropie AEP du bloc local de 4 éléments [n-2,n-1,n,n+1simulé]
                    local_block_simulated = simulated_sequence[-4:]  # 4 derniers éléments
                    simulated_rate = analyzer._calculate_sequence_entropy_aep(local_block_simulated)
                else:
                    simulated_rate = simulated_simple

                # ÉTAPE 4.3: Calculer les différentiels absolus |métrique(n+1) - métrique(n)|
                diff_cond = abs(simulated_conditional - current_conditional)
                diff_taux = abs(simulated_rate - current_rate)
                diff_div_entrop = abs(simulated_simple - current_simple)
                diff_entrop = abs(simulated_theoretical - current_theoretical)

            except Exception as e:
                # En cas d'erreur, utiliser des valeurs par défaut
                diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0

            predictive_differentials[possible_index5] = {
                'DiffCond': diff_cond,
                'DiffTaux': diff_taux,
                'DiffDivEntropG': diff_div_entrop,
                'DiffEntropG': diff_entrop
            }

        # CORRECTION : Sauvegarder dans le cache avant de retourner
        self._differential_cache[cache_key] = predictive_differentials
        return predictive_differentials