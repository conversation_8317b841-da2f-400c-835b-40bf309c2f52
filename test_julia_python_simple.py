"""
Test simple de l'interface Julia-Python
"""

print("Test 1: Import julia")
try:
    import julia
    print("OK Module julia importe avec succes")
except Exception as e:
    print(f"ERREUR import julia: {e}")
    exit(1)

print("\nTest 2: Initialisation Julia")
try:
    from julia import Main
    print("OK Julia Main importe avec succes")
except Exception as e:
    print(f"ERREUR Julia Main: {e}")
    exit(1)

print("\nTest 3: Evaluation simple")
try:
    result = Main.eval("2 + 2")
    print(f"OK Evaluation simple reussie: 2 + 2 = {result}")
except Exception as e:
    print(f"ERREUR evaluation simple: {e}")
    exit(1)

print("\nTest 4: Test avec notre module")
try:
    Main.include("julia_entropy_core.jl")
    print("OK Module julia_entropy_core.jl charge")
except Exception as e:
    print(f"ERREUR chargement module: {e}")
    exit(1)

print("\nTest 5: Using module")
try:
    Main.eval("using .JuliaEntropyCore")
    print("OK Module JuliaEntropyCore utilise")
except Exception as e:
    print(f"ERREUR using module: {e}")
    exit(1)

print("\nTest 6: Test fonction simple")
try:
    result = Main.eval("JuliaEntropyCore.safe_log_one_based([0.5, 0.3, 0.2])")
    print(f"OK Fonction safe_log_one_based reussie: {result}")
except Exception as e:
    print(f"ERREUR fonction: {e}")
    exit(1)

print("\nOK Tous les tests reussis - Julia-Python fonctionne !")
