# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2249 à 2263
# Type: Méthode de la classe INDEX5Predictor

    def predict_frequency_based(self, sequence_history, metrics):
        """
        Prédiction basée sur les fréquences observées
        """
        from collections import Counter

        # Analyser les fréquences récentes
        recent_sequence = sequence_history[-30:] if len(sequence_history) >= 30 else sequence_history
        freq_counter = Counter(recent_sequence)

        if freq_counter:
            # Retourner la valeur la plus fréquente récemment
            return freq_counter.most_common(1)[0][0]

        return None