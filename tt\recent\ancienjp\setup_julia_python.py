"""
setup_julia_python.py - Configuration de l'architecture hybride Julia-Python
Selon plan.txt - Installation et tests complets

Installe les dépendances et configure l'environnement hybride
"""

import subprocess
import sys
import os
import time
from typing import List, Dict, Any

def install_julia_dependencies():
    """
    Installe les dépendances Julia nécessaires
    """
    print("Installation des dependances Julia...")

    try:
        # Vérifier si Julia est installé
        result = subprocess.run(['julia', '--version'], capture_output=True, text=True)
        if result.returncode != 0:
            print("Julia n'est pas installe. Veuillez installer Julia d'abord.")
            return False

        print(f"Julia detecte: {result.stdout.strip()}")

        # Installer les packages Julia nécessaires
        julia_packages = ['Test']  # Packages de base pour les tests

        for package in julia_packages:
            print(f"Installation du package Julia: {package}")
            cmd = f'julia -e "using Pkg; Pkg.add(\\"{package}\\"); using {package}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"Package {package} installe avec succes")
            else:
                print(f"Avertissement lors de l'installation de {package}: {result.stderr}")

        return True

    except FileNotFoundError:
        print("Julia n'est pas trouve dans le PATH. Veuillez installer Julia.")
        return False
    except Exception as e:
        print(f"Erreur lors de l'installation des dependances Julia: {e}")
        return False

def install_python_dependencies():
    """
    Installe les dépendances Python nécessaires
    """
    print("Installation des dependances Python...")

    python_packages = [
        'julia',  # Package Python pour interfacer avec Julia
        'numpy',
        'matplotlib'  # Pour les visualisations
    ]

    for package in python_packages:
        try:
            print(f"Installation du package Python: {package}")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"Package {package} installe avec succes")
        except subprocess.CalledProcessError as e:
            print(f"Erreur lors de l'installation de {package}: {e}")
            return False

    return True

def test_julia_module():
    """
    Teste le module Julia
    """
    print("Test du module Julia...")

    try:
        # Test basique du module Julia
        julia_test_code = '''
        include("julia_entropy_core.jl")
        using .JuliaEntropyCore

        # Test indexation 1-based
        test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"]
        println("Longueur sequence: ", length(test_sequence))
        println("Premier element: ", test_sequence[1])
        println("Dernier element: ", test_sequence[end])

        # Test calculs entropiques
        probs = [0.5, 0.3, 0.2]
        shannon_result = calculate_shannon_entropy_one_based(probs)
        println("Shannon entropy: ", shannon_result)

        aep_result = calculate_sequence_entropy_aep_one_based(test_sequence)
        println("AEP entropy: ", aep_result)

        println("Tests Julia reussis")
        '''
        
        # Écrire le code de test dans un fichier temporaire
        with open('test_julia_temp.jl', 'w') as f:
            f.write(julia_test_code)
        
        # Exécuter le test Julia
        result = subprocess.run(['julia', 'test_julia_temp.jl'], 
                              capture_output=True, text=True, timeout=30)
        
        # Nettoyer le fichier temporaire
        if os.path.exists('test_julia_temp.jl'):
            os.remove('test_julia_temp.jl')
        
        if result.returncode == 0:
            print("Module Julia teste avec succes")
            print("Sortie Julia:")
            print(result.stdout)
            return True
        else:
            print("Erreur lors du test Julia:")
            print(result.stderr)
            return False

    except subprocess.TimeoutExpired:
        print("Timeout lors du test Julia")
        return False
    except Exception as e:
        print(f"Erreur lors du test Julia: {e}")
        return False

def test_python_julia_bridge():
    """
    Teste le pont Python-Julia
    """
    print("Test du pont Python-Julia...")

    try:
        # Importer et tester le pont
        from python_julia_bridge import AdvancedJuliaPythonBridge, test_julia_python_bridge

        # Exécuter les tests
        success = test_julia_python_bridge()

        if success:
            print("Pont Python-Julia teste avec succes")
            return True
        else:
            print("Echec des tests du pont Python-Julia")
            return False

    except ImportError as e:
        print(f"Erreur d'importation du pont: {e}")
        return False
    except Exception as e:
        print(f"Erreur lors du test du pont: {e}")
        return False

def benchmark_performance():
    """
    Benchmark de performance Julia vs Python
    """
    print("Benchmark de performance...")

    try:
        from python_julia_bridge import AdvancedJuliaPythonBridge

        # Créer une séquence de test plus longue
        test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"] * 100  # 300 éléments

        bridge = AdvancedJuliaPythonBridge()

        # Test Julia
        start_time = time.time()
        result_julia = bridge.analyze_complete_game_one_based(test_sequence)
        julia_time = time.time() - start_time

        print(f"Temps Julia: {julia_time:.3f}s")
        print(f"Metriques calculees: {len(result_julia)}")
        print(f"Sequence analysee: {result_julia['sequence_length']} elements")

        return True

    except Exception as e:
        print(f"Erreur lors du benchmark: {e}")
        return False

def main():
    """
    Configuration complète de l'architecture hybride
    """
    print("CONFIGURATION ARCHITECTURE HYBRIDE JULIA-PYTHON")
    print("=" * 60)
    
    success_steps = []
    
    # Étape 1: Installation des dépendances Python
    if install_python_dependencies():
        success_steps.append("Dependances Python")
    else:
        print("Echec installation dependances Python")
        return False

    # Étape 2: Installation des dépendances Julia
    if install_julia_dependencies():
        success_steps.append("Dependances Julia")
    else:
        print("Echec installation dependances Julia")
        return False

    # Étape 3: Test du module Julia
    if test_julia_module():
        success_steps.append("Module Julia")
    else:
        print("Echec test module Julia")
        return False

    # Étape 4: Test du pont Python-Julia
    if test_python_julia_bridge():
        success_steps.append("Pont Python-Julia")
    else:
        print("Echec test pont Python-Julia")
        return False

    # Étape 5: Benchmark de performance
    if benchmark_performance():
        success_steps.append("Benchmark performance")
    else:
        print("Benchmark echoue (non critique)")
    
    # Résumé final
    print("\n" + "=" * 60)
    print("CONFIGURATION TERMINEE")
    print("=" * 60)

    for step in success_steps:
        print(step)

    print("\nArchitecture hybride Julia-Python prete a l'emploi !")
    print("Fichiers crees:")
    print("   - julia_entropy_core.jl (Module Julia)")
    print("   - python_julia_bridge.py (Pont Python-Julia)")
    print("   - setup_julia_python.py (Configuration)")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
