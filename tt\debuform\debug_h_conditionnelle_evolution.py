#!/usr/bin/env python3
"""Debug de H_conditionnelle dans l'évolution entropique"""

def debug_h_conditionnelle_evolution():
    """Vérifie comment H_conditionnelle est calculée pour chaque main"""
    
    # Séquence complète
    sequence = ['0_A_BANKER', '1_B_PLAYER', '0_C_BANKER', '1_A_PLAYER', '0_B_BANKER']
    mains = [6, 7, 8, 9, 10]
    
    print("🔍 DEBUG H_CONDITIONNELLE DANS L'ÉVOLUTION")
    print("=" * 60)
    print("📊 Séquence complète:")
    for i, (main, symbol) in enumerate(zip(mains, sequence)):
        print(f"   Position {i}: Main {main} = '{symbol}'")
    print()
    
    # Simulation de l'évolution entropique
    print("🔄 CALCUL H_CONDITIONNELLE POUR CHAQUE MAIN:")
    print("-" * 60)
    
    for n in range(2, len(sequence) + 1):  # Commence à n=2 (minimum pour H_conditionnelle)
        main_actuelle = mains[n-1]  # Main correspondant à la position n-1
        subsequence = sequence[:n]  # Sous-séquence de longueur n
        
        print(f"\n🎯 MAIN {main_actuelle} (position {n-1}, longueur sous-séquence: {n})")
        print(f"   Sous-séquence: {subsequence}")
        
        # Simulation du calcul H_conditionnelle sur cette sous-séquence
        context_length = 1
        transitions = []
        
        for i in range(len(subsequence) - context_length):
            context = subsequence[i]
            next_symbol = subsequence[i+1]
            main_context = mains[i]
            main_next = mains[i+1]
            transitions.append((context, next_symbol, main_context, main_next))
            print(f"     Transition {i}: '{context}' (Main {main_context}) → '{next_symbol}' (Main {main_next})")
        
        # Analyser la dernière transition
        if transitions:
            last_transition = transitions[-1]
            context, next_symbol, main_context, main_next = last_transition
            
            print(f"   📋 Dernière transition: Main {main_context} → Main {main_next}")
            print(f"   📋 H_conditionnelle(Main {main_actuelle}) inclut la prédiction vers Main {main_next}")
            
            if main_next == main_actuelle:
                print(f"   ✅ H_conditionnelle(Main {main_actuelle}) = H(X_Main_{main_actuelle} | X_Main_{main_context})")
            else:
                print(f"   ⚠️  H_conditionnelle(Main {main_actuelle}) inclut prédiction vers Main {main_next}")
    
    print()
    print("🎯 ANALYSE TEMPORELLE:")
    print("-" * 40)
    print("Pour Main n, la sous-séquence va de Main 6 à Main n")
    print("H_conditionnelle(Main n) calcule toutes les transitions dans cette sous-séquence")
    print("La dernière transition prédit de Main n-1 vers Main n")
    print()
    print("📋 FORMULE CORRECTE:")
    print("H_conditionnelle(Main n) = H(X_Main_n | X_Main_n-1, ..., X_Main_6)")
    print("                          ≈ H(X_Main_n | X_Main_n-1) avec contexte de longueur 1")

if __name__ == "__main__":
    debug_h_conditionnelle_evolution()
