% This LaTeX document needs to be compiled with XeLaTeX.
\documentclass[10pt]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage[version=4]{mhchem}
\usepackage{stmaryrd}
\usepackage{bbold}
\usepackage{graphicx}
\usepackage[export]{adjustbox}
\graphicspath{ {./images/} }
\usepackage[fallback]{xeCJK}
\usepackage{polyglossia}
\usepackage{fontspec}
\IfFontExistsTF{Noto Serif CJK TC}
{\setCJKmainfont{Noto Serif CJK TC}}
{\IfFontExistsTF{STSong}
  {\setCJKmainfont{STSong}}
  {\IfFontExistsTF{Droid Sans Fallback}
    {\setCJKmainfont{Droid Sans Fallback}}
    {\setCJKmainfont{SimSun}}
}}

\setmainlanguage{french}
\IfFontExistsTF{CMU Serif}
{\setmainfont{CMU Serif}}
{\IfFontExistsTF{DejaVu Sans}
  {\setmainfont{DejaVu Sans}}
  {\setmainfont{Georgia}}
}

\title{Entropie }

\author{P. Pansu}
\date{}


\begin{document}
\maketitle
20 mai 2012

\section*{Conventions}
En bleu, les paragraphes qui n'ont pas été traités, ou alors de façon évasive.

\section*{1 Motivation}
\subsection*{1.1 L'entropie dans les sciences}
L'entropie est une invention de physiciens. Elle apparait en thermodynamique macroscopique, comme une nécessité théorique : le second principe de la thermodynamique exprime seulement son existence (Clausius 1854). Il y a une formule pour sa variation lors de certaines transformations, mais pas d'expression directe.

La physique statistique (Boltzmann) donne de nouvelles fondations, microscopiques, à la thermodynamique, et fournit enfin une expression pour l'entropie d'un système : c'est le logarithme du nombre de configurations microscopiques possibles pour le système.

Après la seconde guerre mondiale, l'essor des communications et de nouveaux problèmes (codage, compression) donnent naissance à la théorie de l'information aux Etats-Unis. En 1948, Claude Shannon introduit l'entropie d'une distribution de probabilité finie, l'information mutuelle de deux distributions de probabilité, et la capacité d'un canal de transmission comme l'information mutuelle maximale entre les signaux d'entrée et de sortie. Il prouve que cette capacité est la bien la borne supérieure des taux de transmission autorisés par le canal. Non seulement ces notions capturent bien l'idée de quantité d'information nécessaire pour décrire une variable aléatoire, mais elles permettent de fonder l'entropie des physiciens sur des bases solides.

En URSS, Andrei Kolmogorov et Yakov Sinai définissent en 1958 l'entropie d'une transformation préservant la mesure. En 1966, Roy Adler, Alan Konheim et M. McAndrew introduisent l'entropie topologique d'une transformation continue d'un espace topologique compact, les deux notions sont très utiles en systèmes dynamiques. Kolmogorov, et, aux Etats-Unis, Ray Solomonov et Gregory Chaitin, définissent vers 1965 l'entropie d'un mot écrit dans un alphabet fini comme la longueur minimale d'un programme permettant à un ordinateur de produire ce mot. Cette notion s'avère étroitement reliée à l'entropie de Shannon.

L'entropie est une notion récente. Elle a déjà envahi beaucoup de domaines des mathématiques (probabilités, statistiques, systèmes dynamiques) et aussi de la physique et de l'informatique théorique. C'est le signe de son intérêt.

\subsection*{1.2 Simulation d'une distribution de probabilité}
On lance un dé truqué \(n\) fois. Truqué signifie que, pour \(k=1, \ldots, 6\), la probabilité \(p(k)\) de sortie du chiffre \(k\) n'est pas égale à \(\frac{1}{6}\). On obtient des suites \(\left(x_{1}, \ldots, x_{n}\right), x_{i} \in\{1, \ldots, 6\}\). Quelle est la probabilité d'obtenir une suite "typique". Par suite typique, on veut dire toutes sauf une fraction qui tend vers 0 quand \(n\) tend vers l'infini.

La probabilité d'obtenir la suite \(\left(x_{1}, \ldots, x_{n}\right)\) est le produit

\[
p\left(x_{1}\right) p\left(x_{2}\right) \cdots p\left(x_{n}\right)=\prod_{k=1}^{6} p(k)^{N_{k}}
\]

où \(N_{k}\) est le nombre de fois que le chiffre \(k\) apparait dans la suite \(\left(x_{1}, \ldots, x_{n}\right)\). Or, d'après la loi des grands nombres, lorsque \(n\) est grand, avec forte probabilité, ce nombre \(N_{k}\) est proche de \(n p(k)\). Donc, pour la très grande majorité des tirages, la probabilité cherchée est proche de

\[
\prod_{k=1}^{6} p(k)^{n p(k)}=\left(\prod_{k=1}^{6} p(k)^{p(k)}\right)^{n}=2^{-n H}
\]

où

\[
H=-\sum_{k=1}^{6} p(k) \log _{2}(p(k))
\]

Pour un dé non pipé, toutes les suites sont obtenues avec la même probabilité \(2^{-n \log _{2}(6)}\). Pour un dé pipé, la répartition est inégale. La suite typique est obtenue avec une probabilité plus forte, et cette probabilité s'exprime au moyen de la quantité \(H\). On appelle \(H\) l'entropie de la distribution de probabilité \(p\).

Le fait que \(2^{-n H} \geq 2^{-n \log _{2}(6)}\) ne saute pas aux yeux. C'est l'une des nombreuses identités et inégalités qui constituent les paroles de la théorie de l'information. La musique, ce sont les nombreuses interprétations qu'on peut donner des résultats dans différents champs de la science. On va commencer par établir un certain nombre d'identités et d'inégalités. On passera ensuite aux interprétations.

\subsection*{1.3 Bibliographie}
On suit de près, et dans cet ordre, les chapitres \(2,5,4,3,7,16\) du livre\\
Thomas Cover and Joy Thomas, Elements of Information Theory, John Wiley and Sons, Hoboken, NJ (2006). Cote 003.54 COV ele à la BU (rez de jardin).

Puis on passe aux livres\\
Vladimir Arnold et André Avez, Problèmes ergodiques de la mécanique classique, GauthierVillars, Paris (1967). Chapitre 12 et appendices 18 et 19. Ne se trouve pas à la BU, mais à la Bibliothèque Jacques Hadamard.

Karl Petersen, Ergodic Theory, Cambridge Studies in Advanced Mathematics, 2. Cambridge University Press, Cambridge (1989). Chapitres 5 et 6. En magasin, cote K41366 à la BU.

\section*{2 Premières propriétés}
\subsection*{2.1 Définition}
Définition 1 Soit \(E\) un ensemble fini. Une distribution de probabilité sur \(E\), c'est une fonction \(p: E \rightarrow \mathbb{R}_{+}\)telle que \(\sum_{x \in E} p(x)=1\).\\
\(L\) 'entropie de la distribution de probabilité \(p\) est le nombre

\[
H(p)=-\sum_{x \in E} p(x) \log _{2}(p(x))
\]

Elle est mesurée en bits.\\
Par convention, \(0 \log _{2}(0)=0\). Remarquer que \(H(p) \geq 0\), avec égalité si et seulement si \(p\) est concentrée sur un seul élément de \(E\).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-03(1)}

Exemple 2 L'entropie de la distribution uniforme sur \(E\) vaut \(\log _{2}|E|\) bits.\\
Une distribution uniforme sur un ensemble \(E\) à \(2^{n}\) éléments a donc une entropie de \(n\) bits. D'ailleurs, on peut numéroter les éléments de \(E\) avec \(n\) bits seulement. Plus généralement, on verra plus tard que l'entropie est aussi le nombre moyen (au sens de la distribution \(p\) ) de bits nécessaire pour coder \(E\). C'est pourquoi on prend le logarithme en base 2 et l'unité choisie est appelée bit.

Suggestion d'exercice : \(\mathrm{n}^{0} 1\), feuille 1 .\\
Exemple 3 Soit \(a \in[0,1]\). Soit \(E=\{0,1\}\). Soit \(p_{a}=\mathcal{B}(a)\) la distribution de Bernoulli de paramètre a, i.e \(p_{a}(1)=a, p_{a}(0)=1-a\). L'entropie de \(\mathcal{B}(a)\) vaut \(h(a):=-a \log _{2}(a)-(1-\) a) \(\log _{2}(1-a)\) bits.

Voir courbe représentative de \(h\).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-03}

On constate que \(h\) est une fonction concave de \(a\). On va le démontrer bientôt (Exemple 39).

\subsection*{2.2 Vocabulaire probabiliste}
On fixe une fois pour toutes un ensemble \(\Omega\) muni d'une tribu et d'une mesure de probabilité \(\mathbb{P}\). Soit \(E\) un ensemble muni d'une tribu (ce sera pratiquement toujours un ensemble fini muni\\
de la tribu de tous ses sous-ensembles). Une variable aléatoire à valeurs dans \(E\) est une fonction mesurable \(X: \Omega \rightarrow E\). La loi ou la distribution de \(X\) est la distribution de probabilité \(p_{X}\) sur \(E\) définie par

\[
p_{X}(x)=\mathbb{P}(X=x)
\]

L'espérance de \(X\) est le nombre

\[
\mathbb{E}(X)=\int_{\Omega} X d \mathbb{P}=\int_{E} x d p_{X}(x)=\sum_{x \in E} x p_{X}(x)
\]

dans le cas où \(E\) est fini.\\
Notation 4 Si \(X\) est une variable aléatoire de distribution \(p_{X}\), on note \(H(X)=H\left(p_{X}\right)\), et on l'appelle l'entropie de \(X\). On peut l'exprimer comme une espérance,

\[
H(X)=\mathbb{E}\left(-\log _{2} p(X)\right)
\]

Si on cherche à mesurer la quantité d'information \(I(A)\) contenue dans l'évènement \(X \in A, A \subset E\), la formule qui s'impose est \(I(A)=\log \frac{1}{\mathbb{P}(X \in A)}\). En effet, \(I\) doit être fonction décroissante de \(\mathbb{P}(X \in A)\), et pour deux évènements \(A\) et \(B\) indépendants, on souhaite que

\[
I(A \cap B)=I(A)+I(B),
\]

donc \(I\) est positivement proportionnelle à \(\log \frac{1}{\mathbb{P}(X \in A)}\). L'entropie est donc la quantité d'information fournie en moyenne par \(X\). Tant qu'on ne connait pas \(X\), il s'agit plutôt d'une quantité d'incertitude.

Si \(X\) est constante, \(H(X)=0\). De toutes les variables de Bernoulli, c'est \(\mathcal{B}\left(\frac{1}{2}\right)\) qui est la plus incertaine.

Suggestion d'exercice : \(\mathrm{n}^{0} 2\), feuille 1 .

\subsection*{2.3 Entropie relative et information mutuelle}
Définition 5 Soient \(p\) et \(q\) deux distributions de probabilité sur le même ensemble \(E\). Leur entropie relative \(e s t\)

\[
D(p \| q):=\sum_{x \in E_{X}} p(x) \log _{2}\left(\frac{p(x)}{q(x)}\right)
\]

On verra bientôt qu'on peut penser à l'entropie relative comme à une sorte de distance entre \(p\) et \(q\) (elle est positive, elle est nulle seulement si \(p=q\) ). Toutefois, elle n'est pas symétrique. Attention, elle prend la valeur \(+\infty\) s'il existe \(x\) tel que \(q(x)=0\) mais \(p(x) \neq 0\).

Exemple 6 Soit \(\mathcal{B}(a)\) la distribution de Bernoulli sur \(\{0,1\}\), qui met le poids a sur 1 et \(1-a\) sur 0. Alors

\[
D\left(\mathcal{B}(a) \| \mathcal{B}\left(a^{\prime}\right)\right)=(1-a) \log _{2}\left(\frac{1-a}{1-a^{\prime}}\right)+a \log _{2}\left(\frac{a}{a^{\prime}}\right)
\]

n'est pas symétrique en \(a, a^{\prime}\).\\
Attention à la notation \(\|\).\\
Définition 7 Soient \(X\) et \(Y\) deux variables aléatoires. Leur information mutuelle est l'entropie relative de la loi du couple \(p_{(X, Y)}\) et du produit des lois marginales \(p_{X} \otimes p_{Y}\) sur \(E_{X} \times E_{Y}\),

\[
\begin{aligned}
I(X ; Y): & =D\left(p_{(X, Y)} \| p_{X} \otimes p_{Y}\right) \\
& =\sum_{(x, y) \in E_{X} \times E_{Y}} p_{(X, Y)}(x, y) \log _{2}\left(\frac{p_{(X, Y)}(x, y)}{p_{X}(x) p_{Y}(y)}\right) .
\end{aligned}
\]

Attention au \(;\) On verra qu'on peut interpréter \(I(X ; Y)\) comme la quantité d'information sur \(X\) qu'on gagne en apprenant \(Y\). Par définition, \(I(Y ; X)=I(X ; Y)\), donc on apprend autant sur \(X\) en découvrant \(Y\) qu'on apprend sur \(Y\) en découvrant \(X\).

Exemple 8 Si \(X\) et \(Y\) sont indépendantes, leur information mutuelle est nulle.

\subsection*{2.4 Inégalité de Jensen}
C'est l'outil utilisé pour prouver les inégalités annoncées plus haut.\\
Définition 9 Soit \(I\) un intervalle de \(\mathbb{R}\). Une fonction \(f: I \rightarrow \mathbb{R}\) est convexe si sa courbe représentative est en-dessous de toutes ses cordes. Autrement dit, si, pour tous \(x, y \in I\) et \(t \in[0,1]\),

\[
f((1-t) x+t y) \leq(1-t) f(x)+t f(y)
\]

\(f\) est concave si \(-f\) est convexe.\\
On dit que \(f\) est strictement convexe si l'inégalité est stricte dès que \(x \neq y\) et \(t \in] 0,1[\).\\
Plus généralement, si \(C \subset \mathbb{R}^{n}\) est un ensemble convexe, on dit qu'une fonction \(f: C \rightarrow \mathbb{R}\) est convexe si sa restriction à tout segment de droite est convexe.

Proposition 10 Si \(f: I \rightarrow \mathbb{R}\) est de classe \(C^{2}\) et \(f^{\prime \prime} \geq 0\), alors \(f\) est convexe. Si \(f^{\prime \prime}>0\), alors \(f\) est strictement convexe.

Preuve On pose \(g_{\epsilon}(t)=f((1-t) x+t y)-(1-t) f(x)-t f(y)-\epsilon t(1-t)\). Alors \(g(0)=g(1)=0\). Par l'absurde. Si \(g_{\epsilon}\) prend une valeur \(>0\), alors elle atteint son maximum en \(\left.c \in\right] 0,1\). En ce point, \(g_{\epsilon}^{\prime}(c)=0\) et \(g_{\epsilon}^{\prime \prime}(c) \leq 0\). Or \(g_{\epsilon}^{\prime \prime}(c)=f^{\prime \prime}((1-c) x+c y)+2 \epsilon>0\), contradiction. On conclut que \(g_{\epsilon} \leq 0\) partout. Comme ceci est vrai pour tout \(\epsilon>0\), on conclut que \(f((1-t) x+t y)-(1-t) f(x)-t f(y) \leq 0\) pour tout \(t \in[0,1]\).

Lorsque \(f^{\prime \prime}>0\) et \(x \neq y\), on modifie le raisonnement par l'absurde comme suit. On pose \(\epsilon=0\). On suppose que \(g_{0}\) prend une valeur \(\geq 0\) dans l'intervalle \(] 0,1[\). Elle atteint son maximum en \(c \in] 0,1\) [. En ce point, \(g_{0}^{\prime \prime}(c) \leq 0\). Or \(g_{0}^{\prime \prime}(c)=(y-x)^{2} f^{\prime \prime}((1-c) x+c y)>0\), contradiction. On conclut que \(g_{0}<0\) sur \(] 0,1[\), donc \(f((1-t) x+t y)-(1-t) f(x)-t f(y)<0\) pour tout \(t \in] 0,1[\).

Exemple \(11 f(x)=|x|\) est convexe, \(x^{2}, 2^{x}\) sont strictement convexes, \(f(x)=1 / x, x \log _{2} x\) sont strictement convexes sur \(] 0,+\infty\left[, f(x)=\sqrt{x}\right.\), \(\log _{2}(x)\) sont strictement concaves sur \(] 0,+\infty[\). Les fonctions affines sont à la fois convexes et concaves sur \(\mathbb{R}\).

Proposition 12 Soit \(f: I \rightarrow \mathbb{R}\) une fonction convexe. Alors \(f\) est la borne supérieure d'une famille de fonctions affines.

Preuve Soit \(x \in I\). Comme le graphe de \(f\) passe en dessous de ses cordes, la pentes des cordes issues de \(x\), i.e. le taux de variation \(y \mapsto \Delta(y)=\frac{f(y)-f(x)}{y-x}\), est une fonction croissante. Il possède donc une limite à droite \(f^{\prime}(x+)\) en \(x\). Pour \(y \geq x\),

\[
f(y)=f(x)+\Delta(y)(y-x) \geq f(x)+f^{\prime}(x+)(y-x) .
\]

Pour \(y \leq x\), la même inégalité a lieu \(\left(\Delta(y) \leq f^{\prime}(x+)\right.\) mais \(\left.y-x \leq 0\right)\). Dans les deux cas, \(f \geq a_{x}(y)\) où \(a_{x}\) est la fonction affine définie par \(a_{x}(y)=f(x)+f^{\prime}(x+)(y-x)\). Il y a égalité en \(x\). On conclut que \(f\) est la borne supérieure des fonctions affines \(a_{x}\) lorsque \(x\) décrit \(I\).

Théorème 1 (Inégalité de Jensen) Soit \(I\) un intervalle de \(\mathbb{R}\) et \(f: I \rightarrow \mathbb{R}\) une fonction convexe. Pour toute variable aléatoire \(X\) à valeurs dans \(I\),

\[
f(\mathbb{E}(X)) \leq \mathbb{E}(f(X))
\]

Si \(f\) est strictement convexe, l'égalité entraîne que \(X\) est presque partout constante.

Remarque 13 Lorsque \(X\) ne prend qu'un nombre fini \(n\) de valeurs, l'inégalité de Jensen n'est autre que

\[
f\left(\frac{\sum_{i=1}^{n} a_{i} x_{i}}{\sum_{i=1}^{n} a_{i}}\right) \leq \frac{\sum_{i=1}^{n} a_{i} f\left(x_{i}\right)}{\sum_{i=1}^{n} a_{i}}
\]

lorsque \(a_{i} \geq 0\) pour tout \(i\), avec égalité si et seulement si \(f\) prend la même valeur à tous les points \(x_{i}\) dont le poids \(a_{i}\) est non nul, dans le cas où \(f\) est strictement convexe.

Preuve Bien que ce ne soit pas nécessaire, on commence par traiter le cas fini. On raisonne par récurrence sur \(n\). Par définition, elle est vraie pour \(n=2\). Supposons l'inégalité vraie pour \(n\) points. Etant donnés \(a_{0}, a_{1}, \ldots, a_{n} \geq 0\) et \(x_{0}, x_{1}, \ldots, x_{n}\), posons \(a_{1}^{\prime}=a_{0}+a_{1}, x_{1}^{\prime}=\frac{a_{0} x_{0}+a_{1} x_{1}}{a_{0}+a_{1}}\), et, pour \(i \geq 2, a_{i}^{\prime}=a_{i}, x_{i}^{\prime}=x_{i}\). Par convexité de \(f\),

\[
f\left(x_{1}^{\prime}\right) \leq \frac{a_{0} f\left(x_{0}\right)+a_{1} f\left(x_{1}\right)}{a_{0}+a_{1}}
\]

Avec l'hypothèse de récurrence,

\[
\begin{aligned}
f\left(\frac{\sum_{i=0}^{n} a_{i} x_{i}}{\sum_{i=0}^{n} a_{i}}\right) & =f\left(\frac{\sum_{i=1}^{n} a_{i}^{\prime} x_{i}^{\prime}}{\sum_{i=1}^{n} a_{i}^{\prime}}\right) \\
& \leq \frac{\sum_{i=1}^{n} a_{i}^{\prime} f\left(x_{i}^{\prime}\right)}{\sum_{i=1}^{n} a_{i}^{\prime}} \\
& =\frac{\left(a_{0}+a_{1}\right) f\left(x_{1}^{\prime}\right)+\sum_{i=2}^{n} a_{i} f\left(x_{i}\right)}{\sum_{i=0}^{n} a_{i}} \\
& \leq \frac{\sum_{i=0}^{n} a_{i} f\left(x_{i}\right)}{\sum_{i=0}^{n} a_{i}}
\end{aligned}
\]

ce qu'il fallait démontrer. Par l'hypothèse de récurrence, l'égalité entraîne que \(f\) prend la même valeur en tous les \(x_{i}^{\prime}\) dont les poids sont non nuls, et que cette valeur est égale à \(\frac{a_{0} f\left(x_{0}\right)+a_{1} f\left(x_{1}\right)}{a_{0}+a_{1}}\). Si \(a_{0}\) et \(a_{1}\) sont non nuls, cela entraîne que \(f\left(x_{0}\right)=f\left(x_{1}\right)=f\left(x_{2}\right)=\cdots=f\left(x_{n}\right)\).

Dans le cas général, on utilise la Proposition 12. Par hypothèse, \(f=\sup _{j \in J} a_{j}\) où chaque fonction \(a_{j}\) est affine. Alors pour tout \(j, \mathbb{E}(f(X)) \geq \mathbb{E}\left(a_{j}(X)\right)=a_{j}(\mathbb{E}(X))\). En prenant le sup sur \(J\), il vient \(f(\mathbb{E}(X)) \leq \mathbb{E}(f(X))\).

Cas d'égalité. Supposons que \(f(\mathbb{E}(X))=\mathbb{E}(f(X))\). On pose \(x=\mathbb{E}(X)\). On decompose \(\Omega\) en \(\Omega_{1}=\{X<x\}\) et \(\Omega_{2}=\{X \geq x\}\). Soit \(t=\mathbb{P}\left(\Omega_{2}\right)\). Supposons que \(t>0\) et \(1-t>0\). On munit \(\Omega_{1}\) de la mesure de probabilité \(\left.\frac{1}{1-t} \mathbb{P} \right\rvert\, \Omega_{1}\) et \(\Omega_{2}\) de la mesure de probabilité \(\frac{1}{t} \mathbb{P}_{\mid \Omega_{2}}\). Soit \(X_{i}\) la restriction de \(X\) à \(\Omega_{i}\). Alors \(X_{1}\) et \(X_{2}\) sont des variables aléatoires (ce sont les conditionnements de \(X\) aux évènements \(\{X<\mathbb{E}(X))\}\) et \(\{X \geq \mathbb{E}(X)\}\) ). On pose \(x_{i}=\mathbb{E}\left(X_{i}\right)\) (on pourrait les noter \(\mathbb{E}(X \mid X<x)\) et \(\mathbb{E}(X \mid X \geq x))\). Comme

\[
x_{1}=\mathbb{E}\left(X_{1}\right)=\frac{1}{1-t} \int_{\Omega_{1}} X d \mathbb{P}, \quad x_{2}=\mathbb{E}\left(X_{2}\right)=\frac{1}{t} \int_{\Omega_{2}} X d \mathbb{P}
\]

il vient \(x=(1-t) x_{1}+t x_{2}\). De même, \(\mathbb{E}(f(X))=(1-t) \mathbb{E}\left(f\left(X_{1}\right)\right)+t \mathbb{E}\left(f\left(X_{2}\right)\right)\). On a montré que \(f\left(x_{i}\right)=f\left(\mathbb{E}\left(X_{i}\right)\right) \leq \mathbb{E}\left(f\left(X_{i}\right)\right)\), d'où

\[
\begin{aligned}
(1-t) f\left(x_{1}\right)+t f\left(x_{2}\right) & \leq(1-t) \mathbb{E}\left(f\left(X_{1}\right)\right)+t \mathbb{E}\left(f\left(X_{2}\right)\right) \\
& =E(f(X))=f(\mathbb{E}(X))=f(x) \\
& =f\left((1-t) x_{1}+t x_{2}\right),
\end{aligned}
\]

ce qui contredit la stricte convexité de \(f\). On conclut que \(t=0\) ou 1, i.e. \(X \leq \mathbb{E}(X)\) ou \(X \geq \mathbb{E}(X)\) presque partout. Cela entraîne que \(X=\mathbb{E}(X)\) presque partout.

\subsection*{2.5 Conséquences de l'inégalité de Jensen}
Théorème 2 (Positivité de l'entropie relative) Soient \(p\) et \(q\) deux distributions de probabilité sur le même ensemble E. Alors \(D(p \| q) \geq 0\) avec égalité si et seulement si \(p=q\).

Preuve Soit \(X\) une variable aléatoire de distribution \(p\). En utilisant l'inégalité de Jensen, il vient

\[
\begin{aligned}
D(p \| q) & =\mathbb{E}\left(\log _{2}\left(\frac{p(X)}{q(X)}\right)\right)=\mathbb{E}\left(-\log _{2}\left(\frac{q(X)}{p(X)}\right)\right) \\
& \geq-\log _{2}\left(\mathbb{E}\left(\frac{q(X)}{p(X)}\right)\right)=-\log _{2}\left(\sum_{x \in E} p(x) \frac{q(x)}{p(x)}\right)=-\log _{2}\left(\sum_{x \in E} q(x)\right) \\
& =0
\end{aligned}
\]

Par stricte concavité du log, l'égalité entraîne que la variable \(\frac{q(X)}{p(X)}\) est constante, donc \(p\) et \(q\) sont proportionnelles, donc elles sont égales (puisque la somme de leurs valeurs vaut 1).

Exemple 14 Pour toute distribution \(p\) sur un ensemble fini \(E, H(p) \leq \log _{2}|E|\) avec égalité si et seulement si \(p\) est la distribution uniforme.

En effet, soit \(u\) la distribution uniforme sur \(E\). Alors

\[
0 \leq D(p \| u)=\log _{2}|E|-H(X)
\]

avec égalité si et seulement si \(p=u\).\\
Corollaire 15 Soient \(X\) et \(Y\) deux variables aléatoires. Alors \(I(X ; Y) \geq 0\), avec égalité si et seulement si \(X\) et \(Y\) sont indépendantes.

Interprétation : l'information fournie par \(Y\) fait diminuer l'incertitude sur \(X\), elle diminue strictement sauf si \(Y\) est independante de \(X\).

Preuve Par définition, \(I(X ; Y)\) est l'entropie relative de la loi du couple et de la loi produit. Elle est donc positive, et nulle seulement si \(p_{(X, Y)}=p_{X} p_{Y}\), i.e. \(X\) et \(Y\) sont indépendantes.

Suggestion d'exercice : \(\mathrm{n}^{0} 3\), feuille 1.

\subsection*{2.6 Entropie conditionnelle}
Rappel 16 Soient \(X\) et \(Y\) deux variables aléatoires, à valeurs dans \(E_{X}\) et \(E_{Y}\) respectivement. Le couple ( \(X, Y\) ) est une variable aléatoire à valeurs dans \(E_{X} \times E_{Y}\), on note \(p_{(X, Y)}\) sa distribution, i.e. pour \(x \in E_{X}\) et \(y \in E_{Y}\),

\[
p_{(X, Y)}(x, y)=\mathbb{P}(X=x \text { et } Y=y)
\]

Les distributions \(p_{X}\) et \(p_{Y}\) s'en déduisent,

\[
p_{X}(x)=\sum_{y \in E_{Y}} p_{(X, Y)}(x, y), \quad p_{Y}(y)=\sum_{x \in E_{X}} p_{(X, Y)}(x, y)
\]

Notation 17 Soient \(X\) et \(Y\) deux variables aléatoires. L'entropie du couple ( \(X, Y\) ) est notée \(H(X, Y)\) plutôt que \(H((X, Y))\). Autrement dit,

\[
H(X, Y)=-\sum_{(x, y) \in E_{X} \times E_{Y}} p_{(X, Y)}(x, y) \log _{2}\left(p_{(X, Y)}(x, y)\right)
\]

Rappel 18 La loi conditionnelle de \(Y\) sachant que \(X=x\) est la distribution de probabilité notée \(p_{Y \mid X=x}\) définie par

\[
p_{Y \mid X=x}(y)=\frac{p_{(X, Y)}(x, y)}{p_{X}(x)} .
\]

On dit que \(X\) et \(Y\) sont indépendantes si \(p_{Y \mid X=x}=p_{Y}\) pour tout \(x\), i.e. si \(p_{(X, Y)}(x, y)=\) \(p_{X}(x) p_{Y}(y)\) pour tout \((x, y)\).

Définition 19 Soient \(X\) et \(Y\) deux variables aléatoires, à valeurs dans \(E_{X}\) et \(E_{Y}\) respectivement. On appelle entropie conditionnelle de \(Y\) sachant \(X\), et on note \(H(Y \mid X)\) le nombre

\[
H(Y \mid X)=\sum_{x \in E_{X}} p_{X}(x) H\left(p_{Y \mid X=x}\right)
\]

Autres expressions :

\[
\begin{aligned}
H(Y \mid X) & =-\sum_{x \in E_{X}} p_{X}(x) \sum_{y \in E_{Y}} p_{Y \mid X=x}(y) \log _{2}\left(p_{Y \mid X=x}(y)\right) \\
& =-\sum_{(x, y) \in E_{X} \times E_{Y}} p(x, y) \log _{2}\left(\frac{p_{(X, Y)}(x, y)}{p_{X}(x)}\right)
\end{aligned}
\]

Exemple 20 Lorsque \(Y=X, H(X \mid X)=0\).\\
En effet, la loi conditionnelle de \(X\) sachant que \(X=x\) est concentrée en \(x\), son entropie est nulle. Informellement, si on connait \(X\), il n'y a plus aucune incertitude concernant \(X\), d'où une entropie conditionnelle nulle.

Proposition 21 Soient \(X\) et \(Y\) deux variables aléatoires. Alors

\[
H(X, Y)=H(X)+H(Y \mid X)
\]

\section*{Preuve}
\[
\begin{aligned}
H(Y \mid X) & =-\sum_{(x, y) \in E_{X} \times E_{Y}} p(x, y) \log _{2}\left(\frac{p_{(X, Y)}(x, y)}{p_{X}(x)}\right) \\
& =-\sum_{(x, y) \in E_{X} \times E_{Y}} p(x, y) \log _{2}\left(p_{(X, Y)}(x, y)\right)+\sum_{(x, y) \in E_{X} \times E_{Y}} p(x, y) \log _{2}\left(p_{X}(x)\right) \\
& =H(X, Y)+\sum_{x \in E_{X}} p_{X}(x) \log _{2}\left(p_{X}(x)\right) \\
& =H(X, Y)-H(X)
\end{aligned}
\]

Suggestion d'exercice : \(\mathrm{n}^{0} 4\), feuille 1 .

Remarque 22 En général, \(H(Y \mid X) \neq H(X \mid Y)\).\\
Car \(H(X, Y)=H(Y, X)\) et \(H(X) \neq H(Y)\) en général.\\
Voici une version conditionnelle de la Proposition 21.

Corollaire 23 Soient \(X, Y\) et \(Z\) trois variables aléatoires. Alors

\[
H(X, Y \mid Z)=H(X \mid Z)+H(Y \mid X, Z)
\]

Preuve

\[
\begin{aligned}
H(X, Y \mid Z) & =H(X, Y, Z)-H(Z) \\
& =H(X, Y, Z)-H(X, Z)+H(X, Z)-H(Z) \\
& =H(Y \mid X, Z)+H(X \mid Z)
\end{aligned}
\]

En utilisant de façon répétée la Proposition 21, on obtient\\
Corollaire 24 Soient ( \(X_{1}, \ldots, X_{n}\) ) des variables aléatoires. Alors

\[
H\left(X_{1}, \ldots, X_{n}\right)=H\left(X_{1}\right)+\sum_{i=2}^{n} H\left(X_{i} \mid X_{i-1}, \ldots, X_{1}\right)
\]

\section*{Preuve}
\[
\begin{aligned}
H\left(X_{1}, X_{2}\right) & =H\left(X_{1}\right)+H\left(X_{2} \mid X_{1}\right) \\
H\left(X_{1}, X_{2}, X_{3}\right) & =H\left(X_{1}, X_{2}\right)+H\left(X_{3} \mid\left(X_{1}, X_{2}\right)\right) \\
& =H\left(X_{1}\right)+H\left(X_{2} \mid X_{1}\right)+H\left(X_{3} \mid X_{1}, X_{2}\right)
\end{aligned}
\]

etc...

\subsection*{2.7 Information mutuelle et entropie conditionnelle}
Proposition 25 Soient \(X\) et \(Y\) deux variables aléatoires. Alors

\[
I(X ; Y)=H(X)-H(X \mid Y)
\]

Interprétation : \(I(X ; Y)\) mesure la diminution de l'incertitude contenue dans \(X\) lorsqu'on apprend \(Y\).

\section*{Preuve}
\[
\begin{aligned}
I(X ; Y) & =\sum_{(x, y) \in E_{X} \times E_{Y}} p_{(X, Y)}(x, y) \log _{2}\left(\frac{p_{(X, Y)}(x, y)}{p_{X}(x) p_{Y}(y)}\right) \\
& =\sum_{(x, y) \in E_{X} \times E_{Y}} p(x, y) \log _{2}\left(\frac{p_{(X, Y)}(x, y)}{p_{Y}(y)}\right)-\sum_{(x, y) \in E_{X} \times E_{Y}} p_{(X, Y)}(x, y) \log _{2}\left(p_{X}(x)\right) \\
& =-H(X \mid Y)-\sum_{x \in E_{X}} p_{X}(x) \log _{2}\left(p_{X}(x)\right) \\
& =-H(X \mid Y)+H(X)
\end{aligned}
\]

Exemple \(26 I(X ; X)=H(X)\)\\
En effet, \(H(X \mid X)=0\). Informellement, en apprenant \(X\), on sait tout sur \(X\), donc l'incertitude passe de \(H(X)\) à 0 , la diminution d'incertitude est \(H(X)\).

Corollaire \(27 I(X ; Y)=H(X)+H(Y)-H(X, Y)\).\\
Preuve Combinaison des propositions 21 et 25.\\
Suggestion d'exercice : \(\mathrm{n}^{0} 4\), feuille 1.

\subsection*{2.8 Conditionner diminue l'entropie}
Proposition 28 Soient \(X\) et \(Y\) deux variables aléatoires. Alors \(H(X \mid Y) \leq H(X)\), avec égalité si et seulement si \(X\) et \(Y\) sont indépendantes.

Preuve \(0 \leq I(X ; Y)=H(X)-H(X \mid Y)\).\\
Interprétation : conditionnellement à \(Y\), l'incertitude sur \(X\) diminue toujours, et diminue strictement sauf si \(Y\) est independante de \(X\).

Attention, c'est seulement vrai en moyenne : pour une valeur particulière \(y, H(X \mid Y=y)\) peut être plus grand que \(H(X)\) (certaines informations supplémentaires peuvent ajouter à la confusion, alors que d'autres renseignent sur \(X\) ). Voir un exemple en Exercice 5, TD1.

Suggestion d'exercice : \(\mathrm{n}^{0} 5\), feuille 1 .\\
Corollaire 29 Soient \(X\) et \(Y\) deux variables aléatoires. Alors \(H(X, Y) \leq H(X)+H(Y)\), avec égalité si et seulement si \(X\) et \(Y\) sont indépendantes.

Preuve \(H(X, Y)=H(X)+H(Y \mid X) \leq H(X)+H(Y)\).

\subsection*{2.9 Information mutuelle conditionnelle}
On va voir que le principe "conditionner diminue l'entropie" a une portée plus générale. Il s'applique aussi à l'entropie conditionnelle (Corollaire 32). Pour le montrer, on a recours à une notion supplémentaire, celle d'information mutuelle conditionnelle.

Définition 30 Soient \(X, Y\) et \(Z\) des variables aléatoires. \(L\) information mutuelle conditionnelle de \(X\) et \(Y\) sachant \(Z\) est la moyenne, pondérée par la loi \(p_{Z}\) de \(Z\), des informations mutuelles des variables conditionnées \(X \mid Z=z\) et \(Y \mid Z=z\),

\[
I(X ; Y \mid Z)=\sum_{z \in E_{Z}} p_{Z}(z) I(X|Z=z ; Y| Z=z)
\]

Autre expression :

\[
I(X ; Y \mid Z)=\sum_{x, y, z} p_{(X, Y, Z)}(x, y, z) \log _{2} \frac{\frac{p_{(X, Y, Z)}(x, y, z)}{p_{Z}(z)}}{\frac{p_{(X, Z)}(x, z)}{p_{Z}(z)} \frac{p_{(Y, Z)}(y, z)}{p_{Z}(z)}}
\]

Comme c'est une moyenne d'informations mutuelles, \(I(X ; Y \mid Z) \geq 0\). Comme dans l'information mutuelle, \(X\) et \(Y\) jouent des rôles symétriques : \(I(X ; Y \mid Z)=I(Y ; X \mid Z)\).

Il y a une version conditionnelle de la formule \(I(X ; Y)=H(X)-H(X \mid Y)\) (Proposition 25) :\\
Proposition 31 Soient \(X, Y\) et \(Z\) des variables aléatoires. Alors

\[
I(X ; Y \mid Z)=H(X \mid Z)-H(X \mid Y, Z)
\]

Preuve Pour tout \(z \in E_{Z}, I(X|Z=z ; Y| Z=z)=H(X \mid Z=z)-H((X \mid Z=z) \mid(Y \mid Z=z))\), d'où

\[
\begin{aligned}
I(X ; Y \mid Z) & =\sum_{z \in E_{Z}} p_{Z}(z) I(X|Z=z ; Y| Z=z) \\
& =\sum_{z \in E_{Z}} p_{Z}(z) H(X \mid Z=z)-\sum_{z \in E_{Z}} p_{Z}(z) H((X \mid Z=z) \mid(Y \mid Z=z)) \\
& =H(X \mid Z)-\sum_{(y, z) \in E_{Y} \times E_{Z}} p_{Z}(z) p_{Y \mid Z=z}(y) H((X \mid Z=z) \mid(Y \mid Z=z)=y) \\
& =H(X \mid Z)-\sum_{(y, z) \in E_{Y} \times E_{Z}} p_{(Y, Z)}(y, z) H(X \mid Y=y, Z=z) \\
& =H(X \mid Z)-H(X \mid Y, Z)
\end{aligned}
\]

Pour calculer \(H((X \mid Z=z) \mid(Y \mid Z=z)=y)\), on a calculé la loi conditionnelle de \(X \mid Y=y\) sous la probabilité conditionnelle sachant \(Z=z\), notée \(\mathbb{P}_{z}\),

\[
\begin{aligned}
\mathbb{P}_{(X \mid Z=z) \mid(Y \mid Z=z)=y}(x) & =\left(\mathbb{P}_{z}\right)_{X \mid Y=y}(x) \\
& =\frac{\mathbb{P}_{z}(X=x, Y=y)}{\mathbb{P}_{z}(Y)} \\
& =\frac{\mathbb{P}(X=x, Y=y \mid Z=z)}{\mathbb{P}(Y \mid Z=z)} \\
& =\frac{\mathbb{P}_{(X, Y, Z)}(x, y, z)}{\mathbb{P}_{(Y, Z)}(y, z)} \\
& =\mathbb{P}_{X \mid(Y=y, Z=z)}(x)
\end{aligned}
\]

Corollaire 32 Soient \(X, Y\) et \(Z\) des variables aléatoires. Alors \(H(X \mid Y, Z) \leq H(X \mid Y)\).\\
De même, avec la Proposition 23,\\
Corollaire 33 Soient \(X, Y\) et \(Z\) des variables aléatoires. Alors \(H(X, Y \mid Z) \leq H(X \mid Z)+H(Y \mid Z)\).\\
(Cela peut aussi se montrer directement : pour tout \(z, H(X, Y \mid Z=z) \leq H(X \mid Z=z)+H(Y \mid Z=\) \(z\) ), donc c'est vrai en moyenne).

Enfin, l'information mutuelle satisfait une règle d'addition analogue à la Proposition 21 et au Corollaire 24.

Proposition 34 Soient \(X, Y, Z\) des variables aléatoires. Alors

\[
I(X, Y ; Z)=I(X ; Z)+I(Y ; Z \mid X), \quad I(X ; Y, Z)=I(X ; Z)+I(X ; Y \mid Z)
\]

Plus généralement, soient \(X_{1}, \ldots, X_{n}\) et \(Z\) des variables aléatoires. Alors

\[
I\left(X_{1}, \ldots, X_{n} ; Z\right)=I\left(X_{1} ; Z\right)+\sum_{i=2}^{n} I\left(X_{i} ; Z \mid X_{i-1}, \ldots, X_{1}\right)
\]

Preuve On exprime l'information mutuelle en fonction d'entropies conditionnelles, on applique la Proposition 21 et on conclut avec la Proposition 31.

\[
\begin{aligned}
I(X, Y ; Z) & =H(X, Y)-H(X, Y \mid Z) \\
& =H(X)+H(Y \mid X)-(H(X \mid Z)+H(Y \mid X, Z)) \\
& =I(X ; Z)+I(Y ; Z \mid X)
\end{aligned}
\]

La seconde formule s'obtient en échangeant \(X\) et \(Z\) et en observant que \(I(Y ; Z \mid X)=I(Z ; Y \mid X)\).\\
Quand il y a davantage de variables, le Corollaire 24 remplace la Proposition 21.

\[
\begin{aligned}
I\left(X_{1}, \ldots, X_{n} ; Z\right)= & H\left(X_{1}, \ldots, X_{n}\right)-H\left(X_{1}, \ldots, X_{n} \mid Z\right) \\
= & H\left(X_{1}\right)+\sum_{i=2}^{n} H\left(X_{i} \mid X_{i-1}, \ldots, X_{1}\right) \\
& -\left(H\left(X_{1} \mid Z\right)+\sum_{i=2}^{n} H\left(X_{i} \mid X_{i-1}, \ldots, X_{1}, Z\right)\right) \\
= & I\left(X_{1} \mid Z\right)+\sum_{i=2}^{n} I\left(X_{i} ; Z \mid X_{i-1}, \ldots, X_{1}\right)
\end{aligned}
\]

Suggestion d'exercice : \(\mathrm{n}^{0} 6\), feuille 1 .\\
Suggestion d'exercice : \(\mathrm{n}^{0} 7\), feuille 1 .

\subsection*{2.10 Entropie relative conditionnelle}
Définition 35 Soient \(E_{X}, E_{Y}\) des ensembles finis, soient \(p\) et \(q\) des distributions de probabilité sur \(E_{X} \times E_{Y}\). L'entropie relative conditionnelle \(D\left(p_{Y \mid X} \| q_{Y \mid X}\right)\) est la moyenne selon la marginale \(p_{X}\) des entropies relatives \(D\left(p_{Y \mid X=x} \| q_{Y \mid X=x}\right)\), i.e.

\[
D\left(p_{Y \mid X} \| q_{Y \mid X}\right)=\sum_{x \in E_{X}} p_{X}(x) D\left(p_{Y \mid X=x} \| q_{Y \mid X=x}\right)
\]

Remarquer que \(D\left(p_{Y \mid X} \| q_{Y \mid X}\right)\) est une moyenne de quantités positives ou nulles, donc \(D\left(p_{Y \mid X} \| q_{Y \mid X}\right) \geq\) 0 . Autres expressions :

\[
\begin{aligned}
D\left(p_{Y \mid X} \| q_{Y \mid X}\right) & =\sum_{x \in E_{X}} p_{X}(x) \sum_{y \in E_{Y}} p_{Y \mid X=x}(y) \log \frac{p_{Y \mid X=x}(y)}{q_{Y \mid X=x}(y)} \\
& =\sum_{(x, y) \in E_{X} \times E_{Y}} p(x, y) \log \frac{\frac{p(x, y)}{\sum_{z} p(x, z)}}{\frac{q(x, y)}{\sum_{z} q(x, z)}}
\end{aligned}
\]

Proposition 36 Soient \(E_{X}, E_{Y}\) des ensembles finis, soient \(p\) et \(q\) des distributions de probabilité sur \(E_{X} \times E_{Y}\). On peut exprimer l'entropie relative \(p\) et \(q\) comme suit.

\[
D(p \| q)=D\left(p_{X} \| q_{X}\right)+D\left(p_{Y \mid X} \| q_{Y \mid X}\right)
\]

\section*{Preuve}
\[
\begin{aligned}
D\left(p_{Y \mid X} \| q_{Y \mid X}\right) & =\sum_{(x, y) \in E_{X} \times E_{Y}} p(x, y) \log \frac{p(x, y)}{q(x, y)}+\sum_{(x, y) \in E_{X} \times E_{Y}} p(x, y) \log \frac{\sum_{z} q(x, z)}{\sum_{z} p(x, z)} \\
& =D(p \| q)-D\left(p_{X} \| q_{X}\right)
\end{aligned}
\]

Remarque 37 En particulier, \(D(p \| q) \geq D\left(p_{Y \mid X} \| q_{Y \mid X}\right)\), encore une manifestation du principe selon lequel conditionner diminue l'entropie.

On utilisera l'entropie relative conditionnelle dans la preuve du Théorème 9.

\subsection*{2.11 Convexité de l'entropie relative}
Théorème 3 Soit \(E\) un ensemble fini. L'entropie relative est une fonction convexe sur l'ensemble \(\mathcal{P}(E) \times \mathcal{P}(E)\), où \(\mathcal{P}(E)\) est l'ensemble des distributions de probabilité sur \(E\).\\
Remarquer que \(\mathcal{P}(E)\) est un polyèdre convexe de \(\mathbb{R}^{|E|}\), et \(\mathcal{P}(E) \times \mathcal{P}(E)\) un polyèdre convexe de \(\mathbb{R}^{2|E|}\). On peut donc parler de convexité d'une fonction définie sur \(\mathcal{P}(E) \times \mathcal{P}(E)\).

Fin du cours \(\mathrm{n}^{0} 2\)

Preuve Soient \(p_{1}, p_{2}, q_{1}\) et \(q_{2}\) des distributions de probabilité sur \(E\). Soit \(\left.t \in\right] 0,1[\). Soit \(x \in E\). On écrit\\
\(\frac{(1-t) p_{1}(x)+t p_{2}(x)}{(1-t) q_{1}(x)+t q_{2}(x)}=(1-\lambda) a+\lambda b, \quad\) où \(a=\frac{p_{1}(x)}{q_{1}(x)}, b=\frac{p_{2}(x)}{q_{2}(x)}, \lambda=\frac{t q_{2}(x)}{(1-t) q_{1}(x)+t q_{2}(x)}\).\\
Comme \(x \mapsto f(x)=x \log _{2} x\) est convexe,

\[
\begin{aligned}
f\left(\frac{(1-t) p_{1}(x)+t p_{2}(x)}{(1-t) q_{1}(x)+t q_{2}(x)}\right) & =f((1-\lambda) a+\lambda b) \\
& \leq(1-\lambda) f(a)+\lambda f(b) \\
& =\frac{(1-t) q_{1}(x)}{(1-t) q_{1}(x)+t q_{2}(x)} f\left(\frac{p_{1}(x)}{q_{1}(x)}\right)+\frac{t q_{2}(x)}{(1-t) q_{1}(x)+t q_{2}(x)} f\left(\frac{p_{2}(x)}{q_{2}(x)}\right) \\
& =\frac{(1-t) p_{1}(x)}{(1-t) q_{1}(x)+t q_{2}(x)} \log _{2}\left(\frac{p_{1}(x)}{q_{1}(x)}\right)+\frac{t p_{2}(x)}{(1-t) q_{1}(x)+t q_{2}(x)} \log _{2}\left(\frac{p_{2}(x)}{q_{2}(x)}\right)
\end{aligned}
\]

d'où\\
\(\left((1-t) p_{1}(x)+t p_{2}(x)\right) \log _{2}\left(\frac{(1-t) p_{1}(x)+t p_{2}(x)}{(1-t) q_{1}(x)+t q_{2}(x)}\right) \leq(1-t) p_{1}(x) \log _{2}\left(\frac{p_{1}(x)}{q_{1}(x)}\right)+t p_{2}(x) \log _{2}\left(\frac{p_{2}(x)}{q_{2}(x)}\right)\).\\
En sommant sur \(x \in E\), il vient

\[
D\left((1-t) p_{1}+t p_{2} \|(1-t) q_{1}+t p q_{2}\right) \leq(1-t) D\left(p_{1} \| q_{1}\right)+t D\left(p_{2} \| q_{2}\right)
\]

Corollaire 38 L'entropie est une fonction concave sur l'ensemble des distributions de probabilité sur \(E\). Elle atteint son maximum à la distribution uniforme.

Preuve Soit \(u: x \mapsto \frac{1}{|E|}\) la distribution uniforme sur \(E\). Alors pour toute distribution de probabilité \(p\),

\[
D(p \| u)=\sum_{x \in E} p(x) \log _{2}\left(\frac{p(x)}{u(x)}\right)=\log _{2}(|E|)-H(p)
\]

est une fonction convexe de \(p\), positive ou nulle, et nulle exactement en \(u\).\\
Exemple 39 L'entropie de la distribution de Bernoulli \(q \mapsto H(\mathcal{B}(q))\) est une fonction concave sur \([0,1]\), elle atteint son maximum en \(\frac{1}{2}\).

Suggestion d'exercice : \(\mathrm{n}^{0} 8\), feuille 1.

\subsection*{2.12 A retenir}
\begin{itemize}
  \item Les notions absolues : entropie, entropie relative, information mutuelle.
  \item Leurs versions conditionnelles : entropie conditionnelle, entropie relative conditionnelle, information mutuelle conditionnelle. Les formules d'addition correspondantes.
  \item Le lien entre information mutuelle et entropie conditionnelle (et sa version conditionnelle).
  \item Les inégalités : toutes les entropies/informations sont positives ou nulles, conditionner diminue l'entropie, concavité de l'entropie (resp. convexité de l'entropie relative) comme fonction de la distribution (resp. de deux distributions).\\
Il s'agit d'un chapitre théorique, avec plein de définitions et d'énoncés qui seront utilisés ensuite.
\end{itemize}

\section*{3 Compression de données}
\subsection*{3.1 Motivation}
Il est notoire que l'anglais est plus concis que le français : la traduction anglaise d'un texte français est presque toujours nettement plus courte que le texte original. Jusqu'où peut on aller dans cette direction? Peut on fabriquer une langue artificielle qui soit encore plus économe?

Ignorons la grammaire pour ne travailler que sur le vocabulaire. La langue artificielle - appelons la le codage - est définie par un dictionnaire : à chaque mot français \(x\) correspond son équivalent \(C(x)\), une chaîne de caractères pris dans l'alphabet latin \(\mathcal{D}\), qui possède \(D=26\) lettres. On note \(\ell(x)\) la longueur de \(C(x)\). Etant donné un texte français \(T\), i.e. une suite de mots, la longueur totale de sa traduction est \(\sum_{x \in E} N(x, T) \ell(x)\) où \(E\) est l'ensemble des mots français et \(N(x, T)\) est le nombre d'apparitions du mot \(x\) dans le texte \(T\). La performance du codage est

\[
\sum_{x \in E} \frac{N(x, T)}{N(T)} \ell(x)=\sum_{x \in E} p_{T}(x) \ell(x),
\]

où \(N(T)\) est le nombre total de mots dans \(T\), et \(p_{T}(x)\) la fréquence d'apparition du mot \(x\) dans \(T\). Pour de grands textes, l'expérience montre que les fréquences \(p_{T}\) convergent vers une distribution de probabilité sur \(E\), la fréquence d'utilisation de chaque mot en français.

\subsection*{3.2 Modélisation}
La langue française est donc modélisée par une variable aléatoire \(X\) à valeurs dans un ensemble fini \(E\), le codage par une application \(C: E \rightarrow \mathcal{D}^{*}\) (ensemble des suites finies de lettres prises dans \(\mathcal{D})\), et la performance du codage par l'espérance \(\mathbb{E}(\ell(X))\).

Le codage des textes est l'application \(C^{*}: E^{*} \rightarrow \mathcal{D}^{*}\) qui envoie le texte (i.e. la suite de mots) \(\left(x_{1}, x_{2}, \ldots, x_{k}\right)\) sur sa traduction \(C\left(x_{1}\right) C\left(x_{2}\right) \cdots C\left(x_{k}\right)\). Par souci d'économie, les mots codés sont concaténés sans séparateurs (pas d'espace ni de ponctuation).

Définition 40 Un codage \(C\) est uniquement décodable si l'application \(C^{*}: E^{*} \rightarrow \mathcal{D}^{*}\) est injective.\\
C'est la moindre des choses.\\
Question 41 (Problème du codage de source) Quelle est la meilleure performance \(\mathbb{E}(\ell(X))\) accomplie par les codages uniquement décodables?

Ce problème s'intitule problème du codage de source, par opposition au codage des transmissions, qu'on étudiera plus loin. On voit la variable aléatoire \(X\) comme une source de mots. Dans notre exemple initial, la source, c'est la littérature française.

Suggestion d'exercice : \(\mathrm{n}^{0} 1\), feuille 2 .

\subsection*{3.3 Inégalité de Kraft}
Dans un codage uniquement décodable, les mots codés ne peuvent pas tous être courts. Le théorème suivant donne une borne inférieure optimale sur les longueurs.

Théorème 4 (L. Kraft (1949), B. McMillan (1953)) Soit \(C: E \rightarrow \mathcal{D}^{*}\) un codage uniquement décodable dans un alphabet à \(D=|\mathcal{D}|\) lettres. Alors

\[
\sum_{x \in E} D^{-\ell(x)} \leq 1
\]

Inversement, toute fonction \(\ell: E \rightarrow \mathbb{N} \backslash\{0\}\) qui satisfait l'inégalité précédente est la fonction longueur d'un codage uniquement décodable.

Preuve Soit \(k \geq 1\) un entier. On développe

\[
\begin{aligned}
\left(\sum_{x \in E} D^{-\ell(x)}\right)^{k} & =\sum_{\left(x_{1}, \ldots, x_{k}\right) \in E^{k}} D^{-\sum_{i} \ell\left(x_{i}\right)} \\
& =\sum_{T \in E^{k}} D^{-\left|C^{*}(T)\right|}
\end{aligned}
\]

Ici \(E^{k}\) désigne l'ensemble des textes à \(k\) mots et \(\left|C^{*}(T)\right|\) est la longueur de la traduction de \(T\). Soit

\[
L=\max _{x \in E} \ell(x)
\]

la longueur maximale d'un mot codé. On note \(N(m)\) le nombre de textes à \(k\) mots codés par des suites de \(m\) lettres. Alors

\[
\sum_{T \in E^{k}} D^{-\left|C^{*}(T)\right|}=\sum_{m=1}^{k L} N(m) D^{-m}
\]

Comme \(C^{*}\) est injective, et comme il y a seulement \(D^{m}\) suites de \(m\) lettres distinctes, \(N(m) \leq D^{m}\). Il vient

\[
\left(\sum_{x \in E} D^{-\ell(x)}\right)^{k} \leq \sum_{m=1}^{k L} D^{m} D^{-m}=k L
\]

d'où \(\sum_{x \in E} D^{-\ell(x)} \leq(k L)^{1 / k}\) pour tout \(k \geq 1\). En faisant tendre \(k\) vers \(+\infty\), on trouve que \(\sum_{x \in E} D^{-\ell(x)} \leq 1\).

Réciproquement, soit \(\ell: E \rightarrow \mathbb{N} \backslash\{0\}\) une fonction qui satisfait l'inégalité \(\sum_{x \in E} D^{-\ell(x)} \leq 1\). On ordonne les éléments de \(E=\left\{x_{1}, x_{2}, \ldots\right\}\) de sorte que \(\ell\) soit croissante. On ordonne \(\mathcal{D}\) aussi. L'ensemble \(\mathcal{A}_{0}=\mathcal{D}^{*}\) est ordonné par l'ordre lexicographique. On construit par récurrence des éléments \(C\left(x_{1}\right), C\left(x_{2}\right), \ldots\) et des sous-ensembles décroissants \(\mathcal{A}_{0} \supset \mathcal{A}_{1} \supset \mathcal{A}_{2} \supset \cdots\) comme suit. Soit \(C\left(x_{1}\right)\) le premier élément de \(\mathcal{D}^{*}\) (dans l'ordre lexicographique) de longueur \(\ell\left(x_{1}\right)\). Soit \(\mathcal{A}_{1}=\mathcal{A}_{0}\) privé de toutes les suites qui commencent par \(C\left(x_{1}\right)\). Soit \(C\left(x_{2}\right)\) le premier élément de \(\mathcal{A}_{1}\) (dans l'ordre lexicographique) de longueur \(\ell\left(x_{2}\right)\). Soit \(\mathcal{A}_{2}=\mathcal{A}_{1}\) privé de toutes les suites qui commencent par \(C\left(x_{2}\right)\), etc.... La condition \(\sum_{i} D^{-\ell\left(x_{i}\right)} \leq 1\) garantit qu'à chaque étape, \(\mathcal{A}_{i}\) possède bien des éléments de longueur \(\ell\left(x_{i}\right)\). En effet, soit de nouveau \(L=\max _{x \in E} \ell(x)\). Soit \(N(j)\) le nombre de suites de longueur \(L\) qui n'appartiennent pas à \(\mathcal{\mathcal { A } _ { j }}\). Par construction, \(N(0)=0\). En passant de \(\mathcal{A}_{j-1}\) à \(\mathcal{A}_{j}\), on supprime toutes les suites de longueur \(L\) commençant par \(C\left(x_{j}\right)\), il y en a exactement \(D^{L-\ell\left(x_{j}\right)}\). Par conséquent,

\[
N(j)=N(j-1)+D^{L-\ell\left(x_{i}\right)}, \quad \text { d'où } \quad N(j)=\sum_{i=1}^{j} D^{L-\ell\left(x_{i}\right)} \leq D^{L},
\]

avec inégalité stricte tant que \(E\) n'est pas épuisé. Il y a donc des éléments de longueur \(L\) (et donc, de toute longueur \(\leq L)\) dans \(\mathcal{A}_{j-1}\) jusqu'à la dernière étape de la construction.

Il est commode de visualiser chaque ensemble ordonné \(\mathcal{A}_{j}\) sous la forme d'un arbre enraciné, sous-arbre de l'arbre associé à \(\mathcal{D}^{*}\). Ci-dessous, l'arbre final lorsque \(D=2, E=\left\{x_{1}, x_{2}, x_{3}\right\}\), \(\ell\left(x_{1}\right)=\ell\left(x_{2}\right)=2, \ell\left(x_{3}\right)=3\) (les flèches désignent les branches de l'arbre qui se poursuivent sans changement). Le codage est \(C\left(x_{1}\right)=00, C\left(x_{2}\right)=01, C\left(x_{3}\right)=011\).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-15}

Arbre schématisant la construction du codage\\
Par construction, aucun des \(C(x), x \in E\), n'est un préfixe d'un autre \(C\left(x^{\prime}\right)\). Par conséquent, une concaténation \(w=C\left(x_{i_{1}}\right) C\left(x_{i_{2}}\right) \cdots C\left(x_{i_{k}}\right)\) se décode aisément comme suit. On lit les lettres dans l'ordre jusqu'à ce qu'on reconnaisse un élément de \(C(E)\). Il y a exactement un élément \(x\) de \(E\) tel que \(C(x)\) commence par les mêmes lettres que \(w\), c'est \(x_{i_{1}}\), donc \(w\) est la traduction d'un mot commençant par \(x_{i_{1}}\). On recommence avec \(w_{1}=C\left(x_{i_{2}}\right) \cdots C\left(x_{i_{k}}\right)\), etc... Cela prouve que \(C^{*}: E^{*} \rightarrow \mathcal{D}^{*}\) est injective.

\subsection*{3.4 Théorème du codage de source}
L'inégalité de Kraft ramène la question de minimiser la longueur moyenne d'un codage uniquement décodable à une simple inégalité sur les fonctions sur \(E\). La réponse fait intervenir l'entropie.

Théorème 5 (C. Shannon (1948)) Soit \(X\) une variable aléatoire à valeurs dans un ensemble fini \(E\). Soit \(C: E \rightarrow \mathcal{D}^{*}\) un codage uniquement décodable dans un alphabet à \(D=|\mathcal{D}|\) lettres. Soit \(\ell: E \rightarrow \mathbb{N}\) la longueur des mots-codes. Alors la longueur moyenne satisfait

\[
\mathbb{E}(\ell(X)) \geq \frac{H(X)}{\log _{2}(D)}
\]

Cette borne est atteinte si et seulement si les valeurs \(p(x), x \in E\), de la loi de \(X\) sont des puissances de D.

En général, il existe un code \(C\) qui réalise

\[
\frac{H(X)}{\log _{2}(D)} \leq \mathbb{E}(\ell(X))<\frac{H(X)}{\log _{2}(D)}+1
\]

Autrement dit, si \(L_{u d}(X)\) désigne la borne inférieure des longueurs moyennes des codages uniquement décodables, alors

\[
\frac{H(X)}{\log _{2}(D)} \leq L_{u d}(X)<\frac{H(X)}{\log _{2}(D)}+1
\]

Preuve Soit \(c=\sum_{x \in E} D^{-\ell(x)}\). On définit une distribution de probabilité \(q\) sur \(E\) par \(q(x)=\) \(\frac{1}{c} D^{-\ell(x)}\). On calcule

\[
\begin{aligned}
D(p \| q) & =\sum_{x \in E} p(x) \log _{2}\left(\frac{p(x)}{q(x)}\right) \\
& =-H(X)+\log _{2}(c)+\log _{2}(D) \mathbb{E}(\ell(X))
\end{aligned}
\]

Comme \(D(p \| q) \geq 0\) (Théorème 2) et \(c \leq 1\) (Théorème 4), il vient \(-H(X)+\log _{2}(D) \mathbb{E}(\ell(X)) \geq 0\). L'égalité entraîne que \(D(p \| q)=0\) et \(c=1\), et donc que \(p=q\) est à valeurs dans les puissances de D.

Réciproquement, si \(p\) est à valeurs dans les puissances de \(D\), on pose \(\ell=-\log _{2}(p) / \log _{2}(D)\). C'est une fonction à valeurs entières qui satisfait l'inégalité de Kraft. D'après le Théorème 4, il existe un codage \(C\) uniquement décodable dont les longueurs sont données par \(\ell\), il réalise la performance \(\mathbb{E}(\ell(X))=\frac{H(X)}{\log _{2}(D)}\).

En général, on pose \(\ell=\left\lceil-\log _{2}(p) / \log _{2}(D)\right\rceil\), fonction à valeurs entières qui satisfait l'inégalité de Kraft, donc réalisable par un codage uniquement décodable. On calcule

\[
\begin{aligned}
\mathbb{E}(\ell(X)) & =\sum_{x \in E} p(x)\left\lceil-\frac{\log _{2}(p)}{\log _{2}(D)}\right\rceil \\
& <\sum_{x \in E} p(x)\left(-\frac{\log _{2}(p)}{\log _{2}(D)}+1\right) \\
& =\frac{H(X)}{\log _{2}(D)}+1
\end{aligned}
\]

Cela confirme l'idée que l'entropie mesure la quantité d'information nécessaire pour décrire une variable aléatoire \(X\) : c'est la borne inférieure du nombre de bits nécessaires en moyenne pour coder \(X\).

Remarque 42 Supposons qu'on est mal informé sur la loi de \(X\). On n'en connait qu'une approximation \(q\). Le codage qui semble presque optimal a pour longueurs de mots \(\ell(x)=\left\lceil-\frac{\log _{2}(q)}{\log _{2}(D)}\right\rceil\), donc pour longueur moyenne

\[
\frac{H(X)+D(p \| q)}{\log _{2}(D)} \leq \mathbb{E}(\ell(X))<\frac{H(X)+D(p \| q)}{\log _{2}(D)}+1
\]

Autrement dit, l'entropie relative \(D(p \| q)\) mesure l'augmentation de la complexité de \(X\) due au fait qu'on a une information erronnée sur sa loi.

Remarque 43 Un code non uniquement décodable ne satisfait pas nécessairement \(\mathbb{E}(\ell(X)) \geq\) \(H(X)\).

Suggestion d'exercice : \(\mathrm{n}^{0} 2\), feuille 2.\\
Suggestion d'exercice : \(\mathrm{n}^{0} 3\), feuille 2.

\subsection*{3.5 Construction de codes optimaux}
Le Théorème 5 ne fournit un codage optimal que dans le cas où la distribution de probabilité donnée est à valeurs dans les puissances de la taille \(D\) de l'alphabet. Il existe un algorithme, dû à D. Huffman, qui produit pour toute distribution de probabilité un codage optimal, voir le livre de Cover et Thomas, chapitre 5, pages 118 à 127. On le décrit ici uniquement pour \(D=2\).

\subsection*{3.5.1 Jeu dans la cour d'un collège}
On commence par relier ce cas particulier du problème du codage optimal au jeu des 10 questions : "Choisis un nombre à trois chiffres, mémorise-le sans le dire. Je vais te poser une série de questions auxquelles tu répondras par oui ou par non. En moins de 10 questions, je peux deviner de quel nombre il s'agit!"

Ce qui nous intéresse, ce n'est pas de déterminer le nombre de questions nécessaire dans le pire des cas, mais d'optimiser le nombre de questions en moyenne, lorsque le nombre est tiré suivant une distribution de probabilité connue.

Autrement dit, on se donne une variable aléatoire \(X\) à valeurs dans un ensemble fini \(E\). Une stratégie est un procédé déterministe qui, à une suite de réponses \(s \in\{0,1\}^{*}\) associe une fonction \(f_{s}: X \rightarrow\{0,1\}\). Si \(s\) est de longueur \(i-1, f_{s}\) représente la question que je pose à l'étape \(i\) au vu de la suite de réponses \(s\). Lorsque \(X=x\), le déroulement du jeu produit des suites de réponses \(s_{1}=f_{\emptyset}(x), s_{2}=s_{1} f_{s_{1}}(x), \ldots, s_{i}=s_{i-1} f_{s_{i-1}, \ldots, s_{Q},} Q=Q(x)\). Le jeu s'arrête lorsque la suite de réponses \(s_{Q}(x)\) détermine uniquement \(x\), i.e. lorsque pour tout \(y \in E, s_{Q}(y)=(x) \Rightarrow y=x\). Autrement dit, \(Q(x)\) est la durée du jeu. Il s'agit de trouver la stratégie qui minimise la durée moyenne du jeu, i.e. l'espérance de \(Q(X)\).

Question 44 Quel est le nombre minimum de questions nécessaire, en moyenne, pour déterminer \(X\) ?

On va utiliser une représentation graphique commode, sous forme d'arbre binaire. Il y a une terminologie pour ces arbres.

Définition 45 Un arbre binaire est un arbre muni d'un sommet particulier, la racine. Les arêtes sont orientées dans la direction opposée à la racine. Chaque sommet autre que la racine reçoit une arête, qui provient de son ascendant. Chaque sommet a ou bien 2 descendants (on parle de noeud), ou bien 0 (on parle de feuille). La profondeur d'un sommet est le nombre d'arêtes qui le séparent de la racine, on note cette fonction \(\ell\) (ou \(\ell_{A}\) quand il faut spécifier qu'il s'agit de l'arbre \(A\) ). On note \(\partial A\) l'ensemble des feuilles d'un arbre binaire \(A\).

Proposition 46 Etant donnée une distribution de probabilité \(p\) sur un ensemble fini \(E\), les trois problèmes suivants sont équivalents.

\begin{enumerate}
  \item Construire une stratégie au jeu des questions qui minimise la durée moyenne du jeu.
  \item Construire un arbre binaire \(A\) et une bijection de \(E\) sur l'ensemble des feuilles de \(A\), qui minimise la profondeur moyenne des feuilles.
  \item Construire un codage de longueur moyenne minimale parmi ceux qui ont la propriété suivante : aucun mot-code n'est un préfixe d'un autre mot-code.
\end{enumerate}

Preuve La suite des réponses aux questions jusqu'à terminaison associe une suite \(C(x) \in\{0,1\}^{*}\) à chaque élément de \(E\). L'application \(C\) est un codage sans préfixe. En effet, supposons qu'il existe \(x \neq y \in E\) tels que \(C(x)\) est un préfixe de \(C(y)\). Les tirages \(x\) et \(y\) donnent la même suite de réponses de longueur \(Q(x)\). Si le jeu s'arrête là dans le cas du tirage \(x\), c'est que cette suite détermine uniquement \(x\). En particulier, \(y=x\), contradiction. La fonction longueur \(\ell\) du codage \(C\) coïncide avec \(Q\), donc la durée moyenne du jeu est égale à la longueur moyenne du codage.

On peut représenter graphiquement \(C\) par un arbre binaire : la racine a deux descendants numérotés 0 et 1 , qui ont chacun deux descendants numérotés respectivement 00 et 01,10 et 11, etc... On arrête la construction récursive de l'arbre dès que la suite de questions s'arrête, i.e. quand elle caractérise uniquement un élément de \(E\). Les feuilles correspondent bijectivement aux éléments \(x\) de \(E\), elles héritent d'un poids \(p(x)\). La profondeur de la feuille associée à \(x\) est égale à la longueur \(\ell(x)\) du mot-code \(C(x)\), la profondeur moyenne est égale à la longueur moyenne du codage. Si un noeud ne possède qu'un seul descendant, on peut le supprimer en contractant l'unique arête qui en part. Cela retire une lettre aux mots-codes dont il était un préfixe, donc cela diminue strictement la longueur moyenne. Cela ne peut pas se produire pour un codage minimal. Les codages minimaux correspondent donc à des arbres binaires.

Enfin, un arbre binaire fournit une stratégie pour le jeu des 10 questions. Une suite \(s \in\{0,1\}^{*}\) amène à un noeud \(n(s)\) de l'arbre. De ce noeud émanent deux sous-arbres \(A_{0}\) et \(A_{1}\). Soit \(f_{s}\) la fonction qui vaut 1 sur les feuilles de \(A_{1}\), et 0 sur toutes les autres feuilles. La réponse à cette question permet de de faire un pas de plus, dans \(A_{0}\) ou \(A_{1}\) suivant que la réponse est 0 ou 1 .\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-18}

Stratégie associée à un arbre : sous arbres lorsque \(s=1\)\\
Muni de cette stratégie, le jeu s'arrête lorsqu'on atteint une feuille, marquée par un élément \(x\) de \(E\). Il permet d'identifier uniquement \(x\). La durée du jeu en cas de tirage \(x\) est égale à la profondeur de \(x\), donc profondeur moyenne et durée moyenne du jeu sont égales.

\subsection*{3.5.2 Algorithme de D. Huffman}
En entrée : un ensemble fini ordonné \(E\) muni d'une distribution de probabilité \(p\). En sortie : un arbre binaire \(A\) et une bijection de \(E\) sur \(\partial A\).

On construit une suite d'ensembles ordonnés \(E=E_{0}, \ldots, E_{|E|-1}\) et de distributions de probabilité \(p=p_{0}, \ldots, p_{|E|-1}\). L'algorithme est récursif. On répète l'opération suivante, qui fait diminuer \(|E|\) d'une unité, jusqu'à ce que \(E\) n'ait plus qu'un élément.

Soient \(x\) et \(x^{\prime}\) les éléments de \(E\) dont les probabilités sont les plus basses (s'il y a plusieurs choix, on choisit les derniers dans l'ordre fixé sur \(E\) ). On les fusionne, i.e. on leur donne un ascendant commun \(y\) auquel on affecte la probabilité \(p^{\prime}(y)=p(x)+p\left(x^{\prime}\right)\). On pose \(E^{\prime}=E /\left(x \sim x^{\prime}\right)=\) \(\left(E \backslash\left\{x, x^{\prime}\right\}\right) \cup\{y\}\).

Exemple. Soit \(E=\{1,2,3,4,5\}\) muni de la distribution \(\left(\frac{1}{4}, \frac{1}{4}, \frac{1}{5}, \frac{3}{20}, \frac{3}{20}\right)\).

\begin{itemize}
  \item Au premier tour, on fusionne 4 et 5 en un noeud baptisé 6 , de probabilité \(\frac{3}{20}+\frac{3}{20}=\frac{3}{10}\), d'où \(E_{1}=\{1,2,3,6\}\).
  \item Au second tour, on fusionne 2 et 3 en un noeud baptisé 7 , de probabilité \(\frac{1}{5}+\frac{1}{4}=\frac{9}{20}\), d'où \(E_{2}=\{1,6,7\}\).
  \item Au troisième tour, on fusionne 1 et 6 en un noeud baptisé 8 , de probabilité \(\frac{1}{4}+\frac{3}{10}=\frac{11}{20}\), d'où \(E_{3}=\{7,8\}\).
  \item Au dernier tour, on fusionne 7 et 8 en un noeud baptisé 9 , de probabilité \(\frac{9}{20}+\frac{11}{20}=1\).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-19}
\end{itemize}

Codage de Huffman\\
Cela donne le codage suivant.

\begin{center}
\begin{tabular}{|l|ccccc|}
\hline
\(i\) & 1 & 2 & 3 & 4 & 5 \\
\hline
\(C(i)\) & 11 & 00 & 01 & 100 & 101 \\
\hline
\end{tabular}
\end{center}

Le codage minimal n'est pas unique, loin de là.\\
Proposition 47 Soit \(E\) un ensemble fini muni d'une distribution de probabilité \(p\). L'algorithme qui vient d'être décrit produit un arbre dont les feuilles sont numérotées par E, de profondeur moyenne minimale.

Preuve Par récurrence sur \(|E|\), on montre que pour tout arbre \(A\) et toute bijection \(\phi: E \rightarrow \partial A\), la profondeur moyenne de \(A\), notée \(\mathbb{E}\left(\ell_{A} \circ \phi\right)\) est supérieure ou égale à celle de l'arbre ( \(A_{0}, \phi_{0}\) : \(E \rightarrow \partial A_{0}\) ) construit par l'algorithme.

Initialisation. Lorsque \(|E|=1\), il n'y a qu'un arbre possible, \(A=A_{0}\) ont même profondeur moyenne.

Supposons que l'algorithme construise un arbre optimal pour tous les ensembles \(E\) à \(n\) éléments. Soit \(E\) un ensemble ordonné à \(n+1\) éléments, muni d'une distribution de probabilité \(p\). Soit \(A\) un arbre et \(\phi: E \rightarrow \partial A\) une bijection. Soit ( \(A_{0}, \phi_{0}: E \rightarrow \partial A_{0}\) ) l'arbre construit par l'algorithme.

Opération préliminaire. Soit \(\left\{a, a^{\prime}\right\}\) une paire de feuilles de profondeur maximale dans \(A\), ayant le même ascendant. Soit \(\left\{x, x^{\prime}\right\}\) la paire d'éléments de \(E\) fusionnée au premier tour. En composant \(\phi\) avec une permutation de \(E\), remplaçons \(\phi\) par \(\tilde{\phi}=\phi \circ \sigma: E \rightarrow \partial A\), de sorte que \(\tilde{\phi}(a)=x\) et \(\tilde{\phi}\left(a^{\prime}\right)=x^{\prime}\). La permutation \(\sigma\) répartit différemment les probabilités sans changer les profondeurs. Comme \(x\) et \(x^{\prime}\) sont de probabilités minimales et \(a, a^{\prime}\) de profondeurs maximales, on peut choisir \(\sigma\) de sorte qu'elle n'augmente pas la longueur moyenne, \(\mathbb{E}\left(\ell_{A} \circ \phi\right) \geq \mathbb{E}\left(\ell_{A} \circ \tilde{\phi}\right)\).

Supprimons les feuilles \(a\) et \(a^{\prime}\). On obtient un arbre \(A^{\prime}\) et une bijection \(\phi^{\prime}: E^{\prime} \rightarrow \partial A^{\prime}\) qu'il faut comparer à l'arbre \(A_{1}\), muni de sa bijection évidente \(\phi_{1}: E^{\prime} \rightarrow \partial A_{1}\), produit par l'algorithme. La profondeur moyenne satisfait

\[
\begin{aligned}
\mathbb{E}\left(\ell_{A^{\prime}} \circ \phi^{\prime}\right) & =\mathbb{E}\left(\ell_{A} \circ \tilde{\phi}\right)-p^{\prime}(y), \\
\mathbb{E}\left(\ell_{A_{1}} \circ \phi_{1}\right) & =\mathbb{E}\left(\ell_{A_{0}} \circ \phi_{0}\right)-p^{\prime}(y) .
\end{aligned}
\]

Par l'hypothèse de récurrence, \(\mathbb{E}\left(\ell_{A^{\prime}} \circ \phi^{\prime}\right) \geq \mathbb{E}\left(\ell_{A_{1}} \circ \phi_{1}\right)\). Par conséquent, \(\mathbb{E}\left(\ell_{A} \circ \tilde{\phi}\right) \geq \mathbb{E}\left(\ell_{A_{0}} \circ \phi_{0}\right)\), et donc \(\mathbb{E}\left(\ell_{A} \circ \phi\right) \geq \mathbb{E}\left(\ell_{A_{0}} \circ \phi_{0}\right)\).

Suggestion d'exercice : \(\mathrm{n}^{0} 4\), feuille 2.

Fin du cours \(\mathrm{n}^{0} 4\)

Corollaire 48 Au jeu des 10 questions, la durée moyenne du jeu est au moins égale à l'entropie.\\
Preuve D'après la Proposition 46, la question se traduit en termes de codage sans préfixes. Ce codage est en particulier uniquement décodable (on s'en est servi dans la preuve du théorème de codage de source), donc d'après le théorème de codage de source, sa longueur moyenne satisfait \(\mathbb{E}(\ell(X)) \geq H(X)\).

Exemple 49 Soit \(X\) une variable qui suit une loi géométrique de paramètre \(\frac{1}{2}\) (par exemple, le temps d'obtention de face au jeu de pile ou face). Alors il existe une stratégie dont la durée moyenne est \(H(X)=2\) et on ne peut pas faire mieux.

Voir l'Exercice 2 de la feuille 1, pour la stratégie de durée moyenne 2.\\
Suggestion d'exercice : \(\mathrm{n}^{0} 5\), feuille 2 .\\
Suggestion d'exercice : \(\mathrm{n}^{0} 6\), feuille 2.

\subsection*{3.6 A retenir}
\begin{itemize}
  \item L'interprétation de l'entropie comme longueur moyenne minimale d'un codage d'une source (asymptotiquement).
  \item L'algorithme de Huffman.
\end{itemize}

Il s'agit d'un chapitre pratique, où on a utilisé les outils du chapitre précédent pour résoudre un problème concret.

\section*{4 Entropie des processus stationnaires}
Dans ce chapitre, on cherche à établir le second principe de la thermodynamique. On étudie l'entropie de processus sensés modéliser des systèmes physiques. Il s'avère que leur entropie ellemême n'augmente pas, c'est l'entropie relative à la condition initiale qui augmente. On généralise ensuite la notion d'entropie aux processus aléatoires stationnaires. Pour un processus, c'est l'entropie "par symbole" qui remplace l'entropie d'une variable isolée. Ce taux de croissance se calcule particulièrement bien dans le cas des chaînes de Markov stationnaires.

\subsection*{4.1 Chaînes de Markov}
Définition 50 Une chaîne de Markov est une suite \(X_{0}, X_{1}, \ldots\) de variables aléatoires à valeurs dans un ensemble \(E\) (l'ensemble des états de la chaîne) dont la dépendance au passé se résume à la dépendance à la dernière position. Autrement dit, pour tous états \(x_{0}, \ldots, x_{n+1} \in E\),

\[
\mathbb{P}\left(X_{n+1}=x_{n+1} \mid X_{n}=x_{n}, X_{n-1}=x_{n-1}, \ldots, X_{0}=x_{0}\right)=\mathbb{P}\left(X_{n+1}=x_{n+1} \mid X_{n}=x_{n}\right)
\]

On fera, sans le dire, l'hypothèse supplémentaire que la chaîne est indépendante du temps, i.e. que les probabilités conditionnelles ne dépendent pas de \(n\),

\[
\mathbb{P}\left(X_{n+1}=j \mid X_{n}=i\right)=P_{i j}
\]

La matrice \(P=\left(P_{i j}\right)_{i, j \in E}\) s'appelle la matrice des probabilités de transition.

Proposition 51 Soit \(X_{0}, X_{1}, \ldots\) une chaîne de Markov. La loi de \(X_{n}\) est entièrement déterminée par celle de \(X_{0}\) et par la matrice \(P\) des probabilités de transition. Si on représente les distributions de probabilité par des vecteurs lignes, alors

\[
p_{X_{n}}=p_{X_{n-1}} P=p_{X_{0}} P^{n}
\]

Plus généralement, la loi jointe de \(\left(X_{0}, X_{1}, \ldots, X_{n}\right)\) est déterminée par \(P\) et par la loi de \(X_{0}\).\\
Preuve La loi de \(X_{n+1}\)

\[
\begin{aligned}
\mathbb{P}\left(X_{n+1}=y\right) & =\sum_{x \in E} \mathbb{P}\left(X_{n+1}=y \text { et } X_{n}=x\right) \\
& =\sum_{x \in E} \mathbb{P}\left(X_{n+1}=y \mid X_{n}=x\right) \mathbb{P}\left(X_{n}=x\right) \\
& =\sum_{x \in E} P_{x y} \mathbb{P}\left(X_{n}=x\right)
\end{aligned}
\]

s'exprime en fonction de \(P\) et de la loi de \(X_{n}\).\\
Plus généralement, par la définition d'une chaîne de Markov, la loi jointe

\[
\begin{aligned}
p_{\left(X_{0}, \ldots, X_{n}\right)}\left(x_{0}, \ldots, x_{n}\right) & =\mathbb{P}\left(X_{0}=x_{0}, \ldots, X_{n}=x_{n}\right) \\
& =\mathbb{P}\left(X_{n}=x_{n} \mid X_{0}=x_{0}, \ldots, X_{n-1}=x_{n-1}\right) \mathbb{P}\left(X_{0}=x_{0}, \ldots, X_{n-1}=x_{n-1}\right) \\
& =\mathbb{P}\left(X_{n}=x_{n} \mid X_{n-1}=x_{n-1}\right) \mathbb{P}\left(X_{0}=x_{0}, \ldots, X_{n-1}=x_{n-1}\right) \\
& =P_{x_{n-1} x_{n}} \mathbb{P}\left(X_{0}=x_{0}, \ldots, X_{n-1}=x_{n-1}\right) \\
& \vdots \\
& =P_{x_{n-1} x_{n}} P_{x_{n-2} x_{n-1}} \cdots P_{x_{1} x_{2}} p_{X_{0}}\left(x_{0}\right)
\end{aligned}
\]

s'exprime directement en fonction de \(P\) et \(p_{X_{0}}\).\\
Remarque 52 Représentation graphique. Une chaîne de Markov sur E peut être schématisée par un graphe orienté dont l'ensemble des sommets est \(E\). Chaque arête orientée \((x, y)\) porte la probabilité de transition \(P_{x y}\). Chaque sommet \(x\) porte sa probabilité \(p(x)\).

Voici le schéma correspondant à la chaîne à 2 états de probabilités respectives \(\gamma\) et \(1-\gamma\) et de matrice de probabilités de transition \(\left(\begin{array}{cc}1-\alpha & \alpha \\ \beta & 1-\beta\end{array}\right)\).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-21}

Graphe orienté schématisant une chaîne de Markov\\
On admettra le théorème suivant (on reviendra sur ce point au paragraphe 5.6).\\
Théorème 6 Soit \(P\) une matrice stochastique (les coefficients sont positifs ou nuls, les sommes par ligne valent 1) de taille \(d\). Soit \(\mu\) une distribution de probabilité sur \(\{1, \ldots, d\}\). Il existe une chaîne de Markov \(X_{0}, X_{1}, \ldots\) à valeurs dans \(\{1, \ldots, d\}\) dont la matrice des probabilités de transition est \(P\) et telle que la loi de \(X_{0}\) est \(\mu\).

Exemple 53 (Marches aléatoires) Soit \(G=(E, w)\) un graphe pondéré fini : il y a un ensemble fini \(E\) de sommets, et pour chaque paire de sommets distincts \(\{x, y\}\), un réel positif ou nul \(w(x, y)=\) \(w(y, x) \quad(w(x, y)=0\) signifie que le graphe \(G\) ne contient pas l'arête xy). La marche aléatoire d'origine \(x_{0}\) est la chaîne de Markov à valeurs dans \(E\) telle que \(X_{0}=x_{0}\) et dont la matrice des probabilités de transitions est définie par

\[
P_{x y}=\frac{w(x, y)}{\sum_{z \neq x} w(x, z)}
\]

si \(x \neq y\), et \(P_{x x}=0\).\\
Autrement dit, on saute d'un sommet à l'un de ses voisins le long d'une arête choisie au hasard, avec probabilité proportionnelle à son poids.

\subsection*{4.2 Processus stationnaires}
Définition 54 Un processus aléatoire \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N} \text { ou } \mathbb{Z}}\) est stationnaire si pour tout \(k \in \mathbb{N}\), la loi jointe de \(\left(X_{n+1}, \ldots, X_{n+k}\right)\) ne dépend pas de \(n\).

Autrement dit, une translation dans le temps n'a pas d'effet sur la loi du processus.\\
Exemple 55 Une suite de variables aléatoires indépendantes est un processus stationnaire si et seulement si les variables ont toutes même loi.

Proposition 56 Une chaîne de Markov \(X_{0}, X_{1}, \ldots\) de loi initiale \(p\) et de matrice de probabilités de transition \(P\) est stationnaire si et seulement si \(p P=p\).

Preuve Si la chaîne est stationnaire, alors \(X_{0}\) et \(X_{1}\) ont même loi, \(p=p_{X_{0}}=p_{X_{1}}=p P\). Réciproquement, si \(p=p P\), alors \(p_{X_{1}}=p\). Le processus \(Y_{n}=X_{n+1}\) a même loi initiale et même matrice de probabilités de transition que \(X_{n}\), donc mêmes lois jointes (Proposition 51). Cela entraîne que pour tout \(k\) et tout \(n\), la loi jointe de ( \(X_{n}, \ldots, X_{n+k}\) ) coïncide avec la loi de \(\left(X_{0}, \ldots, X_{k}\right)\).

Exemple 57 Pour une chaîne de Markov à deux états, la matrice des probabilités de transition est de la forme \(\left(\begin{array}{cc}1-\alpha & \alpha \\ \beta & 1-\beta\end{array}\right)\). Il \(y\) a une unique distribution stationnaire, le vecteur ligne \(\left(\begin{array}{ll}\frac{\beta}{\alpha+\beta} & \frac{\alpha}{\alpha+\beta}\end{array}\right)\).

On admettra le théorème élémentaire suivant (algèbre linéaire).\\
Théorème 7 Soit \(P\) une matrice stochastique (les coefficients sont positifs ou nuls, les sommes par ligne valent 1). On suppose \(P\) irréductible (il existe une puissance de \(P\) dont tous les coefficients sont strictement positifs) et apériodique (pour tout \(i\), le pgcd des entiers \(n\) tels que \(\left(P^{n}\right)_{i i}>0\) vaut 1). Alors il existe une unique distribution de probabilité stationnaire \(\mu\). Pour toute chaîne de Markov de matrice de probabilités de transitions \(P\), la loi de \(X_{n}\) tend vers \(\mu\) quand \(n\) tend vers l'infini.

Exemple 58 Pour la marche aléatoire sur le graphe pondéré \(G=(E, w)\), la distribution de probabilité suivante

\[
\mu(x)=\frac{\sum_{z \neq x} w(x, z)}{\sum_{z \in E} \sum_{z^{\prime} \neq z} w\left(z, z^{\prime}\right)}
\]

est stationnaire.\\
Voir Exercice 1, feuille 3.\\
Suggestion d'exercice : \(\mathrm{n}^{0} 1\), feuille 3 .

\subsection*{4.3 Second principe de la thermodynamique}
On considère qu'une chaîne de Markov constitue un bon modèle d'un système physique isolé. En effet, le caractère aléatoire résulte de la simplification d'un modèle déterministe (on substitue aux variables microscopiques des observables macroscopiques obtenues en faisant des moyennes), conformément au paradigme de la physique statistique. La condition sur la dépendance par rapport au passé traduit le fait qu'au niveau microscopique, le système suit une évolution déterministe gouvernée par des équations différentielles (une position et une vitesse initiales, i.e. une position dans un espace des phases, déterminent l'évolution future).

L'entropie des physiciens est le logarithme du nombre de configurations microscopiques correspondant à l'état macroscopique du système. On donne fréquemment l'exemple suivant. Soit un système isolé comportant \(N\) molécules identiques, qui peuvent occuper différents états numérotés de 1 à \(\ell\). On considère que l'état macroscopique du système est décrit par les effectifs \(N_{1}, \ldots, N_{\ell}\) de chaque état, où, de façon équivalente, par les proportions \(p_{i}=\frac{N_{i}}{N}\). Un même état macrocopique est réalisé à l'échelle microscopique de multiples manières. Le nombre de façons de répartir les \(N\) molécules entre les états dans les proportions prescrites \(p_{i}\) est le coefficient multinômial

\[
\nu\left(p_{1}, \ldots, p_{\ell}\right)=\frac{N!}{N_{1}!\ldots N_{\ell}}
\]

D'après Boltzmann, l'entropie du système dans l'état macroscopique \(p=\left(p_{1}, \ldots, p_{\ell}\right)\) est

\[
S=k \log \nu(p)
\]

où \(k\) est une constante physique, la constante de Boltzmann. En utilisant la formule de Stirling, on voit que, lorsque les \(N_{i}\) sont grands,

\[
\begin{aligned}
S & \sim N \log _{2} N-\sum_{i=1}^{\ell} N_{i} \log _{2} N_{i} \\
& =N\left(-\sum_{i=1}^{\ell} \frac{N_{i}}{N} \log _{2} \frac{N_{i}}{N}\right) \\
& =N H(p)
\end{aligned}
\]

donc l'entropie par molécule vaut \(H(p)\).\\
Il semble que seules les chaînes de Markov dont la distribution stationnaire est uniforme aient une réalité physique.

Théorème 8 Soit \(X_{0}, X_{1}, \ldots\) une chaîne de Markov. On suppose que la distribution uniforme est stationnaire. Alors l'entropie augmente : la suite \(H\left(X_{n}\right)\) est croissante.

Remarque 59 Il est facile de caractériser les chaînes de Markov pour lesquelles la distribution uniforme est stationnaire. Voir Exercice 2, feuille 3.

Suggestion d'exercice: \(\mathrm{n}^{0} 2\), feuille 3 .\\
Le Théorème 8 résulte du fait plus général suivant.\\
Théorème 9 Soient \(\mu\) et \(\mu^{\prime}\) deux distributions de probabilité sur un ensemble fini \(E\). Soient \(\mu_{n}=\) \(\mu P^{n}\) et \(\mu_{n}^{\prime}=\mu^{\prime} P^{n}\) les évolutions de ces distributions sous une même matrice stochastique \(P\). Alors l'entropie relative \(D\left(\mu_{n}, \mu_{n}^{\prime}\right)\) décroît.

\section*{Preuve du Théorème 9.}
Pour \((x, y) \in E \times E\), on pose \(p_{n}(x, y)=\mu_{n}(x) P_{x y}\) et \(q_{n}(x, y)=\mu_{n}^{\prime}(x) P_{x y}\). Par construction,

\[
p_{n, X}=\mu_{n}, \quad q_{n, X}=\mu_{n}^{\prime}, \quad p_{n, Y}=\mu_{n+1}, \quad q_{n, Y}=\mu_{n+1}^{\prime}
\]

et, pour \((x, y) \in E \times E\),

\[
p_{n, Y \mid X=x}(y)=\frac{\mu_{n}(x) P_{x y}}{p_{n, X}(x)}=P_{x y}=q_{n, Y \mid X=x}(y)
\]

les \(D\left(p_{n, Y \mid X=x} \| q_{n, Y \mid X=x}\right)\) sont toutes nulles, donc \(D\left(p_{n, Y \mid X} \| q_{n, Y \mid X}\right)=0\). En utilisant deux fois la proposition 36, on obtient

\[
\begin{aligned}
D\left(\mu_{n} \| \mu_{n}^{\prime}\right) & =D\left(p_{n, X} \| q_{n, X}\right) \\
& =D\left(p_{n, X} \| q_{n, X}\right)+D\left(p_{n, Y \mid X} \| q_{n, Y \mid X}\right) \\
& =D\left(p_{n} \| q_{n}\right) \\
& =D\left(p_{n, Y} \| q_{n, Y}\right)+D\left(p_{n, X \mid Y} \| q_{n, X \mid Y}\right) \\
& \geq D\left(\mu_{n+1} \| \mu_{n+1}^{\prime}\right)
\end{aligned}
\]

\(\operatorname{car} D\left(p_{n, X \mid Y} \| q_{n, X \mid Y}\right) \geq 0\).

\section*{Preuve du Théorème 8.}
On applique le Théorème 9 à la loi \(\mu\) de \(X_{0}\) et à la distribution uniforme \(\mu^{\prime}\). Par hypothèse, \(\mu^{\prime}\) est stationnaire, donc \(\mu_{n}^{\prime}=\mu^{\prime}\). En revanche, \(\mu_{n}\) est la loi de \(X_{n}\), qui dépend de \(n\). On sait que \(D\left(\mu_{n}, \mu^{\prime}\right)=\log |E|-H\left(\mu_{n}\right)\), donc \(H\left(\mu_{n}\right)=H\left(X_{n}\right)\) décroît.

Suggestion d'exercice : \(\mathrm{n}^{0} 3\), feuille 3 .\\
Suggestion d'exercice : \(\mathrm{n}^{0} 4\), feuille 3 .\\
Pour une autre manifestation de la croissance de l'entropie, voir l'Exercice 5, feuille 3.\\
Suggestion d'exercice: \(\mathrm{n}^{0} 5\), feuille 3 .\\
Suggestion d'exercice : \(\mathrm{n}^{0} 6\), feuille 3 .

\subsection*{4.4 Les langues naturelles comme processus stochastiques}
Au chapitre précédent, on a vu une langue naturelle comme une source, de lettres ou de mots. Autrement dit, comme une distribution de probabilité sur l'ensemble des 27 lettres (inclure l'espace), ou sur l'ensemble des mots du dictionnaire.

Dans l'hypothèse où les textes seraient des tirages aléatoires indépendants (de lettres ou de mots), l'entropie de cette distribution mesure la probabilité des textes typiques. Ce n'est réaliste que pour les textes produits par un singe tapant à la machine ou tirant des mots dans un dictionnaire.

Une modélisation plus fidèle du langage consiste à voir un texte comme une réalisation d'un processus stochastique \(\Xi=\left(X_{1}, X_{2}, \ldots\right)\) non nécessairement indépendant. En première approximation, on peut penser que ce processus est stationnaire : les règles de production de phrases varient suffisamment lentement dans le temps pour qu'on puisse négliger cette variation. Ce qui nous renseigne sur la probabilité de phrases typiques, c'est l'entropie jointe \(H\left(X_{1}, \ldots, X_{n}\right)\). Pour les phrases écrites par le singe, les \(X_{i}\) sont indépendantes, donc \(H\left(X_{1}, \ldots, X_{n}\right)=n H\left(X_{1}\right)\) est proportionnelle aux nombre de mots. Pour avoir une quantité qui ne tend pas vers l'infini, on considère l'entropie "par symbole" (lettre ou mot) \(\frac{1}{n} H\left(X_{1}, \ldots, X_{n}\right)\) même dans le cas général.

\subsection*{4.5 Entropie par symbole}
Définition 60 Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\) un processus aléatoire. L'entropie par symbole du processus est la limite

\[
H(\Xi)=\lim _{n \rightarrow \infty} \frac{1}{n} H\left(X_{1}, \ldots, X_{n}\right)
\]

lorsqu'elle existe.\\
Remarque 61 Cette limite n'existe pas toujours. Par exemple, pour une suite de variables indépendantes \(\frac{1}{n} H\left(X_{1}, \ldots, X_{n}\right)=\frac{1}{n} \sum_{i=1}^{n} H\left(X_{i}\right)\) peut faire à peu près n'importe quoi.

Théorème 10 Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\) un processus stationnaire. Alors les limites suivantes existent et sont égales.

\[
H(\Xi)=\lim _{n \rightarrow \infty} \frac{1}{n} H\left(X_{1}, \ldots, X_{n}\right)=\lim _{n \rightarrow \infty} H\left(X_{n} \mid X_{n-1}, \ldots, X_{1}\right)
\]

Preuve D'après le principe "conditionner diminue l'entropie",

\[
H\left(X_{n+1} \mid X_{n}, \ldots, X_{1}\right) \leq H\left(X_{n+1} \mid X_{n-1}, \ldots, X_{2}\right)=H\left(X_{n} \mid X_{n-1}, \ldots, X_{1}\right)
\]

par stationnarité. Une suite décroissante positive ayant une limite, la suite \(u_{n}=H\left(X_{n} \mid X_{n-1}, \ldots, X_{1}\right)\) converge. D'autre part, d'après la Proposition 24,

\[
H\left(X_{1}, \ldots, X_{n}\right)=\sum_{i=1}^{n} H\left(X_{i} \mid X_{i-1}, \ldots, X_{1}\right)=\sum_{i=1}^{n} u_{i} .
\]

Pour tout suite convergente \(u_{n}, v_{n}=\frac{1}{n} \sum_{i=1}^{n} u_{i}\) converge vers la même limite.\\
Exemple \(62 S i \Xi\) est une suite de variables indépendantes de même loi \(p\), alors \(H(\Xi)=p\).\\
Il faut donc penser à l'entropie par symbole \(H(\Xi)\) comme une généralisation aux processus de l'entropie d'une variable isolée.

Proposition 63 Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\) une chaîne de Markov stationnaire. Alors

\[
H(\Xi)=H\left(X_{1} \mid X_{0}\right)=-\sum_{x, y \in E} \mu(x) P_{x y} \log _{2}\left(P_{x y}\right),
\]

où \(\mu\) désigne la distribution de \(X_{0}\), qui est stationnaire.\\
Preuve Par définition d'une chaîne de Markov, la loi conditionnelle de \(X_{n} \mid X_{n-1}=x_{n-1}, \ldots\), \(X_{0}=x_{0}\) coïncide avec la loi conditionnelle \(X_{n} \mid X_{n-1}=x_{n-1}\), donc \(H\left(X_{n} \mid X_{n-1}, \ldots, X_{1}\right)=\) \(H\left(X_{n} \mid X_{n-1}\right)=H\left(X_{1}, X_{0}\right)\) par stationnarité.

Par définition de la matrice des probabilités de transition, la loi conditionnelle \(p_{X_{1} \mid X_{0}=x}(y)=\) \(P_{x y}\), donc \(H\left(p_{X_{1} \mid X_{0}=x}\right)=-\sum_{y \in E}\) et \(H\left(X_{1}, X_{0}\right)=-\sum_{x, y \in E} \mu(x) P_{x y} \log _{2}\left(P_{x y}\right)\).\\
Corollaire 64 Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\) une chaîne de Markov irréductible et apériodique. Alors l'entropie par symbole est bien définie et vaut \(H(\Xi)=-\sum_{x, y \in E} \mu(x) P_{x y} \log _{2}\left(P_{x y}\right)\), où \(\mu\) est la distribution stationnaire et \(P\) la matrice des probabilités de transition.

Exemple 65 Soit \(G=(E, w)\) un graphe pondéré fini. On pose, pour \(x \in E, W(x)=\sum_{y \neq x} w(x, y)\) et \(W=\sum_{x \in E} W(x)\). On introduit deux distributions de probabilité, \(p(x, y)=\frac{w(x, y)}{W}\) sur \(E \times E\) et \(\mu(x)=\frac{W(x)}{W}\) sur \(E\). Soit \(\Xi\) une marche aléatoire sur \(G\). On suppose qu'elle est irréductible et apériodique. Alors \(H(\Xi)=H(p)-H(\mu)\).

Voir Exercice 7, feuille 3.\\
Suggestion d'exercice : \(\mathrm{n}^{0} 7\), feuille 3 .\\
Suggestion d'exercice : \(\mathrm{n}^{0} 8\), feuille 3 .

\subsection*{4.6 Entropie par symbole d'une langue naturelle}
La mesure directe de l'entropie par lettre (ou par mot) d'une langue naturelle est malaisée. Par exemple, si \(X_{n}\) désigne la \(n\)-ème lettre d'un texte tiré au hasard, les lois conditionnelles de \(X_{5} \mid X_{4}=x_{4}, X_{3}=x_{3}, X_{2}=x_{2}, X_{1}=x_{1}\) sont au nombre de \(27^{4}=531441\). Evaluer directement ces probabilités nécessite de traiter des millions de bouts de textes. Aller loin au delà n'est pas envisageable, sans parler d'évaluer une limite.

En 1951, Shannon a suggéré de procéder autrement, au moyen d'un protocole expérimental faisant intervenir des volontaires (étudiants) à qui on présente des textes tronqués, de longueur 75. Pour chaque texte, chaque volontaire doit deviner la lettre suivante. Il doit fournir une liste de 27 lettres ordonnée par probabilité décroissante. Dans chaque liste, l'expérimentateur note le rang de la lettre qui était effectivement la suivante dans le texte examiné. Par exemple, confronté au texte "allons enfants de la patrie le jour de gloire est arrive contre nous de la ", le volontaire répond la liste ordonnée : \(m, t, e, c, a, b, \ldots\), et l'expérimentateur note : 2 . Il fait de même avec 11 autres volontaires, confrontés à 10 textes chacun, soigneusement tirés au hasard dans un gros livre. Il obtient 120 notes comprises entre 1 et 27, dont il fait un histogramme, i.e. le tableau des effectifs \(N_{1}, \ldots, N_{27}\). Il calcule une entropie empirique

\[
h=-\sum_{i=1}^{27} \frac{N_{i}}{120} \log _{2}\left(\frac{N_{i}}{120}\right)
\]

L'expérience, conduite par Shannon en 1950, a donné une valeur de 1.3 bits par lettre pour la langue anglaise.

Pourquoi cette valeur expérimentale constitue t'elle une valeur approchée de l'entropie de la langue anglaise? On fait l'hypothèse que les volontaires connaissent bien leur langue, et qu'ils produisent tous leurs listes en suivant le même raisonnement déterministe. La note ne dépend alors que du texte. Si le texte est tiré au hasard, cette note devient une variable aléatoire \(Y\) à valeurs dans \(E=\{1, \ldots, 27\}\). Cette variable contient la même information que la variable \(X_{76}\) : étant donné un texte \(t=t_{1} \ldots t_{76}\), la 76ème lettre \(t_{76}=X_{76}(t)\) de \(t\) est uniquement déterminée par \(t_{1} \ldots t_{75}\) et \(Y\left(t_{1} \ldots t_{75}\right)\). Donc les lois conditionnelles de \(Y\) et de \(X_{76}\) sachant les 75 premières lettres sont les mêmes, à une bijection de \(E\) sur l'alphabet près. En particulier, l'entropie \(H(Y)=\) \(H\left(X_{76} \mid X_{1}, \ldots, X_{75}\right)\). L'expérience constitue une simulation de la variable \(Y\). D'après la loi des grands nombres, quand le nombre de volontaires tend vers l'infini, l'histogramme des notes converge vers la loi de \(Y\), donc l'entropie empirique calculée à partir de cet histogramme converge vers \(H\left(X_{76} \mid X_{1}, \ldots, X_{75}\right)\). Celle-ci constitue un majorant de l'entropie par lettre de la langue anglaise. Les hésitations et erreurs introduites par les volontaires tendent à augmenter l'entropie de \(Y\), cela va dans le même sens : la valeur réelle de l'entropie par lettre de la langue anglaise est inférieure à la valeur empirique.

Comment interpréter la valeur numérique 1.3 bits par lettre? Il faut d'abord la comparer au maximum possible, \(\log _{2}(27)=4.75\), atteinte lorsque les lettres sont tirées indépendamment selon la distribution uniforme. Lorsqu'on tire les lettres indépendamment selon la distribution qu'elles ont en anglais, donnée par le tableau suivant,

\begin{center}
\begin{tabular}{|c|c|c|c|c|c|c|c|c|c|c|c|c|c|}
\hline
lettre & e & t & a & o & i & n & s & h & r & d & l & c & u \\
\hline
\(\%\) & 13 & 9 & 8 & 7,5 & 7 & 6,8 & 6,5 & 6,2 & 6 & 4 & 3,8 & 2,8 & 2,7 \\
\hline
lettre & m & w & f & g & y & p & b & v & k & j & x & q & z \\
\hline
\(\%\) & 2,5 & 2,4 & 2,3 & 2,1 & 2 & 1,9 & 1,5 & 1,1 & 0,9 & 0,2 & 0,2 & 0,1 & 0,1 \\
\hline
\end{tabular}
\end{center}

l'entropie vaut 4.2. Une meilleure approximation est obtenue par une chaîne de Markov stationnaire dont la matrice des probabilités de transition est tirée du tableau des fréquences des couples de lettres en anglais. L'entropie par lettre de cette chaîne vaut 4.03 bits par lettre. On peut raffiner en tirant chaque lettre supplémentaire en respectant la loi jointe de 3 ou de 4 lettres, dont l'entropie vaut 2.3 bits par lettre. Une entropie nettement plus basse, de 1.3 bits par lettre, signifie que l'anglais est bien plus déterministe, du fait des règles de grammaire et de construction des mots.

\subsection*{4.7 A retenir}
\begin{itemize}
  \item La notion de chaîne de Markov.
  \item La notion d'entropie par symbole.
  \item Le cas des chaînes de Markov.
\end{itemize}

Il s'agit d'un chapitre mi-théorique, mi-pratique, où on donne une définition précise à une notion qui provient de problèmes concrets, tout en posant les bases de développements ultérieurs.

\section*{5 Equipartition asymptotique}
Dans cette section, on donne une nouvelle preuve du théorème du codage de source, basée sur l'idée que la loi jointe de variables iid est concentrée.

\subsection*{5.1 Comportement typique pour une suite de variables iid}
Notation 66 Un processus aléatoire à valeurs dans un ensemble \(E\), c'est simplement une suite de variables aléatoires \(\Xi=\left(X_{n}\right)_{n \in I}\) à valeurs dans \(E\), indexée par \(I=\mathbb{N}, \mathbb{N} \backslash\{0\}\) ou \(\mathbb{Z}\). Pour \(m \leq n\), on note \(X_{m}^{n}=\left(X_{m}, X_{m+1}, \ldots, X_{n}\right)\). On note \(p_{m}^{n}\) sa loi, une distribution de probabilité sur \(E^{n}\).

On s'intéresse à la probabilité de sortie d'une suite \(\left(x_{1}, \ldots, x_{n}\right) \in E^{n}, p_{1}^{n}\left(x_{1}, \ldots, x_{n}\right)\), vue comme variable aléatoire. Autrement dit, \(p_{1}^{n}\) est une fonction sur \(E^{n}\), et on considère la variable aléatoire \(p_{1}^{n}\left(X_{1}, \ldots, X_{n}\right)\), qu'on peut noter \(p_{1}^{n}\left(X_{1}^{n}\right)\).

Question 67 Etant donné un processus aléatoire \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\), quel est le comportement asymptotique de la variable aléatoire \(p_{1}^{n}\left(X_{1}^{n}\right)\) lorsque \(n\) tend vers l'infini?

On va formaliser le raisonnement indiqué au début du cours (paragraphe 1.2).\\
Rappel 68 (Loi des grands nombres) Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\) une suite de variables aléatoires indépendantes, de même loi, intégrables (i.e. \(\mathbb{E}\left(\left|X_{0}\right|\right)<+\infty\) ). Alors la suite \(S_{n}=\frac{1}{n} \sum_{i=1}^{n} X_{i}\) converge presque sûrement vers la constante \(\mathbb{E}\left(X_{0}\right)\). En particulier, elle converge en probabilité, i.e. pour tout \(\epsilon>0\), la probabilité \(\mathbb{P}\left(\left|S_{n}-\mathbb{E}\left(X_{0}\right)\right|>\epsilon\right)\) tend vers 0 quand \(n\) tend vers \(+\infty\).

A toutes fins utiles, on donne au paragraphe 5.11 une preuve de la loi des grands nombres sous l'hypothèse (suffisante pour l'application ci-dessous) que \(X_{0}\) est bornée. Et on explique pourquoi convergence presque sûre implique convergence en probabilité.

Théorème 11 (Equipartition asymptotique) Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\) une suite de variables aléatoires indépendantes, de même loi \(p\). Alors \(-\frac{1}{n} \log _{2} p_{1}^{n}\left(X_{1}^{n}\right)\) converge presque partout et en probabilité vers la constante \(H(p)\). En particulier, pour tout \(\epsilon>0\), la probabilité \(\mathbb{P}\left(\left|-\frac{1}{n} \log _{2} p_{1}^{n}\left(X_{1}^{n}\right)-H(p)\right|>\right.\) \(\epsilon)\) tend vers 0 quand \(n\) tend vers \(+\infty\).

Preuve Par indépendance, \(p_{1}^{n}\left(x_{1}, \ldots, x_{n}\right)=p\left(x_{1}\right) \cdots p\left(x_{n}\right)\). Les variables \(Z_{i}=-\log _{2} p\left(X_{i}\right)\) sont indépendantes, donc, d'après la loi des grands nombres

\[
-\frac{1}{n} \log _{2} p_{1}^{n}\left(X_{1}^{n}\right)=\frac{1}{n} \sum_{i=1}^{n} Z_{i} \rightarrow \mathbb{E}\left(-\log _{2} p\left(X_{0}\right)\right)=H(p)
\]

en probabilité.\\
Interprétation : Etant donné \(\epsilon>0\) et \(n \in \mathbb{N}\), soit \(T\) le sous-ensemble de \(E^{n}\) défini par

\[
T=\left\{t \in E^{n} ; 2^{-n(H(p)+\epsilon)} \leq p_{1}^{n}(t) \leq 2^{-n(H(p)-\epsilon)}\right\}
\]

et soit \(S\) son complémentaire. Alors \(p_{1}^{n}(S)\) tend vers 0 quand \(n\) tend vers \(+\infty\) à \(\epsilon\) fixé. De plus, comme \(p\) est presque constante sur \(T\), le nombre d'éléments de \(T\) est de l'ordre de \(2^{n H(p)}\). Précisément, pour \(n\) assez grand,

\[
(1-\epsilon) 2^{n(H(p)-\epsilon)} \leq|T| \leq 2^{n(H(p)+\epsilon)}
\]

On pense à \(T\) comme à l'ensemble des "suites typiques", ce sont celles qui ont le plus de chances de sortir.

Suggestion d'exercice : \(\mathrm{n}^{0} 1\), feuille 4 .

\subsection*{5.2 Suites conjointement typiques}
Il s'agit d'une variante de l'équipartition qui permet de donner une interprétation à l'information mutuelle. De plus, cette notion va être à l'origine d'un procédé de décodage utilisé au chapitre 6 .

Définition 69 Considérons deux variables aléatoires \(X\) et \(Y\) de loi jointe \(p_{(X, Y)}\). Soient \(n \in \mathbb{N}\) et \(\epsilon>0\). L'ensemble \(T=T(n, \epsilon) \subset\left(E_{X} \times E_{Y}\right)^{n}\) des suites conjointement typiques est l'ensemble des suites \((x, y)_{1}^{n}\) telles que

\begin{enumerate}
  \item \(\left|-\frac{1}{n} \log _{2}\left(p_{X}\left(x_{1}\right) \cdots p_{X}\left(x_{n}\right)\right)-H(X)\right|<\epsilon\).
  \item \(\left|-\frac{1}{n} \log _{2}\left(p_{Y}\left(y_{1}\right) \cdots p_{Y}\left(y_{n}\right)\right)-H(Y)\right|<\epsilon\).
  \item \(\left|-\frac{1}{n} \log _{2}\left(p_{(X, Y)}\left(x_{1}, y_{1}\right) \cdots p_{(X, Y)}\left(x_{n}, y_{n}\right)\right)-H(X, Y)\right|<\epsilon\).
\end{enumerate}

Autrement dit, \(x_{1}^{n}\) est typique pour \(p_{X}, y_{1}^{n}\) est typique pour \(p_{Y}\) et \(\left(\left(x_{1}, y_{1}\right), \ldots\left(x_{n}, y_{n}\right)\right)\) est typique pour \(p_{(X, Y)}\).

Proposition 70 Soit \((X, Y)_{1}^{n}\) une suite de variables indépendantes et de même loi \(p_{(X, Y)}\). Alors, pour \(n\) assez grand,

\begin{enumerate}
  \item \(\mathbb{P}\left((X, Y)_{1}^{n} \in T\right)\) tend vers 1 quand \(n\) tend vers \(+\infty\).
  \item \((1-\epsilon) 2^{n(H(X, Y)+\epsilon)} \leq|T| \leq 2^{n(H(X, Y)+\epsilon)}\).
  \item Soient \(\tilde{X}_{1}^{n}\) et \(\tilde{Y}_{1}^{n}\) des variables indépendantes, de lois respectives \(p_{X}\) et \(p_{Y}\). Alors, pour \(n\) assez grand,
\end{enumerate}

\[
(1-\epsilon) 2^{-n(I(X ; Y)+3 \epsilon)} \leq \mathbb{P}\left((\tilde{X}, \tilde{Y})_{1}^{n} \in T\right) \leq 2^{-n(I(X ; Y)-3 \epsilon)}
\]

Preuve 1. On applique la loi des grands nombres aux trois variables

\[
-\log _{2} p_{X}\left(X_{n}\right), \quad-\log _{2} p_{Y}\left(Y_{n}\right), \quad-\log _{2} p_{(X, Y)}\left(X_{n}, Y_{n}\right)
\]

Presque sûrement, les trois convergent respectivement vers \(H(X), H(Y)\) et \(H(X, Y)\). En particulier, il y a convergence en probabilité.\\
2. Pour chaque \(t \in T\), pour \(n\) assez grand,

\[
2^{-n(H(X, Y)+\epsilon)} \leq \mathbb{P}\left((X, Y)_{1}^{n}=t\right) \leq 2^{-n(H(X, Y)-\epsilon)}
\]

On écrit

\[
1 \geq \mathbb{P}\left((X, Y)_{1}^{n} \in T\right)=\sum_{t \in T} \mathbb{P}\left((X, Y)_{1}^{n}=t\right) \geq|T| 2^{-n(H(X, Y)+\epsilon)}
\]

D'où \(|T| \leq 2^{n(H(X, Y)+\epsilon}\). Inversement, pour \(n\) assez grand, \(\mathbb{P}\left((X, Y)_{1}^{n} \in T\right) \geq 1-\epsilon\), ce qui s'écrit

\[
1-\epsilon \leq \sum_{t \in T} \mathbb{P}\left((X, Y)_{1}^{n}=t\right) \leq|T| 2^{-n(H(X, Y)-\epsilon)}
\]

soit \(|T| \geq(1-\epsilon) 2^{n(H(X, Y)-\epsilon)}\).\\
3. Par hypothèse, pour \(t=\left(\left(x_{1}, y_{1}\right), \ldots,\left(x_{n}, y_{n}\right)\right) \in T\),

\[
\begin{aligned}
P\left((\tilde{X}, \tilde{Y})_{1}^{n}=t\right) & =p_{X}\left(x_{1}\right) \cdots p_{X}\left(x_{n}\right) p_{Y}\left(y_{1}\right) \cdots p_{Y}\left(y_{n}\right) \\
& \leq 2^{-n(H(X)-\epsilon)} 2^{-n(H(Y)-\epsilon)}
\end{aligned}
\]

d'où

\[
\mathbb{P}\left((\tilde{X}, \tilde{Y})_{1}^{n} \in T\right) \leq 2^{-n(H(X)-\epsilon)} 2^{-n(H(Y)-\epsilon)}|T| \leq 2^{-n(H(X, Y)-H(X)-H(Y)-3 \epsilon)}
\]

De même, pour \(t \in T\),

\[
P\left((\tilde{X}, \tilde{Y})_{1}^{n}=t\right) \geq 2^{-n(H(X)+\epsilon)} 2^{-n(H(Y)+\epsilon)}
\]

d'où

\[
\mathbb{P}\left((\tilde{X}, \tilde{Y})_{1}^{n} \in T\right) \geq 2^{-n(H(X)+\epsilon)} 2^{-n(H(Y)+\epsilon)}|T| \geq(1-\epsilon) 2^{-n(H(X)+H(Y)-H(X, Y)+3 \epsilon)}
\]

\subsection*{5.3 Application au codage de source}
Proposition 71 Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\) une suite de variables aléatoires indépendantes, de même loi p. Soit \(\mathcal{D}\) un alphabet à \(D=|\mathcal{D}|\) lettres. Soit \(\epsilon>0\). Il existe \(n_{0} \in \mathbb{N}\) tel que, pour tout \(n \geq n_{0}\), il se produit la chose suivante.

\begin{enumerate}
  \item Pour toute application injective \(C^{*}: E^{n} \rightarrow \mathcal{D}^{*}\), la longueur moyenne
\end{enumerate}

\[
\mathbb{E}\left(\frac{1}{n} \ell\left(X_{1}^{n}\right)\right) \geq \frac{1}{\log _{2} D} H(p)-\epsilon
\]

\begin{enumerate}
  \setcounter{enumi}{1}
  \item Il existe une application injective \(C^{*}: E^{n} \rightarrow \mathcal{D}^{*}\) dont la longueur moyenne
\end{enumerate}

\[
\mathbb{E}\left(\frac{1}{n} \ell\left(X_{1}^{n}\right)\right) \leq \frac{1}{\log _{2} D} H(p)+\epsilon
\]

Le premier énoncé est un peu plus fort que celui du Théorème 5 . En effet, si \(C: E \rightarrow \mathcal{D}^{*}\) est un codage uniquement décodable, son extension aux textes \(C^{*}: E^{*} \rightarrow \mathcal{D}^{*}\) est injective, et en particulier sa restriction à l'ensemble \(E^{n}\) des textes à \(n\) mots est injective. De plus, dans le contexte du Théorème \(5, C^{*}\left(x_{1}^{n}\right)=C\left(x_{1}\right) \cdots C\left(x_{n}\right)\), donc \(\ell\left(x_{1}^{n}\right)=\sum_{i=1}^{n} \ell\left(x_{i}\right), \mathbb{E}\left(\ell\left(X_{1}^{n}\right)\right)=\) \(\sum_{i=1}^{n} \mathbb{E}\left(\ell\left(X_{i}\right)\right)=n \mathbb{E}\left(\ell\left(X_{0}\right)\right)\). En faisant varier \(\epsilon\) et en faisant tendre \(n\) vers l'infini, l'inégalité \(\mathbb{E}\left(\ell\left(X_{0}\right)\right)=\mathbb{E}\left(\frac{1}{n} \ell\left(X_{1}^{n}\right)\right) \geq \frac{1}{\log _{2} D} H(p)-\epsilon\) entraîne que \(\mathbb{E}\left(\ell\left(X_{0}\right)\right) \geq \frac{1}{\log _{2} D} H\left(X_{0}\right)=\frac{H(p)}{\log _{2} D}\). Donc la proposition 71 implique bien la borne inférieure pour la longueur moyenne des codages uniquement décodables donnée par le Théorème 5. C'était d'ailleurs l'argument que Shannon avait en tête.

Le second énoncé ne se compare pas directement à celui de Shannon. Néanmoins, il indique bien que la borne par l'entropie est essentiellement optimale.

Preuve On étend à \(D\) quelconque le résultat de l'exercice 3 de la feuille 2 , limité au cas où \(D=2\). Par hypothèse, \(C^{*}\) est un codage non singulier de \(E^{n}\). Dans un codage non singulier optimal, les \(D\) premiers éléments sont codés par des suites de longueur 1 , les \(D^{2}\) suivant par des suites de longueur 2, etc... La longueur \(\ell\left(x_{i}\right)\) du \(i\)-ème élément de \(E\) satisfait

\[
\sum_{k=1}^{\ell\left(x_{i}\right)-1} D^{k}<i \leq \sum_{k=1}^{\ell\left(x_{i}\right)} D^{k}
\]

i.e.

\[
\frac{D^{\ell\left(x_{i}\right)}-1}{D-1}-1<i \leq \frac{D^{\ell\left(x_{i}\right)+1}-1}{D-1}-1
\]

ce qui s'écrit aussi

\[
\ell\left(x_{i}\right)<\log _{D}((D-1)(i+1)+1) \leq \ell\left(x_{i}\right)+1,
\]

soit \(\ell\left(x_{i}\right)=\left\lceil\log _{D}((D-1)(i+1)+1)\right\rceil=\left\lceil\log _{D}\left(\frac{(D-1) i}{D}+1\right)\right\rceil\). D'où l'inégalité

\[
\mathbb{E}(\ell(X)) \geq \sum_{i=1}^{|E|} p\left(x_{i}\right)\left\lceil\log _{D}\left(\frac{(D-1) i}{D}+1\right)\right\rceil \geq M:=\sum_{i=1}^{|E|} p\left(x_{i}\right) \log _{D}\left(\frac{(D-1) i}{D}+1\right)
\]

On pose

\[
c=\sum_{i=1}^{\left|E^{n}\right|} \frac{1}{\frac{(D-1) i}{D}+1}, \quad q\left(x_{i}\right)=\frac{1}{c} \frac{1}{\frac{(D-1) i}{D}+1}
\]

Comme \(q\) est une distribution de probabilité, on peut appliquer le Théorème 2,

\[
\begin{aligned}
0 & \leq \frac{D(p \| q)}{\log _{2}(D)}=\sum_{i=1}^{|E|^{n}} p\left(x_{i}\right) \log _{D} p\left(x_{i}\right)-\sum_{i=1}^{|E|^{n}} p\left(x_{i}\right) \log _{D} q\left(x_{i}\right) \\
& =-\frac{H(p)}{\log _{2} D}+\sum_{i=1}^{|E|^{n}} p\left(x_{i}\right) \log _{D}\left(c\left(\frac{(D-1) i}{D}+1\right)\right) \\
& =-\frac{H(p)}{\log _{2} D}+\log _{D}(c)+M
\end{aligned}
\]

d'où \(\frac{H(p)}{\log _{2} D}-M \leq \log _{D}(c)\).\\
Il reste à majorer \(c\). On utilise l'encadrement de la série harmonique par le logarithme népérien : si \(h(k)=\sum_{j=1}^{n} \frac{1}{j}\), alors \(h(k) \leq \ell n(k)+1\). Ici,\\
\(c=\sum_{i=1}^{|E|^{n}} \frac{1}{\frac{(D-1) i}{D}+1}=\frac{D}{D-1} \sum_{i=1}^{|E|^{n}} \frac{1}{i+\frac{D}{D-1}} \leq 2\left(h\left(|E|^{n}+1\right)-1\right) \leq 2 \ell n\left(|E|^{n}+1\right)-2 \leq 2 n \ell n(|E|)\).\\
On voit que \(\log _{D}(c) \leq O(\log (n))\). D'où la minoration \(\mathbb{E}\left(\frac{1}{n} \ell(X)\right) \geq \frac{H(p)}{\log _{2} D}-O\left(\frac{\log n}{n}\right) \geq \frac{H(p)}{\log _{2} D}-\epsilon\) pour \(n\) assez grand.

Inversement, soit \(T=T(n, \epsilon)\) l'ensemble des suites typiques et \(S\) son complémentaire. Soit \(L\) le plus petit entier tel que \(|T| \leq D^{L}\). Alors \(L \leq n \frac{(H(p)+\epsilon)}{\log _{2} D}+1\). Il existe une application injective \(C: T \rightarrow \mathcal{D}^{L}\), on choisit n'importe laquelle. Soit \(L^{\prime}\) le plus petit entier \(>L\) tel que \(\left|E^{n}\right| \leq D^{L^{\prime}}\). Alors \(L^{\prime} \leq n \frac{\log _{2}|E|}{\log _{2} D}+1\). Sur \(S\), on choisit une application injective à valeurs dans \(\mathcal{D}^{L^{\prime}}\). On calcule

\[
\begin{aligned}
\mathbb{E}\left(\frac{1}{n} \ell\left(X_{1}^{n}\right)\right) & =\sum_{t \in T} p_{1}^{n}(t) \frac{L}{n}+\sum_{s \in S} p_{1}^{n}(s) \frac{L^{\prime}}{n} \\
& \leq \mathbb{P}\left(X_{1}^{n} \in T\right) \frac{H(p)+\epsilon}{\log _{2} D}+\mathbb{P}\left(X_{1}^{n} \in S\right) \frac{\log _{2}|E|}{\log _{2} D}+o(1)
\end{aligned}
\]

qui tend vers \(\frac{H(p)+\epsilon}{\log _{2} D}\) quand \(n\) tend vers l'infini.

\subsection*{5.4 Equirépartition asymptotique pour les processus stationnaires}
On va étendre aux processus aléatoire stationnaires le théorème d'équirépartition asymptotique. On n'ira pas jusqu'au bout de la preuve, assez difficile, mais on établira la traduction du problème dans le langage des systèmes dynamiques. Cela nous conduira naturellement à deux pierres angulaires de cette théorie, la notion d'ergodicité et le Théorème Ergodique de Birkhoff.

Comme au paragraphe 5.1, étant donné un processus aléatoire \(\Xi\), on s'intéresse à la probabilité de sortie d'une suite \(\left(x_{1}, \ldots, x_{n}\right) \in E^{n}, p_{1}^{n}\left(x_{1}, \ldots, x_{n}\right)\), vue comme variable aléatoire. Autrement dit, \(p_{1}^{n}\) est une fonction sur \(E^{n}\), et on considère la variable aléatoire \(p_{1}^{n}\left(X_{1}, \ldots, X_{n}\right)\), qu'on peut noter \(p_{1}^{n}\left(X_{1}^{n}\right)\). On vise le théorème suivant.

Théorème 12 (Shannon, McMillan, Breiman) Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{Z}}\) un processus stationnaire d'entropie par symbole \(H(\Xi)\). Alors

\[
\lim _{n \rightarrow \infty}-\frac{1}{n} \log _{2} p_{1}^{n}\left(X_{1}^{n}\right)=H(\Xi) \quad \text { presque sûrement. }
\]

On renvoie au Chapitre 16.8 du livre de Cover et Thomas. On va présenter quelques ingrédients de la preuve.

\subsection*{5.5 Schéma de la preuve}
L'espérance de la variable \(-\log _{2} p_{1}^{n}\left(X_{1}^{n}\right)\) est l'entropie jointe \(H\left(X_{1}^{n}\right)\). Il s'agit donc de montrer qu'une variable aléatoire se concentre autour de son espérance. Comme dans la preuve de l'équirépartition asymptotique pour les suites iid, on voudrait utiliser la loi des grands nombres. Pour faire apparaitre une moyenne de variables, on introduit les lois conditionnelles

\[
p\left(x_{i} \mid x_{1}^{i-1}\right)=\mathbb{P}\left(X_{i}=x_{i} \mid X_{i-1}=x_{i-1}, \ldots, X_{1}=x_{1}\right),
\]

(vue ici comme une simple fonction sur \(E^{i+1}\) ) et on écrit que

\[
p_{1}^{n}\left(x_{1}^{n}\right)=p\left(x_{i}\right) \prod_{i=2}^{n} p\left(x_{i} \mid x_{1}^{i-1}\right)
\]

d'où

\[
p_{1}^{n}\left(X_{1}^{n}\right)=p\left(X_{i}\right) \prod_{i=2}^{n} p\left(X_{i} \mid X_{1}^{i-1}\right)
\]

Les variables \(-\log _{2} p\left(X_{i} \mid X_{1}^{i-1}\right)\) n'étant pas indépendantes, la loi des grands nombres ordinaire (Proposition 78) ne s'applique pas. La forme la plus générale de la loi des grands nombres, c'est le Théorème Ergodique de Birkhoff. Il se formule en termes de transformation préservant une mesure de probabilité. On va maintenant expliquer le lien entre processus stationnaires et transformations préservant une mesure de probabilité.

Fin du cours \(\mathrm{n}^{0} 7\)

\subsection*{5.6 Processus stationnaires et transformations préservant une mesure de probabilité}
Proposition 72 Soit \(\Xi=\left(\left(X_{n}\right)_{n \in \mathbb{Z}}\right)\) un processus aléatoire stationnaire à valeurs dans un ensemble fini \(E\). Sur l'ensemble \(E^{\mathbb{Z}}\) des suites bi-infinies d'éléments de \(E\), muni de la tribu \(\mathcal{B}\) engendrée par les cylindres, il existe une unique mesure de probabilité \(\mu\) possédant les propriétés suivantes.

\begin{itemize}
  \item \(\mu\) est invariante par le décalage \(T:\left(x_{n}\right)_{n \in \mathbb{Z}} \mapsto\left(y_{n}\right)_{n \in \mathbb{Z}}\), où \(y_{n}=x_{n+1}\).
  \item Le processus \(\Upsilon\) formé par les fonctions coordonnées \(Y_{m}\left(\left(x_{n}\right)_{n \in \mathbb{Z}}\right)=x_{m}\) a les mêmes lois jointes que \(\Xi\).\\
Réciproquement,\\
Autrement dit, on peut supposer que \(X_{n}\) est la \(n\)-ème fonction coordonnée sur \(E^{\mathbb{Z}}\), ce qu'on fera dans la suite.
\end{itemize}

Preuve Etant donnés \(m \leq n \in \mathbb{Z}\) et \(z_{m}^{n} \in E^{n-m+1}\), on appelle cylindre l'ensemble

\[
C_{z_{m}^{n}}=\left\{\left(x_{n}\right)_{n \in \mathbb{Z}} ; x_{m}=z_{m}, \ldots, x_{n}=z_{n}\right\}
\]

La mesure \(\mu\) doit satisfaire

\[
\mu\left(C_{z_{m}^{n}}\right)=\mathbb{P}\left(X_{m}=z_{m}, \ldots, X_{n}=z_{n}\right)
\]

Comme les cylindres engendrent la tribu \(\mathcal{B}, \mu\) est uniquement déterminée par ces conditions. Réciproquement, ces conditions définissent une fonction \(T\)-invariante sur l'ensemble des cylindres. Le fait que cette fonction se prolonge en une mesure résulte d'un principe général dû à Kolmogorov.

Exemple 73 Soient \(X_{n}, n \in \mathbb{Z}\), des variables indépendantes et de même loi \(p\). Alors \(\mu=p_{\mathbb{Z}}\) est la mesure produit.

Exemple 74 Soit \(X_{n}, n \in \mathbb{Z}\), une chaîne de Markov stationnaire, de loi \(p\) et de matrice de probabilités de transition. Alors \(\mu\) est caractérisée par

\[
\mu\left(C_{z_{m}^{n}}\right)=p\left(z_{m}\right) P_{z_{m} z_{m+1}} \cdots P_{z_{n-1} z_{n}}
\]

Suggestion d'exercice : \(\mathrm{n}^{0} 2\), feuille 4 .

\subsection*{5.7 Le Théorème Ergodique de Birkhoff}
Voici le substitut annoncé de la loi des grands nombres.\\
Théorème 13 (Birkhoff 1931) Soit ( \(\Omega, \mathcal{B}, \mu\) ) un espace probabilisé. Soit \(T: \Omega \rightarrow \Omega\) une bijection qui préserve la mesure. Soit \(f: \Omega \rightarrow \mathbb{R}\) une fonction intégrable. Alors la limite

\[
\bar{f}(x)=\lim _{n \rightarrow+\infty} \frac{1}{n}\left(f(x)+f(T(x))+\cdots+f\left(T^{n-1}(x)\right)\right)
\]

existe \(\mu\)-presque partout. La fonction \(\bar{f}\) est intégrable, son espérance est égale à celle de \(f\). Enfin, \(\bar{f} \circ T=\bar{f}\) presque partout.

Preuve Voir le livre de Petersen, pages 27 à 33.\\
Lorsque \(\Omega=E^{\mathbb{Z}}, \mathcal{B}\) est la tribu produit et \(\mu=p^{\mathbb{Z}}\) est la mesure produit, les coordonnées \(X_{n}\) sont des variables aléatoires indépendantes et de même loi, le Théorème 13 affirme que les sommes \(\frac{1}{n} \sum_{i=1}^{n} f\left(X_{i}\right)\) convergent presque sûrement vers une fonction \(T\)-invariante \(\bar{f}\). Il faudrait encore montrer que \(\bar{f}\) est presque partout constante. C'est vrai pour les suites de variables indépendantes, mais pas pour tous les processus stationnaires, comme on va le voir.

\subsection*{5.8 Ergodicité}
Définition 75 On dit qu'une transformation préservant la mesure \(T: \Omega \rightarrow \Omega\) est ergodique si, pour tout ensemble mesurable \(A \in \mathcal{B}\),

\[
T(A)=A \quad \Rightarrow \quad \mu(A)(1-\mu(A))=0
\]

En particulier, une fonction invariante coïncide avec son espérance presque partout. Cela entraîne que les sommes de Birkhoff convergent presque partout vers l'espérance de \(f\).

Proposition 76 (Ergodicité des chaînes de Markov) Soit \(\Xi\) une chaîne de Markov stationnaire et irréductible. Alors le décalage \(T: E^{\mathbb{Z}} \rightarrow E^{\mathbb{Z}}\) est ergodique pour la mesure \(\mu\). La réciproque est vraie : l'ergodicité de \(T\) entraîne que la chaîne est irréductible, pourvu que la mesure stationnaire charge tous les points de E.

Preuve Soit, pour \(x \in E, f_{x}\) la fonction caractéristique de l'ensemble \(C_{x}\) des suites \(\left(x_{n}\right)_{n \in \mathbb{Z}}\) telle \(x_{0}=x\). Le théorème ergodique de Birkhoff affirme que la suite de fonctions \(\frac{1}{n} \sum_{i=1}^{n} f_{x} \circ T^{i}\) converge presque partout. Par convergence dominée, pour tous \(x, y \in E\), la limite

\[
q_{y x}=\frac{1}{\mathbb{E}\left(f_{y}\right)} \lim _{n \rightarrow+\infty} \mathbb{E}\left(\frac{1}{n} \sum_{i=1}^{n}\left(f_{x} \circ T^{i}\right) f_{y}\right)
\]

existe. Or

\[
\begin{aligned}
\frac{1}{\mathbb{E}\left(f_{y}\right)} \mathbb{E}\left(\left(f_{x} \circ T^{i}\right) f_{y}\right) & =\frac{1}{p(y)} \mathbb{P}\left(X_{0}=y \text { et } X_{i}=x\right) \\
& =\mathbb{P}\left(X_{i}=x \mid X_{0}=y\right)=P_{y x}^{i}
\end{aligned}
\]

(ici, \(P^{i}\) désigne la puissance \(i\)-ème de la matrice des probabilités de transition \(P\) ), donc

\[
\begin{aligned}
q_{x y} & =\lim _{n \rightarrow+\infty} \frac{1}{n} \sum_{i=1}^{n} \mathbb{E}\left(\left(f_{x} \circ T^{i}\right) f_{y}\right) \\
& =\lim _{n \rightarrow+\infty} \frac{1}{n} \sum_{i=1}^{n} P_{y x}^{i}
\end{aligned}
\]

Autrement dit, la limite

\[
Q=\lim _{n \rightarrow+\infty} \frac{1}{n} \sum_{i=1}^{n} P^{i}
\]

existe (on pourrait aussi le démontrer directement par l'algèbre linéaire). Chaque ligne \(q\) de \(Q\) est une distribution de probabilité et satisfait \(q P=q\). Par unicité de la distribution stationnaire, \(q=p\), donc

\[
\lim _{n \rightarrow+\infty} \frac{1}{n} \sum_{i=1}^{n} \mathbb{E}\left(\left(f_{x} \circ T^{i}\right) f_{y}\right)=\mathbb{E}\left(f_{x}\right) \mathbb{E}\left(f_{y}\right)
\]

Plus généralement, soit \(f_{z_{r}^{r+\ell}}\) la fonction caractéristique de l'ensemble \(C_{z_{r}^{r+\ell}}\) des suites \(\left(x_{n}\right)_{n \in \mathbb{Z}}\) telles \(x_{r}=z_{r}, \ldots, x_{r+\ell}=z_{r+\ell}\). Alors, dès que \(i+r>s+m\),

\[
\begin{aligned}
\mathbb{E}\left(\left(f_{z_{r}^{r+\ell}} \circ T^{i}\right) f_{z_{s}^{s+m}}\right) & =\mathbb{P}\left(X_{s}^{s+m}=z_{s}^{s+m} \text { et } X_{r+i}^{r+i+\ell}=z_{r+i}^{r+i+\ell}\right) \\
& =p\left(z_{s}\right) P_{z_{s} z_{s+1}} \cdots P_{z_{m-1} z_{m}} P_{z_{m} z_{r+i}}^{i-m} P_{z_{r+i} z_{r+i+1}} \cdots P_{z_{r+i+\ell-1} z_{r+i+\ell}}
\end{aligned}
\]

donc

\[
\begin{aligned}
\lim _{n \rightarrow+\infty} \frac{1}{n} \sum_{i=s+m+1}^{n} \mathbb{E}\left(\left(f_{z_{r}^{r+\ell}} \circ T^{i}\right) f_{z_{s}^{s+m}}\right)= & p\left(z_{s}\right) P_{z_{s} z_{s+1}} \cdots P_{z_{z+m-1} z_{m}} \\
& Q_{z_{m} z_{r+i}} P_{z_{r+i} z_{r+i+1}} \cdots P_{z_{r+i+\ell-1} z_{r+i+\ell}} \\
= & \mathbb{P}\left(X_{s}^{s+m}=z_{s}^{s+m}\right) p\left(z_{r}\right) P_{z_{r+i} z_{r+i+1}} \cdots P_{z_{r+i+\ell-1} z_{r+i+\ell}} \\
= & \mathbb{P}\left(X_{s}^{s+m}=z_{s}^{s+m}\right) \mathbb{P}\left(X_{r}^{r+\ell}=z_{r}^{r+\ell}\right) \\
= & \mathbb{E}\left(f_{z_{r}^{r+\ell}}\right) \mathbb{E}\left(f_{z_{s}^{s+m}}\right)
\end{aligned}
\]

Ajouter le terme \(\sum_{i=1}^{s+m} \mathbb{E}\left(\left(f_{z_{r}^{r+\ell}} \circ T^{i}\right) f_{z_{s}^{s+m}}\right)\) ne change pas la limite. L'espace vectoriel engendré par la famille de fonctions \(f_{z_{s}^{s+m}}\) étant dense dans \(L^{2}\left(E^{\mathbb{Z}}, \mu\right)\), on en déduit que pour toutes les fonctions \(f\) et \(g \in L^{2}\left(E^{\mathbb{Z}}, \mu\right)\),

\[
\lim _{n \rightarrow+\infty} \frac{1}{n} \sum_{i=1}^{n} \mathbb{E}\left(\left(f \circ T^{i}\right) g\right)=\mathbb{E}(f) \mathbb{E}(g)
\]

On peut appliquer ceci à la fonction caractéristique \(f=g=1_{A}\) d'un ensemble invariant \(A\). Il vient \(\mu(A)=\mathbb{E}\left(1_{A}^{2}\right)=\mathbb{E}\left(1_{A}\right) \mathbb{E}\left(1_{A}\right)=\mu(A)^{2}\), d'où \(\mu(A)=0\) ou 1 . On conclut que \(T\) est ergodique.

Exemple 77 Soit \(\Xi\) un processus indépendant du temps, i.e. pour tout \(n \in \mathbb{Z}, X_{n}=X_{1}\). Alors \(\Xi\) est une chaîne de Markov dont la matrice des probabilités de transition est l'identité. Si \(X_{1}\) n'est pas presque partout constante, la transformation correspondante sur \(E^{\mathbb{Z}}\) n'est pas ergodique.

En effet, la mesure \(\mu\) ne charge que les suites constantes. Si \(X_{1}\) n'est pas presque partout constante, il existe \(x \in E\) tel que \(0<\mathbb{P}\left(X_{1}=x\right)<1\). La suite constante ( \(\ldots, x, x, \ldots\) ) est un ensemble invariant qui a pour mesure \(\mathbb{P}\left(X_{1}=x\right)\).

Suggestion d'exercice : \(\mathrm{n}^{0} 3\), feuille 4 .\\
Suggestion d'exercice : \(\mathrm{n}^{0} 4\), feuille 4.

\subsection*{5.9 Preuve du Théorème 12, cas des chaînes de Markov stationnaires irréductibles}
Dans ce cas, \(-\log _{2} p\left(x_{i} \mid x_{1}^{i-1}\right)=-\log _{2} p\left(x_{i} \mid x_{i-1}\right)=f \circ T^{i-1}\left(\left(x_{n}\right)_{n \in \mathbb{Z}}\right)\) où la fonction \(f: E^{\mathbb{Z}} \rightarrow\) \(\mathbb{R}\) est définie par

\[
f\left(\left(x_{n}\right)_{n \in \mathbb{Z}}\right)=-\log _{2} p\left(x_{1} \mid x_{0}\right)
\]

Par conséquent, la quantité qui nous intéresse est une somme de Birkhoff de \(f\), à un terme tendant vers 0 près,

\[
-\frac{1}{n} \log _{2} p_{1}^{n}\left(x_{1}^{n}\right)=\frac{1}{n} \sum_{i=1}^{n-1} f \circ T^{i}\left(\left(x_{n}\right)_{n \in \mathbb{Z}}\right)+\frac{1}{n} \log _{2} p_{1}\left(x_{1}\right)
\]

Le Théorème ergodique de Birkhoff affirme que \(-\frac{1}{n} \log _{2} p_{1}^{n}\left(x_{1}^{n}\right)\) converge presque sûrement. Si la transformation \(T\) est ergodique, la limite est l'espérance \(H(\Xi)\). Ceci achève la démonstration, dans le cas particulier des chaînes de Markov stationnaires ergodiques. Or, d'après la Proposition 76, elles le sont si elles sont irréductibles.

Fin du cours \(\mathrm{n}^{0} 8\)

\subsection*{5.10 Cas général}
Dans le cas d'un processus stationnaire général, la quantité qui nous intéresse n'est pas exactement une somme de Birkhoff. On l'encadre par deux variantes qui sont des sommes de Birkhoff. On pose\\
\(f_{k}\left(\left(x_{n}\right)_{n \in \mathbb{Z}}\right)=-\log _{2} p\left(x_{0} \mid x_{-1}, x_{-2}, \ldots, x_{-k}\right), \quad f_{\infty}\left(\left(x_{n}\right)_{n \in \mathbb{Z}}\right)=-\log _{2} p\left(x_{0} \mid x_{-1}, x_{-2}, \ldots, x_{-k}, \ldots\right)\).\\
Alors

\[
\begin{aligned}
\frac{1}{n} \sum_{i=0}^{n-1} f_{\infty} \circ T^{i} & =-\frac{1}{n} \log _{2} \prod_{i=0}^{n-1} p\left(x_{i} \mid x_{i-1}, x_{i-2}, \ldots\right) \\
& =-\frac{1}{n} \log _{2} p\left(x_{0}^{n-1} \mid x_{-\infty}^{-1}\right)
\end{aligned}
\]

Le Théorème ergodique de Birkhoff affirme donc que \(-\frac{1}{n} \log _{2} p\left(X_{0}^{n-1} \mid X_{-\infty}^{-1}\right)\) converge presque sûrement vers \(\mathbb{E}\left(f_{\infty}\right)\). Par analogie, on note

\[
p^{k}\left(x_{0}^{n-1}\right)=p\left(x_{0}^{k-1}\right) \prod_{i=k}^{n-1} p\left(x_{i} \mid x_{i-k}^{i-1}\right)
\]

de sorte que \(-\frac{1}{n} \log _{2} p^{k}\left(X_{0}^{n-1}\right)\) converge presque sûrement vers \(\mathbb{E}\left(f_{k}\right)\) quand \(n\) tend vers \(+\infty\).

D'après le Théorème 10,

\[
\begin{aligned}
\mathbb{E}\left(f_{k}\right) & =\mathbb{E}\left(-\log _{2} p\left(X_{0} \mid X_{-1}, \ldots, X_{-k}\right)\right) \\
& =\mathbb{E}\left(-\log _{2} p\left(X_{k} \mid X_{k-1}, \ldots, X_{0}\right)\right) \\
& =H\left(X_{k} \mid X_{k-1}, \ldots, X_{0}\right)
\end{aligned}
\]

tend vers \(H(\Xi)\) quand \(k\) tend vers \(+\infty\). Comme \(f_{\infty}=\lim _{k \rightarrow+\infty} f_{k}\) (cela résulte du théorème de convergence des martingales), le théorème de convergence dominée entraîne que \(\mathbb{E}\left(f_{\infty}\right)=H(\Xi)\). Donc les deux quantités ci-dessus ont la même limite presque sûre.

On montre enfin que pour tout \(k\),

\[
p^{k}\left(x_{0}^{n-1}\right) \leq p\left(x_{0}^{n-1}\right) \leq p\left(x_{0}^{n-1} \mid x_{-\infty}^{-1}\right)
\]

à des termes tendant vers 0 près. En faisant tendre en plus \(k\) vers \(+\infty\), on conclut que \(-\frac{1}{n} \log _{2} p\left(X_{0}^{n-1}\right)\) converge presque sûrement vers \(H(\Xi)\).

\subsection*{5.11 Appendice : preuve d'une forme faible de la loi des grands nombres}
Proposition 78 Soit \(\Xi=\left(X_{n}\right)_{n \in \mathbb{N}}\) une suite de variables aléatoires indépendantes, de même loi, bornées. Alors la suite \(S_{n}=\frac{1}{n} \sum_{i=1}^{n} X_{i}\) converge presque sûrement vers la constante \(\mathbb{E}\left(X_{0}\right)\).\\
Preuve Quitte à retrancher à \(X_{n}\) son espérance, on peut supposer que \(\mathbb{E}\left(X_{n}\right)=0\). Comme les \(X_{i}\) sont bornées, \(\left|X_{i}\right| \leq M\), on peut étudier l'espérance de produits comme \(\mathbb{E}\left(X_{i_{1}} X_{i_{2}} X_{i_{3}} X_{i_{4}}\right)\). Si l'un des indices, disons \(i_{1}\), n'apparait qu'une fois, alors, par indépendance,

\[
\mathbb{E}\left(X_{i_{1}} X_{i_{2}} X_{i_{3}} X_{i_{4}}\right)=\mathbb{E}\left(X_{i_{1}}\right) \mathbb{E}\left(X_{i_{2}} X_{i_{3}} X_{i_{4}}\right)=0
\]

On développe

\[
\begin{aligned}
\mathbb{E}\left(\left|S_{n}\right|^{4}\right) & =\frac{1}{n^{4}} \sum_{i_{1}, i_{2}, i_{3}, i_{4}} \mathbb{E}\left(X_{i_{1}} X_{i_{2}} X_{i_{3}} X_{i_{4}}\right) \\
& =\frac{1}{n^{4}}\left(\sum_{i} \mathbb{E}\left(X_{i}^{2}\right)+3 \sum_{i_{1}<i_{2}} \mathbb{E}\left(X_{i_{1}}^{2} X_{i_{2}}^{2}\right)\right. \\
& \leq \frac{1}{n^{4}}\left(3 n^{2}-2 n\right) M^{4} \sim \frac{3 M^{4}}{n^{2}}
\end{aligned}
\]

La série d'intégrales \(\sum_{n=1}^{\infty} \mathbb{E}\left(\left|S_{n}\right|^{4}\right)\) converge. Par conséquent, la somme de la série \(\sum_{n=1}^{\infty}\left|S_{n}\right|^{4}\) est intégrable (Théorème de convergence monotone), et donc presque sûrement finie. Cela entraîne que \(S_{n}\) tend vers 0 presque sûrement.\\
Proposition 79 La convergence presque sûre implique la convergence en probabilité. Autrement dit, si une suite variables aléatoires \(X_{n}\) tend vers 0 presque sûrement, alors pour tout \(\epsilon>0\), la probabilité \(\mathbb{P}\left(\left|X_{n}\right|>\epsilon\right)\) tend vers 0 quand \(n\) tend vers \(+\infty\).\\
Preuve Soit \(Y_{n}=\min \left\{\left|X_{n}\right|, 1\right\}\). Cette suite de fonctions converge presque partout vers 0. Elle est majorée par la constante 1, qui est intégrable. Par convergence dominée, \(\mathbb{E}\left(Y_{n}\right)\) tend vers 0 . Par l'inégalité de Bienaymé-Tchebycheff, \(\mathbb{P}\left(\left|X_{n}\right|>\epsilon\right)=\mathbb{P}\left(\left|Y_{n}\right|>\epsilon\right) \leq \frac{1}{\epsilon} \mathbb{E}\left(Y_{n}\right)\) tend vers 0 pour tout \(\epsilon>0\).

\subsection*{5.12 A retenir}
\begin{itemize}
  \item L'équipartition asymptotique, et l'interprétation de l'entropie et de l'information mutuelle en termes de suites typiques.
  \item Le lien entre processus stationnaires et systèmes dynamiques.
  \item Le Théorème ergodique de Birkhoff, substitut à la loi des grands nombres.
  \item La notion d'ergodicité.
\end{itemize}

Il s'agit d'un chapitre théorique, où on passe progressivement de considérations élémentaires sur des probabilités finies à un niveau d'abstraction plus élevé, avec des espaces non discrets, des tribus, et des théorèmes de convergence délicats.

\section*{6 Capacité d'un canal de transmission}
On cherche à faire passer des textes au travers d'un canal de transmission imparfait : pour chaque lettre envoyée, il y a une probabilité non nulle que la lettre reçue soit différente. Par un codage judicieux, on peut diminuer cette probabilité d'erreur. Par exemple, en répétant chaque lettre 3 fois, la probabilité d'erreur est élevée au carré, elle devient négligeable. Le prix à payer est un allongement du message, ce que l'on souhaite éviter. Quel est le meilleur compromis entre risque d'erreur et longueur de la transmission?

\subsection*{6.1 Modélisation d'un canal de transmission}
Le canal prend des caractères pris dans un ensemble fini \(E_{X}\) et retourne des caractères pris dans un autre ensemble \(E_{Y}\). On se limite aux canaux sans mémoire, i.e. tels que la probabilité d'observer un caractère \(y\) en sortie ne dépend que du caractère \(x\) arrivé en entrée. Le canal est donc entièrement décrit par la matrice de probabilités de transition \(P_{x y}=p(y \mid x)\). Si \(X\) est une variable aléatoire à valeurs dans \(E_{X}\), le canal produit à partir de \(X\) mis en entrée une variable aléatoire \(Y\) à valeurs dans \(E_{Y}\). La loi du couple ( \(X, Y\) ) est donnée par

\[
\mathbb{P}(Y=y \text { et } X=x)=\mathbb{P}(Y=y \mid X=x) P(X=x)=P_{x y} p_{X}(x)
\]

Définition 80 La capacité \(\kappa\) d'un canal est la borne supérieure des informations mutuelles \(I(X ; Y)\) sur toutes les variables d'entrée \(X\).

Exemple 81 Canal binaire symétrique : il reçoit et renvoie des bits, chaque bit est renversé avec probabilité \(\alpha\). Alors \(\kappa=1-h(\alpha)\).\\
En effet, la matrice des probabilités de transition est \(\left(\begin{array}{cc}1-\alpha & \alpha \\ \alpha & 1-\alpha\end{array}\right)\). Pour toute variable \(X\),

\[
H(Y \mid X)=\sum_{x \in E_{X}} p_{X}(x) H(Y \mid X=x)=h(\alpha),
\]

d'où

\[
I(X ; Y)=H(Y)-H(Y \mid X)=H(Y)-h(\alpha) \leq 1-h(\alpha),
\]

car \(Y\) ne prend que deux valeurs, et l'égalité a lieu si la loi de \(X\) est uniforme.\\
Suggestion d'exercice : \(\mathrm{n}^{0} 1\), feuille 5.

Fin du cours \(\mathrm{n}^{0} 9\)

Exemple 82 Canal effaceur : il reçoit des bits et renvoie des bits ou bien la mention "illisible". Chaque bit est rendu illisible avec probabilité \(\alpha\). Alors \(\kappa=1-\alpha\).\\
En effet, la matrice des probabilités de transition est \(\left(\begin{array}{ccc}1-\alpha & 0 & \alpha \\ 0 & 1-\alpha & \alpha\end{array}\right)\). A nouveau, pour toute variable \(X, H(Y \mid X)=h(\alpha)\), d'où \(I(X ; Y) \leq \log _{2} 3-h(\alpha)\), mais le cas d'égalité n'est jamais réalisé. Il faut trouver une meilleure majoration de \(H(Y)\). Soit \(Z\) la variable aléatoire qui vaut 1 quand \(Y\) est illisible, 0 sinon. Alors \(Z\) est une fonction de \(Y\) donc

\[
\begin{aligned}
H(Y) & =H(Y)+H(Z \mid Y)=H(Y, Z)=H(Z)+H(Y \mid Z) \\
& =h(\alpha)+\mathbb{P}(Z=1) H(Y \mid Z=1)+\mathbb{P}(Z=0) H(Y \mid Z=0) \\
& =h(\alpha)+(1-\alpha) H(X) \leq h(\alpha)+1-\alpha,
\end{aligned}
\]

avec égalité si la loi de \(X\) est uniforme. Il vient

\[
I(X ; Y)=H(Y)-H(Y \mid X) \leq 1-\alpha
\]

atteint pour \(X\) uniformément distribué, et \(\kappa=1-\alpha\).

Remarque 83 Dans cet exemple, il est intuitivement clair qu'une fraction \(\alpha\) de l'information est irrémédiablement perdue.

Exemple 84 Machine à écrire défectueuse : quand on frappe sur une touche, la lettre imprimée est ou bien celle voulue ou bien la suivante dans l'ordre alphabétique (cyclique), avec égale probabilité. Alors \(\kappa=\log _{2} 13\).

La matrice des probabilités de transition a des \(1 / 2\) sur la diagonale et sur la diagonale qui se trouve juste en dessous. L'entropie relative \(H(Y \mid X)=h\left(\frac{1}{2}\right)=1\). Par conséquent, \(I(X ; Y)=\) \(H(Y)-H(Y \mid X) \leq \log _{2}(26)-1=\log _{2}(13)\), atteint lorsque la loi de \(X\) est uniforme.

Suggestion d'exercice : \(\mathrm{n}^{0} 2\), feuille 5 .\\
Suggestion d'exercice : \(\mathrm{n}^{0} 3\), feuille 5 .

\subsection*{6.2 Modélisation du codage et du décodage}
En amont du canal, il y a un dispositif déterministe qui code le texte original, écrit dans un alphabet \(A\), de sorte qu'il puisse être digéré par le canal. Chaque lettre \(a \in A\) est codée par une chaîne de \(n\) caractères \(C(a) \in E_{X}^{n}\). En aval, il y a un dispositif déterministe qui reconstitue des lettres \(g\left(y_{1}, \ldots, y_{n}\right) \in A\) à partir de ce qui sort du canal. L'entier \(n\) est appelé la longueur des blocs. Ce qui nous intéresse, c'est la probabilité maximale d'erreur \(\max _{a \in A} \lambda_{a}\), où \(\lambda_{a}\) est la probabilité conditionnelle d'erreur sur la lettre \(a\),

\[
\lambda_{a}=\mathbb{P}(g(\text { sortie }) \neq a \mid \text { entrée }=C(a)) .
\]

Elle dépend à la fois du canal, du codeur et du décodeur. L'efficacité d'un codeur/décodeur se mesure à son taux \(\frac{\log _{2}|A|}{n}\). L'unité est le bit par transmission.

Exemple 85 On transmet des bits au travers d'un canal binaire symétrique de probabilité d'erreur \(\alpha\). On considère le dispositif qui, en amont du canal, répète chaque bit 3 fois et en aval, choisit dans chaque suite de 3 bits celui qui a la majorité. Son taux vaut \(\frac{1}{3}\) bits par transmission. Sa probabilité maximale d'erreur vaut \(3 \alpha^{2}-2 \alpha^{3}\).

En effet, il y a erreur lorsque le canal a changé au moins deux des trois bits identiques qui constituent le codage d'une lettre. Ceci se produit avec probabilité \(3 \alpha^{2}(1-\alpha)+\alpha^{3}=3 \alpha^{2}-2 \alpha^{3}\).

Définition 86 On dit qu'un canal autorise un taux de transmission \(\tau\) s'il existe une suite de codeurs/décodeurs de taux tendant vers \(\tau\) et dont la probabilité maximale d'erreur tend vers 0 .

Exemple 87 Machine à écrire défectueuse : Il est aisé de voir qu'on peut transmettre 13 lettres sans aucune erreur (il suffit de ne transmettre que les lettres de numéros impairs : \(A, C, E, \ldots\) ), ce qui donne un taux de transmission au moins égal à \(\log _{2}(13)\) bits par transmission.

Ici, \(A\) est l'ensemble des 13 lettres de numéros impairs, et la longueur des bloc vaut 1 . Le codage est l'injection de \(A\) dans l'alphabet à 26 lettres. Le décodage consiste à remplacer toute lettre lue en sortie par la lettre de numéro impair immédiatement inférieure. La probabilité maximale d'erreur vaut 0 . Le taux vaut \(\log _{2}(13)\).

\subsection*{6.3 Le théorème du codage de canal}
Dans l'exemple précédent, le dispositif de codage/décodage est optimal : il réalise le taux de transmission maximal, en vertu du théorème suivant.

Théorème 14 (Shannon, 1948) Le taux de transmission maximal autorisé par un canal sans mémoire est égal à sa capacité \(\kappa\). Autrement dit,

\begin{enumerate}
  \item Pour toute suite de codeurs/décodeurs dont la probabilité maximale d'erreur tend vers 0 , la limite supérieure des taux est \(\leq \kappa\).
  \item Il existe une suite de codeurs/décodeurs dont le taux tend vers \(\kappa\) et dont la probabilité maximale d'erreur tend vers 0 .
\end{enumerate}

Idée de la preuve. Soit un dispositif codeur/décodeur utilisant des blocs de longueur \(n\) assez grande. Notons \(X_{1}^{n}\) la variable mise en entrée du canal, après codage d'une variable uniforme, et \(Y_{1}^{n}\) la variable lue en sortie. Supposons, pour simplifier, que la probabilité maximale d'erreur est nulle. Alors \(\log _{2}|A|=H\left(X_{1}^{n}\right)=H\left(X_{1}^{n} \mid Y_{1}^{n}\right)+I\left(X_{1}^{n} ; Y_{1}^{n}\right)=I\left(X_{1}^{n} ; Y_{1}^{n}\right) \leq n I(X ; Y) \leq n \kappa\), car dans ce cas, \(X_{1}^{n}\) est une fonction de \(Y_{1}^{n}\). L'argument s'étend au cas général (prise en compte d'une faible probabilité d'erreur).

Réciproquement, on montre que par un codage judicieux, on peut forcer le canal, ou plutôt, son extension aux blocs de longueur \(n\), à se comporter comme la machine à écrire défectueuse, où il y a un grand sous-ensemble de blocs de longueur \(n\) dont les images possibles en sortie constituent des sous-ensembles presque disjoints de \(E_{Y}^{n}\). Ces blocs, ce sont les suites typiques.

D'après la propriété d'équirépartition asymptotique, pour chaque bloc de \(E_{X}^{n}\), il y a environ \(2^{n H(Y \mid X)}\) suites typiques dans \(E_{Y}^{n}\), d'égale probabilité. Or le nombre total de suites typiques dans \(E_{Y}^{n}\) est \(2^{n H(Y)}\). Donc le nombre de blocs de \(E_{X}^{n}\) qui donnent des ensembles disjoints en sortie est au plus \(2^{n(H(Y)-H(Y \mid X))}=2^{n I(X ; Y)}\). Cette borne est atteinte par un codage tiré au hasard. Autrement dit, on peut transmettre \(2^{n I(X ; Y)}\) blocs distincts, ce qui correspond à un taux de transmission \(\geq I(X ; Y)\) bits par transmission, lorsqu'à l'entrée arrive un texte tiré selon \(X\).

Fin du cours \(\mathrm{n}^{0} 10\)

\subsection*{6.4 Deux inégalités}
D'abord, quand un canal doit transmettre des blocs de longueur \(n\), sa capacité est au pire multipliée par \(n\), i.e., sa capacité "par symbole" n'augmente pas.

Lemme 88 Soient \(E_{X}\) et \(E_{Y}\) les alphabets d'entrée et de sortie d'un canal sans mémoire de capacité \(\kappa\). Soit \(X_{1}^{n}\) une variable aléatoire à valeurs dans \(E_{X}^{n}\). On fait passer successivement les composantes de \(X_{1}^{n}\) à travers le canal et on obtient une variable \(Y_{1}^{n}\) à valeurs dans \(E_{Y}^{n}\). Alors

\[
I\left(X_{1}^{n} ; Y_{1}^{n}\right) \leq \sum_{i=1}^{n} I\left(X_{i} ; Y_{i}\right) \leq n \kappa
\]

Preuve

\[
\begin{aligned}
I\left(X_{1}^{n} ; Y_{1}^{n}\right) & =H\left(Y_{1}^{n}\right)-H\left(Y_{1}^{n} \mid X_{1}^{n}\right) \\
& =H\left(Y_{1}^{n}\right)-H\left(Y_{1} \mid X_{1}^{n}\right)-\sum_{i=2}^{n} H\left(Y_{i} \mid Y_{1}, \ldots, Y_{i-1}, X_{1}^{n}\right) \\
& =H\left(Y_{1}^{n}\right)-\sum_{i=1}^{n} H\left(Y_{i} \mid X_{i}\right)
\end{aligned}
\]

car \(Y_{i}\) ne dépend que de \(X_{i}\) et, conditionnellement à \(X_{1}^{n}, Y_{i}\) est indépendant des \(Y_{j}, j \neq i\). Il vient

\[
\begin{aligned}
I\left(X_{1}^{n} ; Y_{1}^{n}\right) & \leq \sum_{i=1}^{n} H\left(Y_{i}\right)-\sum_{i=1}^{n} H\left(Y_{i} \mid X_{i}\right) \\
& =\sum_{i=1}^{n} H\left(Y_{i}\right)-H\left(Y_{i} \mid X_{i}\right)=\sum_{i=1}^{n} I\left(Y_{i} ; X_{i}\right) \leq n \kappa
\end{aligned}
\]

par définition de la capacité.

Ensuite, une propriété des chaînes de Markov.\\
Lemme 89 Soient \(X, Y\) et \(Z\) des variables aléatoires. On suppose que le processus ( \(X, Y, Z\) ) est une chaîne de Markov. Alors

\[
I(X ; Z) \leq I(Y ; Z) \quad \text { et } \quad I(X ; Z) \leq I(X ; Y)
\]

Preuve D'après la propriété de Markov, \(X\) est conditionnellement indépendant de \(Z\) sachant \(Y\), i.e.

\[
\mathbb{P}(X=x \mid Y=y, Z=z)=\mathbb{P}(X=x \mid Y=y)
\]

Autrement dit,

\[
\frac{p_{(X, Y, Z)}(x, y, z)}{p_{(Y, Z)}(y, z)}=\frac{p_{(X, Y)(x, y)}}{p_{Y}(y)}
\]

ce qui, par définition de l'information mutuelle conditionnelle, entraîne que \(I(X ; Z \mid Y)=0\). D'après la Proposition 34,

\[
I(X ; Y, Z)=I(X ; Y)+I(X ; Z \mid Y)=I(X ; Y)
\]

d'où, en appliquant une seconde fois la Proposition 34,

\[
I(X ; Y)=I(X ; Y, Z)=I(X ; Z)+I(X ; Y \mid Z)) \geq I(X ; Z)
\]

Comme \(I(Z ; X \mid Y)=I(X ; Z \mid Y)=0\), on peut échanger \(X\) et \(Z\), et conclure que \(I(Z ; Y) \geq I(X ; Z)\).

Suggestion d'exercice : \(\mathrm{n}^{0} 4\), feuille 5.

\subsection*{6.5 Inégalité de Fano}
Pour prendre en compte une probabilité d'erreur non nulle mais faible, on utilise une majoration de l'entropie conditionnelle entre une variable et une valeur approchée de cette variable en fonction de la probabilité d'erreur. Il s'agit de l'inégalité de Fano.

Proposition 90 (Fano, 1952) Soient \(W\) et \(\hat{W}\) deux variables aléatoires à valeurs dans un ensemble fini \(E_{W}\). Soit \(p_{e}=\mathbb{P}(W \neq \hat{W})\). Alors

\[
H(W \mid \hat{W}) \leq h\left(p_{e}\right)+p_{e} \log _{2}\left|E_{W}\right|
\]

Preuve Soit \(Z\) la variable aléatoire qui vaut 1 si \(W \neq \hat{W}\) et 0 sinon. \(Z\) est une fonction de ( \(W, \hat{W}\) ), donc \(H(Z \mid W, \hat{W})=0\). En appliquant de deux façons différentes le Corollaire 23, il vient

\[
\begin{aligned}
H(W \mid \hat{W}) & =H(W \mid \hat{W})+H(Z \mid W, \hat{W}) \\
& =H(Z, W \mid \hat{W}) \\
& =H(Z \mid \hat{W})+H(W \mid Z, \hat{W})
\end{aligned}
\]

D'après le principe "conditionner diminue l'entropie" (Proposition 28), \(H(Z \mid \hat{W}) \leq H(Z)=h\left(p_{e}\right)\). Sachant que \(Z=0, W=\hat{W}\), donc \(H(W \mid \hat{W}, Z=0)=0\). Sachant que \(Z=1, H(W \mid \hat{W}, Z=1) \leq\) \(H(W) \leq \log _{2}\left|E_{W}\right|\). Par définition de l'entropie conditionnelle,

\[
H(W \mid Z, \hat{W})=\mathbb{P}(Z=0) H(W \mid \hat{W}, Z=0)+\mathbb{P}(Z=1) H(W \mid \hat{W}, Z=1) \leq p_{e} \log _{2}\left|E_{W}\right|
\]

On conclut que \(H(W \mid \hat{W}) \leq h\left(p_{e}\right)+p_{e} \log _{2}\left|E_{W}\right|\).

\subsection*{6.6 Preuve du Théorème 14, sens direct}
Supposons donné un canal de capacité \(\kappa\) et un dispositif de codage \(C: A \rightarrow E_{X}^{n}\) et de décodage \(g: E_{Y}^{n} \rightarrow A\), de taux \(\tau\), utilisant des blocs de taille \(n\), pour ce canal. Soit \(p_{e}\) sa probabilité maximale d'erreur. Soit \(W\) une variable aléatoire de loi uniforme dans \(A\). Soit \(X_{1}^{n}=C(W)\) la variable mise en entrée du canal, soit \(Y_{1}^{n}\) la variable observée en sortie, soit \(\hat{W}=g(W)\) le résultat du décodage. Alors

\[
\begin{aligned}
\mathbb{P}(W \neq \hat{W}) & =\sum_{a \in A} \mathbb{P}(W=a) \mathbb{P}(\hat{W} \neq a \mid W=a) \\
& =\sum_{a \in A} \mathbb{P}(W=a) \mathbb{P}\left(g\left(Y_{1}^{n}\right) \neq a \mid X_{1}^{n}=C(a)\right) \\
& =\sum_{a \in A} \mathbb{P}(W=a) \lambda_{a} \\
& \leq \max _{a \in A} \lambda_{a}=p_{e}
\end{aligned}
\]

Le processus ( \(W, X_{1}^{n}, Y_{1}^{n}, \hat{W}\) ) est une chaîne de Markov. En effet, \(X_{1}^{n}\) dépend de façon déterministe de \(W, Y_{1}^{n}\) dépend de façon markovienne de \(X_{1}^{n}\) (car le canal est sans mémoire), et \(\hat{W}\) dépend de façon déterministe de \(Y_{1}^{n}\). D'après le Lemme 89,

\[
I(W ; \hat{W}) \leq I\left(W ; Y_{1}^{n}\right) \leq I\left(X_{1}^{n} ; Y_{1}^{n}\right)
\]

D'après le Lemme 88,

\[
I\left(X_{1}^{n} ; Y_{1}^{n}\right) \leq n \kappa
\]

L'inégalité de Fano donne

\[
\begin{aligned}
& H(W \mid \hat{W}) \leq h\left(p_{e}\right)+p_{e} \log _{2}|A| \\
& H(W)=H(W \mid \hat{W})+I(W ; \hat{W}) \\
& \leq h\left(p_{e}\right)+p_{e} \log _{2}|A|+I\left(X_{1}^{n} ; Y_{1}^{n}\right) \\
& \leq 1+p_{e} \log _{2}|A|+n \kappa
\end{aligned}
\]

En choisissant \(W\) uniformément réparti dans \(A\), on trouve

\[
\tau \leq \kappa+\frac{1}{n}+p_{e} \tau
\]

Lorsque la taille des blocs tend vers l'infini et la probabilité maximale d'erreur tend vers 0 , on trouve asymptotiquement \(\tau \leq \kappa\).

工 Fin du cours \(\mathrm{n}^{0} 11\)

\subsection*{6.7 Schéma de la preuve du Théorème 14, sens réciproque}
On montre que si \(\tau<\kappa\), il existe un codeur/décodeur de taux de transmission \(>\tau\).\\
Le décodage est fondé sur la notion de suite conjointement typique. Soit \(C: A \rightarrow E_{X}^{n}\) le codeur. Avec forte probabilité, le dispositif codeur/canal produit des suites conjointement typiques pour le couple ( \(X, Y\) ). Si on observe \(y_{1}^{n}\) en sortie, l'entrée \(x_{1}^{n}\) doit être telle que le couple ( \(x_{1}^{n}, y_{1}^{n}\) ) est conjointement typique. Le décodage \(g\left(y_{1}^{n}\right)\) doit être un \(a \in A\) tel que ( \(C(a), x_{1}^{n}\) ) est une suite conjointement typique. Si \(|A|<2^{n \tau}\), avec forte probabilité, cet élément \(a\) existe et est unique.

Le codeur est tiré au hasard. On montre que l'espérance, parmi les choix aléatoires de \(C\), de la probabilité maximale d'erreur du dispositif codeur/décodeur, est faible. Il existe donc un codeur dont la probabilité maximale d'erreur est faible. C'est une illustration de ce qu'on appelle parfois la méthode probabiliste en combinatoire.

\subsection*{6.8 Majoration de la probabilité d'erreur}
Par hypothèse, il existe une distribution \(p_{X}\) sur \(E_{X}\) (et donc une variable aléatoire \(X\) et la variable de sortie \(Y\) correspondante) telle que \(I(X ; Y)>\tau\). Soit \(A\) un ensemble à \(2^{n \tau}\) éléments. On interprète le fait de tirer indépendamment \(2^{n \tau}\) éléments de \(E_{X}^{n}\) selon la loi produit \(p_{X}^{\otimes n}\) comme une application aléatoire \(C: A \rightarrow E_{X}^{n}\). Autrement dit, chaque lettre de \(C(a), a \in A\), est tirée indépendamment selon \(p_{X}\), ce qu'on note

\[
\mathbb{P}_{C}\left(C(a)=x_{1}^{n}\right)=p_{X}\left(x_{1}\right) \cdots p_{X}\left(x_{n}\right)
\]

car l'aléa provient du choix de l'application \(C\). De plus les différentes variables \(C(a), a \in A\), sont indépendantes.

Le procédé de décodage retenu repose sur la notion de suite conjointement typique pour le couple ( \(X, Y\) ). On fixe \(\epsilon<\frac{1}{4}(I(X ; Y)-\tau)\). Soit \(W\) une variable uniformément répartie dans \(A\), soit \(\tilde{X}_{1_{\sim}}^{n}=C(W)\) le signal mis en entrée du canal, et \(\tilde{Y}_{1}^{n}\) la variable observée en sortie. Le décodage \(a=g\left(\tilde{Y}_{1}^{n}\right)\) est l'élément \(a \in A\) tel que \(\left(C(a), \tilde{Y}_{1}^{n}\right) \in T\). Il y a deux sources d'erreurs de décodage.

\begin{enumerate}
  \item Il n'existe aucun \(a \in A\) tel que ( \(C(a), \tilde{Y}_{1}^{n}\) ) est conjointement typique. Même \(a=\underset{\sim}{W}\) ne convient pas. Donc la probabilité de cet évènement est majorée par celle de \(\mathcal{E}=\left\{\left(\tilde{X}_{1}^{n}, \tilde{Y}_{1}^{n}\right) \notin\right.\) \(T)\}\).
  \item Il existe plusieurs \(a \in A\) tels que \(\left(C(a), \tilde{Y}_{1}^{n}\right) \in T\). Notons cet évènement \(\mathcal{F}\).
\end{enumerate}

Fixons \(a_{0} \in A\) et raisonnons conditionnellement à l'évènement \(\left\{\underset{\sim}{W}=a_{0}\right\}\). L'aléa restant provient du tirage au hasard du codeur \(C\). Alors les composantes de \(\tilde{X}_{1}^{n}\) sont (conditionnellement) indépendantes, de loi \(p_{X}\), et, comme le canal est sans mémoire, les composantes de \(\tilde{Y}_{1}^{n}\) sont (conditionnellement) indépendantes, de loi \(p_{Y}\). Chacun des couples ( \(\tilde{X}_{i}, \tilde{Y}_{i}\) ) a même loi que ( \(X, Y\) ). D'après la Proposition 70, point 1,

\[
\mathbb{P}_{C}\left(\mathcal{E} \mid W=a_{0}\right)=\mathbb{P}\left((X, Y)_{1}^{n} \notin T\right)
\]

tend vers 0 , donc est \(<\epsilon\) pour \(n\) assez grand.\\
Pour \(a \neq a_{0}\), les variables conditionnées \(\tilde{X}_{1}^{n} \mid W=a\) et \(\tilde{X}_{1}^{n} \mid W=a_{0}\) sont indépendantes. Comme le canal est sans mémoire, il en est de même de \(\tilde{Y}_{1}^{n} \mid W=a\) et \(\tilde{X}_{1}^{n} \mid W=a_{0}\). Donc pour tout \(a \neq a_{0}\), la Proposition 70, point 3, donne

\[
\mathbb{P}_{C}\left(\left(\tilde{X}|W=a, \tilde{Y}| W=a_{0}\right)_{1}^{n} \in T\right) \leq 2^{-n(I(X ; Y)-3 \epsilon)} \leq 2^{-n(\tau+\epsilon)}
\]

En sommant sur les \(a \neq a_{0}\),

\[
\mathbb{P}\left(\mathcal{F} \mid W=a_{0}\right) \leq 2^{n \tau} \mathbb{P}\left((\tilde{X}, \tilde{Y})_{1}^{n} \in T\right) \leq 2^{-n(I(X ; Y)-\tau-3 \epsilon)} \leq 2^{-n \epsilon}<\epsilon
\]

pour \(n\) assez grand. En faisant la moyenne sur les tirages \(a_{0} \in A\) de \(W\), il vient

\[
\mathbb{E}_{W}\left(\mathbb{P}_{C}(\mathcal{E} \cup \mathcal{F})\right)<2 \epsilon
\]

pour \(n\) assez grand. On écrit cela \(\mathbb{E}_{W}\left(\mathbb{E}_{C}\left(1_{\mathcal{E} \cup \mathcal{F}}\right)\right)<2 \epsilon\), ou, avec Fubini, \(\mathbb{E}_{C}\left(\mathbb{E}_{W}\left(1_{\mathcal{E} \cup \mathcal{F}}\right)\right)<2 \epsilon\)\\
Il existe donc un codage \(C\) qui rend la probabilité moyenne d'erreur \(\mathbb{E}_{W}\left(1_{\mathcal{E} \cup \mathcal{F})}\right)<2 \epsilon\). On fixe un tel \(C\) désormais. Remarquer que

\[
\mathbb{E}\left(\lambda_{W}\right)=2^{-n \tau} \sum_{a \in A} \lambda_{a}=\mathbb{E}_{W}(\mathbb{P}(\mathcal{E} \cup \mathcal{F}))<2 \epsilon
\]

On ordonne les éléments de \(A\) par probabilité d'erreur \(\lambda_{a}\) croissante, et on note \(A^{\prime} \subset A\) la première moitié de \(A\) pour cette ordre. Alors

\[
p_{e}=\max _{a \in A^{\prime}} \lambda_{a}<4 \epsilon
\]

En effet, \(p_{e} \leq \min _{a \notin A^{\prime}} \lambda_{a}\), d'où

\[
|A| \mathbb{E}\left(\lambda_{W}\right)=\sum_{a \in A^{\prime}} \lambda_{a}+\sum_{a \notin A^{\prime}} \lambda_{a} \geq\left|A \backslash A^{\prime}\right| p_{e}=\frac{1}{2}|A| p_{e}
\]

On modifie le codage \(C\) choisi en remplaçant \(A\) par \(A^{\prime}\) et \(C\) par sa restriction \(C^{\prime}\) à \(A^{\prime}\). Pour le codage \(C^{\prime}\), la probabilité maximale d'erreur est \(p_{e}<4 \epsilon\). D'autre part, le taux de transmission de \(C^{\prime}\) est \(\frac{\log \left|A^{\prime}\right|}{n}=\tau-\frac{1}{n}\). On faisant tendre \(n\) vers l'infini, on conclut que \(\tau\) constitue un taux de transmission autorisé par le canal. Ceci achève la preuve du Théorème 14.

\subsection*{6.9 Postérité}
Le Théorème du codage de canal contient tous les ingrédients pour attirer l'attention :

\begin{itemize}
  \item Un problème concret, d'intérêt industriel.
  \item Une modélisation simple, un résultat d'impossibilité théorique qui, pour une fois, est réaliste.
  \item Une preuve lumineuse, basée sur des idées heuristiques simples, mais un peu délicate à mettre au point.
  \item Une preuve probabiliste d'existence, qui ne fournit aucune piste pour construire des codeurs/décodeurs réels, et constitue donc un défi.\\
Le théorème est à l'origine d'innombrables travaux qui ont produit des codeurs/décodeurs de plus en plus performants. Il ne suffit pas que le taux de transmission soit élevé. Il faut aussi des algorithmes peu coûteux de codage et de décodage. L'algèbre sur les corps finis a été mis à contribution. Les turbo-codes, inventés par des ingénieurs de l'Ecole Nationale Supérieure des Télécommunications, à Lannion, se sont imposés comme solution technologique. Ils équipent aussi bien les missions spatiales de la NASA et de l'Agence Spatiale Européenne que les lecteurs de DVD. Ils constituent aussi une excellente solution théorique : ils réalisent presque la borne du Théorème du codage de canal sur le taux de transmission autorisé pour de vastes classes de canaux.
\end{itemize}

\subsection*{6.10 A retenir}
\begin{itemize}
  \item L'interprétation de l'information mutuelle comme borne sur le taux de transmission autorisé par un canal.
  \item Le calcul de la capacité dans quelques exemples.
\end{itemize}

Il s'agit d'un chapitre pratique, où on utilise les outils du chapitre précédent dans un cas simple (indépendance) pour résoudre un problème concret.

\section*{\(7 \quad\) Entropie métrique}
Un processus stationnaire à valeurs dans un ensemble fini n'est qu'un exemple de transformation préservant une mesure de probabilité. L'entropie par symbole, définie pour l'instant seulement pour les processus stationnaires à valeurs dans des ensembles finis, s'étend à cette situation plus vaste.

Reprenons nos deux exemples favoris de processus stationnaires, et traduisons les en termes de dynamique sur un espace probabilisé.

Exemple 91 (Décalage de Bernoulli) Soit \(p\) une distribution de probabilité sur un ensemble fini \(E\). Le décalage sur \(E^{\mathbb{Z}}\) muni de la tribu produit et de la mesure produit \(\mu=p^{\otimes \mathbb{Z}}\) est appelé décalage de Bernoulli \(\mathcal{B}(p)\) (Bernoulli shift en anglais).

La tribu produit est engendrée par les cylindres de la forme

\[
C_{z_{m}^{n}}=\left\{\text { suites }\left(x_{n}\right)_{n \in \mathbb{N}} \text { telles que } x_{m}=z_{m}, \ldots, x_{n}=z_{n}\right\} .
\]

La mesure produit donne au cylindre \(C_{z_{m}^{n}}\) la mesure \(\mu\left(C_{z_{m}^{n}}\right)=p\left(z_{m}\right) \cdots p\left(z_{n}\right)\).\\
Exemple 92 (Décalages de Markov) Soit \(p\) une distribution de probabilité sur un ensemble fini E. Soit \(P\) une matrice stochastique pour laquelle \(p\) est stationnaire, i.e. \(p P=p\). Le décalage de Markov \(\mathcal{M}(p, P)\) (Markov shift en anglais) est le décalage sur \(E^{\mathbb{Z}}\) muni de la tribu produit et de la mesure \(\mu\) telle que

\[
\mu\left(C_{z_{m}^{n}}\right)=p\left(z_{m}\right) P_{z_{m} z_{m+1}} \cdots P_{z_{n-1} z_{n}}
\]

Evidemment, le décalage de Bernoulli est un cas particulier de décalage de Markov.

\subsection*{7.1 Entropie d'une transformation préservant la mesure}
Voici la démarche qui conduit à la généralisation. A une transformation \(T\) préservant une mesure de probabilité \(\mu\) sur un espace \(\Omega\) sont associés des processus stationnaires à valeurs dans des ensembles finis. Il suffit pour cela de choisir une partition finie \(\alpha=\left(A_{x}\right)_{x \in E_{\alpha}}\) de \(\Omega\), et de poser

\[
X_{n}(\omega)=x \text { si } T^{n}(\omega) \in A_{x}
\]

C'est bien un processus stationnaire : pour tous \(k \in \mathbb{N}\) et \(n \in \mathbb{Z}\), la loi jointe satisfait

\[
\begin{aligned}
\mathbb{P}\left(X_{n}^{n+k}=x_{0}^{k}\right) & =\mathbb{P}\left(T^{n}(\omega) \in A_{x_{0}}, \ldots, T^{n+k}(\omega) \in A_{x_{k}}\right) \\
& =\mu\left(T^{-n}\left(A_{x_{0}}\right) \cap \cdots \cap T^{-n-k} A_{x_{k}}\right) \\
& =\mu\left(T^{-n}\left(A_{x_{0}} \cap \cdots \cap T^{-k} A_{x_{k}}\right)\right) \\
& =\mu\left(A_{x_{0}} \cap \cdots \cap T^{-k} A_{x_{k}}\right) \\
& =\mathbb{P}\left(\omega \in A_{x_{0}}, \ldots, T^{k}(\omega) \in A_{x_{k}}\right) \\
& =\mathbb{P}\left(X_{0}^{k}=x_{0}^{k}\right)
\end{aligned}
\]

On note \(\Xi_{\alpha}\) ce processus. Il correspond à ce qu'on peut extraire de \(T\) à l'aide d'un appareil de mesure, qui ne possède forcément qu'un nombre fini de graduations. Pour affiner la mesure, il faut des appareils de plus en plus précis, donc des partitions de plus en plus fines. Cela conduit à la définition suivante.

Définition 93 Soit ( \(\Omega, \mathcal{B}, \mu\) ) un espace probabilisé. Soit \(T: \Omega \rightarrow \Omega\) une application qui préserve la mesure. Etant donné une partition mesurable finie \(\alpha=\left(A_{x}\right)_{x \in E_{\alpha}}\) de \(\Omega\), on note \(\Xi_{\alpha}\) le processus stationnaire correspondant. On appelle entropie métrique de \(T\) la borne supérieure

\[
h_{\mu}(T)=\sup _{\alpha} H\left(\Xi_{\alpha}\right)
\]

prise sur toutes les partitions mesurables finies de \(\Omega\).\\
On abrégera souvent entropie métrique en entropie, quand le contexte exclut toute confusion.

\subsection*{7.2 Entropie des partitions}
On peut s'affranchir de la notion de processus stationnaire dans la définition de l'entropie métrique. En effet, les lois jointes ne sont autres que les probabilités d'évènements engendrés par la transformation \(T\) à partir de la partition \(\alpha\).

Définition 94 Soient \(\alpha=\left(A_{x}\right)_{x \in E_{\alpha}}\) et \(\beta=\left(B_{y}\right)_{y \in E_{\beta}}\) deux partitions finies d'un espace probabilisé \(\Omega\), et \(T: \Omega \rightarrow \Omega\) une application qui préserve la mesure.

\begin{itemize}
  \item On note \(T \alpha\) la partition dont les pièces sont les \(\left(T^{-1} A_{x}\right)_{x \in E_{\alpha}}\).
  \item On dit que \(\beta\) raffine \(\alpha\), et on note \(\alpha \preceq \beta\), si chaque pièce de \(\beta\) est contenue dans une pièce de \(\alpha\).
  \item On note \(\alpha \vee \beta\) la partition dont les pièces sont les \(\left(A_{x} \cap B_{y}\right)_{(x, y) \in E_{\alpha} \times E_{\beta}}\). Cette opération est commutative et associative.
  \item On appelle entropie de la partition \(\alpha\) le nombre \(H(\alpha)=-\sum_{x \in E} \mu\left(A_{x}\right) \log _{2}\left(\mu\left(A_{x}\right)\right)\).
  \item On appelle entropie conditionnelle des partitions \(\alpha\) et \(\beta\) le nombre
\end{itemize}

\[
H(\alpha \mid \beta)=-\sum_{(x, y) \in E_{\beta}} \mu\left(A_{x} \cap B_{y}\right) \log _{2} \frac{\mu\left(A_{x} \cap B_{y}\right)}{\mu\left(B_{y}\right)}
\]

Lemme 95 L'entropie des partitions a les propriétés suivantes.\\
\(-0 \leq H(\alpha \mid \beta) \leq H(\alpha)\).\\
\(-H(\alpha \vee \beta)=H(\beta)+H(\alpha \mid \beta)\).\\
\(-H(\alpha \vee \beta) \leq H(\alpha)+H(\beta)\).\\
\(-\alpha \preceq \alpha^{\prime} \Rightarrow H(\alpha) \leq H\left(\alpha^{\prime}\right)\) et \(H(\alpha \mid \beta) \leq H\left(\alpha^{\prime} \mid \beta\right)\).

\begin{itemize}
  \item Si \(T: \Omega \rightarrow \Omega\) préserve la mesure, \(T(\alpha \vee \beta)=T(\alpha) \vee T(\beta), H(\alpha \mid \beta)=H(T \alpha \mid T \beta)\).
\end{itemize}

Preuve \(H(\alpha)=H\left(f_{\alpha}\right)\) où la variable aléatoire \(f_{\alpha}: \Omega \rightarrow E_{\alpha}\) vaut \(x\) sur \(A_{x}\).

Suggestion d'exercice : \(\mathrm{n}^{0} 1\), feuille 6 .

Proposition 96 Soit \(T: \Omega \rightarrow \Omega\) une application qui préserve la mesure. Pour toute partition mesurable finie \(\alpha\),

\[
H\left(\Xi_{\alpha}\right)=\lim _{n \rightarrow \infty} \frac{1}{n} H\left(\alpha \vee T \alpha \vee \cdots \vee T^{n-1} \alpha\right)
\]

Preuve Par construction, la variable \(X_{\alpha, n}\) du processus stationnaire \(\Xi_{\alpha}\) est \(f_{T^{n} \alpha}\). Par conséquent, l'entropie de la \(n\)-ème partition engendrée par \(T\) est égale à l'entropie jointe,

\[
H\left(\alpha \vee T \alpha \vee \cdots \vee T^{n-1} \alpha\right)=H\left(X_{\alpha, 1}, \ldots, X_{\alpha, n}\right)
\]

Exemple 97 (Décalage de Bernoulli) Soit \(T=\mathcal{B}(p)\) un décalage de Bernoulli sur \(E^{\mathbb{Z}}\). Soit \(\alpha\) la partition telle que \(A_{x}=\left\{\right.\) suites \(\left(x_{n}\right)_{n \in \mathbb{Z}}\) telles que \(\left.x_{0}=x\right\}\), i.e. la partition induite par la projection sur un facteur. Le processus \(\Xi_{\alpha}\) est une suite de variables indépendantes de loi \(p\), donc \(H\left(\Xi_{\alpha}\right)=H(p)\).

Exemple 98 (Décalage de Markov) Soit \(T=\mathcal{M}(p, P)\) un décalage de Markov sur \(E^{\mathbb{Z}}\). Soit \(\alpha\) la partition induite par la projection sur un facteur. Le processus \(\Xi_{\alpha}\) est une chaîne de Markov stationnaire de matrice de probabilités de transition \(P\) et de distribution stationnaire \(p\), donc \(H\left(\Xi_{\alpha}\right)=-\sum_{x, y \in E} p(x) P_{x y} \log _{2}\left(P_{x y}\right)\).

\subsection*{7.3 Partitions génératrices}
La définition de l'entropie métrique par une borne supérieure est une belle construction théorique, qui paraît parfaitement incalculable. Il n'en est rien. Il se trouve que les "bonnes" partitions \(\alpha\) donnent toutes la même valeur de \(H\left(\Xi_{\alpha}\right)\).

Notation 99 Soit \(\alpha\) une partition finie d'un ensemble \(\Omega\). On note \(\alpha_{m}^{n}=T^{m} \alpha \vee \cdots \vee T^{n} \alpha\). On note \(\alpha_{-\infty}^{n}\) la tribu engendrée par les pièces des partitions \(\alpha_{m}^{n}, m \leq n\). Et de même, \(\alpha_{-\infty}^{\infty}\) est la tribu engendrée par les pièces de toutes les partitions \(\alpha_{m}^{n}, m, n \in \mathbb{Z}\).

Suggestion d'exercice : \(\mathrm{n}^{0} 2\), feuille 6 .\\
Suggestion d'exercice : \(\mathrm{n}^{0} 3\), feuille 6 .

Exemple 100 (Tribu produit) Soit \(E\) un ensemble fini, \(\Omega=E^{\mathbb{Z}}, T=\) décalage, \(\alpha\) la partition \(A_{x}=\left\{\right.\) suites \(\left(x_{n}\right)_{n \in \mathbb{Z}}\) telles que \(\left.x_{0}=x\right\}\) induite par la projection sur un facteur. La tribu produit sur \(\Omega\) coüncide avec \(\alpha_{-\infty}^{\infty}\).

En effet, pour tout \(\left(z_{m}, \ldots, z_{n}\right) \in E^{n-m+1}\),

\[
C_{z_{1}^{n}}=T^{-m} A_{z_{m}} \cap \cdots \cap T^{-n} A_{z_{n}}
\]

donc \(\alpha_{m}^{n}\) coïncide avec l'ensemble des cylindres sur les coordonnées \(m\) à \(n\). On conclut par définition de la tribu produit.

Définition 101 Soit ( \(\Omega, \mathcal{B}, \mu\) ) un espace probabilisé. Soit \(T: \Omega \rightarrow \Omega\) une application qui préserve la mesure. On dit qu'une partition mesurable finie \(\alpha\) de \(\Omega\) est génératrice pour \(T\) si \(\mathcal{B}=\alpha_{-\infty}^{\infty}\) aux ensembles de mesure nulle près.

Exemple 102 Pour un décalage de Bernoulli ou, plus généralement, de Markov, la partition induite par la projection sur un facteur est génératrice.

Fin du cours \(\mathrm{n}^{0} 12\)

Le lemme suivant aide à comprendre ce que signifie \(\alpha_{-\infty}^{\infty}\), et par conséquent, la notion de partition génératrice.

Lemme 103 Soit ( \(\Omega, \mathcal{B}, \mu\) ) un espace probabilisé. Soit \(\beta_{0} \preceq \cdots \preceq \beta_{n} \preceq\) une suite de partitions mesurables finies de \(\Omega\), de plus en plus fines. Soit \(\beta_{\infty}\), la tribu engendrée par toutes les \(\beta_{n}, n \in \mathbb{N}\). Si \(C \in \beta_{\infty}\), alors pour tout \(\delta>0\), il existe \(n\) et un élément \(B\) de la tribu engendrée par \(\beta_{n}\) tel que \(\mu(B \Delta C)<\delta\).

Preuve Notons \(\beta_{\rightarrow}\) la famille de toutes les intersections \(B_{1} \cap \cdots B_{n} \cap \cdots\) où \(B_{i}\) appartient à la tribu engendrée par \(\beta_{i}\). Soit \(\mathcal{T}\) l'ensemble des réunions dénombrables d'éléments de \(\beta_{\rightarrow}\). On montre d'abord que \(\beta_{\infty}=\mathcal{T}\).

Par construction, \(T\) est stable par réunion dénombrable. Si \(A \in \beta_{\rightarrow}\), le complémentaire \(A^{c}\) est une réunion dénombrable de complémentaires \(B_{i}^{c}\). Chaque \(B_{i}^{c}\) appartient à la tribu engendrée par \(\beta_{i}\), donc à \(\beta_{\rightarrow}\). Par conséquent, \(A^{c} \in \mathcal{T}\), i.e. \(\mathcal{T}\) est stable par complémentaire. Cela prouve que \(\mathcal{T}\) est une tribu. Pour tout \(i\), elle contient la tribu engendrée par \(\beta_{i}\), donc \(\mathcal{T}\) contient \(\beta_{\infty}\). Réciproquement, comme \(\beta_{\infty}\) est stable par intersection dénombrable, \(\beta_{\rightarrow} \subset \beta_{\infty}\), et comme \(\beta_{\infty}\) est stable par réunion dénombrable, \(\mathcal{T} \subset \beta_{\infty}\). On conclut que \(\mathcal{T}=\beta_{\infty}\).

Soit \(C \in \beta_{\infty}\). On vient de montrer que \(C=\bigcup_{k \in \mathbb{N}} I_{k}\) où \(I_{k}\) est une intersection \(I_{k}=\bigcap_{i \in \mathbb{N}} B_{k, i}\) d'ensembles \(B_{k, i}\) appartenant à la tribu engendrée par \(\beta_{i}\). Il existe \(\ell\) tel que \(\mu\left(\bigcup_{k \geq \ell} I_{k}\right)<\frac{\delta}{2}\). Pour chaque \(k<\ell\), il existe \(i(k)\) tel que \(\mu\left(\bigcap_{i \leq i(k)} B_{k, i} \backslash I_{k}\right)<\frac{\delta}{2 \ell}\). Soit \(B=\bigcup_{k<\ell} \bigcap_{i \leq i(k)} B_{k, i}\). Alors \(\mu(B \Delta C)<\delta\) et \(B\) appartient à la tribu engendrée par \(\beta_{n}\), pour \(n=\max _{k<\ell} i(k)\).

\subsection*{7.4 Calcul de l'entropie métrique}
Théorème 15 (Kolmogorov, Sinai 1958) Soit ( \(\Omega, \mathcal{B}, \mu\) ) un espace probabilisé. Soit \(T: \Omega \rightarrow \Omega\) une application qui préserve la mesure. Soit \(\alpha\) une partition mesurable finie qui est génératrice pour T. Alors

\[
h_{\mu}(T)=H\left(\Xi_{\alpha}\right) .
\]

Preuve La preuve que voici, dûe à Yakov Sinai, procède en trois étapes.

\begin{enumerate}
  \item Soit \(\Phi\) l'ensemble des partitions mesurables finies de \(\Omega\) modulo ensembles de mesure nulle. Muni de la distance de l'entropie conditionnelle, \(\Phi\) est un espace métrique sur lequel la fonction \(\beta \mapsto H\left(\Xi_{\beta}\right)\) est continue.
  \item Soit \(\Psi \subset \Phi\) le sous-ensemble défini par
\end{enumerate}

\[
\Psi=\left\{\beta \in \Phi ; \exists k \text { tel que } \beta \preceq \alpha_{-k}^{k}\right\} .
\]

On montre que pour tout \(\beta \in \Psi, H\left(\Xi_{\beta}\right) \leq H\left(\Xi_{\alpha}\right)\).\\
3. Sous l'hypothèse que \(\alpha\) est génératrice, on montre que \(\Psi\) est dense dans \(\Phi\).

\begin{enumerate}
  \item Pour l'étape 1, on renvoie à l'Exercice 3 du TD6.
  \item Soit \(\beta\) une partition mesurable finie. Supposons là moins fine que la partition \(\alpha_{-k}^{k}\), i.e. \(\beta \preceq \alpha_{-k}^{k}\). Alors
\end{enumerate}

\[
\beta_{-n}^{n} \preceq\left(\alpha_{-k}^{k}\right)_{-n}^{n}=\alpha_{-n-k}^{n+k},
\]

(comparer à l'exercice 2 du TD6), d'où

\[
\begin{aligned}
\frac{1}{2 n+1} H\left(\beta_{1}^{2 n+1}\right) & =\frac{1}{2 n+1} H\left(\beta_{-n}^{n}\right) \\
& \leq \frac{1}{2 n+1} H\left(\alpha_{-n-k}^{n+k}\right)=\frac{2 n+2 k+1}{2 n+1} \frac{1}{2 n+2 k+1} H\left(\alpha_{1}^{2 n+2 k+1}\right)
\end{aligned}
\]

et, en faisant tendre \(n\) vers l'infini, \(H\left(\Xi_{\beta}\right) \leq H\left(\Xi_{\alpha}\right)\).\\
3. Soit \(\gamma \in \Phi\) une partition finie dont toutes les pièces ont une mesure non nulle. Par hypothèse, chaque pièce \(C_{z}, z \in E_{\gamma}\) de \(\gamma\) appartient à la tribu engendrée par les pièces de tous les \(\alpha_{-n}^{n}\), à un ensemble de mesure nulle près. D'après le Lemme 103, cela entraîne que pour tout \(\delta>0\), il existe \(n\) et pour tout \(z\), il existe un élément \(C_{z}^{\prime}\) de la tribu engendrée par \(\alpha_{-n}^{n}\) tel que \(\mu\left(C_{z} \Delta C_{z}^{\prime}\right)<\delta\). Soit \(\beta\) la partition engendrée par les \(C_{z}^{\prime}, z \in E_{\gamma}\). Les pièces sont toutes les intersections de \(C_{z}^{\prime}\). Pour chaque \(z\), il y a une grosse pièce, \(B_{z}=C_{z}^{\prime} \backslash \bigcup_{z^{\prime} \neq z^{\prime}} C_{z^{\prime}}^{\prime}\), et de petites pièces de mesure \(<\delta\), donc \(E_{\gamma} \subset E_{\beta}\). Les variables aléatoires \(f_{\gamma}\) et \(f_{\beta}\) ne diffèrent que sur un ensemble de probabilité \(p_{e}<\left|E_{\gamma}\right| \delta\). L'inégalité de Fano 90 entraîne que

\[
\rho(\beta, \gamma)=H(\gamma \mid \beta)+H(\beta \mid \gamma) \leq 2 h\left(p_{e}\right)+2 p_{e} \log _{2}\left|E_{\gamma}\right|
\]

On conclut qu'en choisissant \(\delta\) assez petit, on peut rendre \(\rho(\beta, \gamma)\) arbitrairement petit. Par construction, \(\beta \preceq \alpha_{-n}^{n}\) donc \(\beta \in \Psi\). Cela prouve la densité de \(\Psi\) dans \(\Phi\), et permet d'étendre par continuité l'inégalité \(H\left(\Xi_{\beta}\right) \leq H\left(\Xi_{\alpha}\right)\) prouvée pour \(\beta \in \Psi\) à \(\Phi\) tout entier.

On conclut que \(h_{\mu}(T)=\max _{\beta \in \Phi} H\left(\Xi_{\beta}\right)=H\left(\Xi_{\alpha}\right)\).\\
Corollaire 104 L'entropie du décalage de Bernoulli \(\mathcal{B}(p)\) vaut \(H(p)\).\\
L'entropie du décalage de Markov \(\mathcal{M}(p, P)\) vaut \(-\sum_{x, y \in E} p(x) P_{x y} \log _{2}\left(P_{x y}\right)\).

\subsection*{7.5 Rotations du cercle}
Il existe des transformations d'entropie nulle.\\
Proposition 105 Soit \(T: x \mapsto x+\theta\) une rotation d'angle irrationnel de \(\mathbb{R} / \mathbb{Z}\), et \(\lambda\) la mesure de Lebesgue. Alors \(h_{\lambda}(T)=0\).\\
Preuve On montre que la partition en 2 morceaux \(\alpha=\left\{\left[0, \frac{1}{2}\left[,\left[\frac{1}{2}, 1[ \}\right.\right.\right.\right.\) est génératrice. On utilise la densité des orbites (Exercice 6 du TD 4). Pour tout intervalle \([a, b]\) et tout \(\epsilon>0\), il existe des entiers \(n\) et \(m\) tels que \(-n \theta \in] a-\epsilon, a[,-m \theta \in] b, b+\epsilon[\). Alors l'intersection des intervalles \(T^{-n}\left[0, \frac{1}{2}\left[\cap T^{-m}\left[\frac{1}{2}, 1\left[=\left[-n \theta,-m \theta\left[\right.\right.\right.\right.\right.\right.\) est \([a, b]\). Ceci prouve que \([a, b] \in \alpha_{-\infty}^{\infty}\). Comme la tribu engendrée par les intervalles est la tribu borélienne \(\mathcal{B}, \alpha_{-\infty}^{\infty}=\mathcal{B}\), et \(\alpha\) est génératrice.

Le nombre de pièces dans la partition \(\alpha_{1}^{n}\) est \(2 n\). En effet, couper une partition selon deux points opposés ne divise que deux pièces de la partition, donc \(\left|\alpha_{1}^{n+1}\right|=\left|\alpha_{1}^{n}\right|+2\). Par conséquent, \(H\left(\alpha_{1}^{n}\right) \leq \log _{2}\left|\alpha_{1}^{n}\right|=\log _{2}(2 n)=o(n)\), donc \(H\left(\Xi_{\alpha}\right)=0\), et, avec le théorème \(15, h_{\lambda}(T)=0\).

\subsection*{7.6 Invariance de l'entropie}
Définition 106 Soient ( \(\Omega, \mathcal{B}, \mu\) ) et ( \(\Omega^{\prime}, \mathcal{B}^{\prime}, \mu^{\prime}\) ) des espaces probabilisés. On dit que deux transformations préservant la mesure \(T: \Omega \rightarrow \Omega\) et \(\Omega^{\prime} \rightarrow \Omega^{\prime}\) sont isomorphes ou conjuguées s'il existe une transformation préservant la mesure \(\phi: \Omega \rightarrow \Omega^{\prime}\) telle que \(T^{\prime} \circ \phi=\phi \circ T\) presque partout.

Exemple 107 Soit \(\phi:\{0,1\}^{\mathbb{Z}} \rightarrow[0,2] \times[0,1]\), définie pour \(z=\left(z_{n}\right)_{n \in \mathbb{Z}}\) par

\[
\phi(x)=\left(\sum_{n=0}^{\infty} z_{n} 2^{-n}, \sum_{n=-\infty}^{-1} z_{n} 2^{n}\right)
\]

Alors \(\phi\) conjugue le décalage à la transformation \(T^{\prime}:[0,2] \times[0,1] \rightarrow[0,2] \times[0,1]\), définie pour \(x \in[0,2]\) et \(y \in[0,1]\), par

\[
T^{\prime}(x, y)= \begin{cases}\left(2 x, \frac{1}{2} y\right) & \text { si } x \leq 1 \\ \left(2 x-2, \frac{1}{2}(1+y)\right) & \text { si } x>1\end{cases}
\]

Soit \(w=T(z)\), i.e. \(w_{n}=z_{n+1}\). Soit \((x, y)=\phi(z)\). Si \(z_{0}=0\), alors \(x \leq 1\). Si \(z_{0}=1\), alors \(x \geq 1\).

\[
\begin{aligned}
\phi(T(z)) & =\left(\sum_{n=0}^{\infty} z_{n+1} 2^{-n}, \sum_{n=-\infty}^{-1} z_{n+1} 2^{n}\right) \\
& =\left(\sum_{m=1}^{\infty} z_{m} 2^{-(m-1)}, \sum_{m=-\infty}^{0} z_{m} 2^{m-1}\right) \\
& =\left(-2 z_{0}+2 \sum_{m=0}^{\infty} z_{m} 2^{-m}, \frac{z_{0}}{2}+\frac{1}{2} \sum_{m=-\infty}^{-1} z_{m} 2^{m}\right) \\
& =\left(-2 z_{0}+2 x, \frac{z_{0}}{2}+\frac{1}{2} y\right) \\
& =T^{\prime}(x, y)=T^{\prime}(\phi(z))
\end{aligned}
\]

sauf peut-être lorsque \(x=1\). Enfin, soit \(\mu\) la mesure produit \(p^{\otimes \mathbb{Z}}\) où \(p\) est la distribution uniforme sur \(\{0,1\}\). Alors \(\phi_{*} \mu=\frac{1}{2} \lambda\) où \(\lambda\) désigne la mesure de Lebesgue sur \([0,2] \times[0,1]\). En effet, étant donnés \(m \leq 0 \leq n \in \mathbb{Z}\) et \(z_{m}, \ldots, z_{n} \in\{0,1\}\), prolongeons \(z\) à \(\mathbb{Z}\) par 0 et posons \(\left(x_{m}, y_{n}\right)=\phi(z)\). Le cylindre \(C_{z_{m}, \ldots, z_{n}}\), de mesure \(2^{-n+m-1}\), est envoyé sur le rectangle \(\left[x_{m}, x_{m}+2^{m}\right] \times\left[y_{n}, y_{n}+2^{-n}\right]\), de mesure de Lebesgue \(2^{-n+m}\).\\
\(T^{\prime}\) est parfois désignée sous le nom de transformation du boulanger, car elle rappelle le geste de l'artisan qui partage une boule de pâte en deux parts, en pose une sur l'autre et presse pour obtenir à nouveau une boule.

Proposition 108 Si deux transformations sont isomorphes, et si l'une est ergodique, il en est de même de l'autre.

Proposition 109 Deux transformations isomorphes ont même entropie.\\
En effet, un isomorphisme envoie classes de partitions modulo ensembles de mesure nulle sur classes de partitions modulo ensembles de mesure nulle, et, par naturalité, \(H\left(\Xi_{\phi(\alpha)}\right)=H\left(\Xi_{\alpha}\right)\), donc \(h_{\mu^{\prime}}\left(T^{\prime}\right)=h_{\mu}(T)\).

Corollaire 110 Deux décalages de Bernoulli, ou, plus généralement, de Markov, d'entropies différentes ne sont pas isomorphes.

Exemple 111 La transformation du boulanger, définie dans l'exemple 107, est ergodique et son entropie vaut 1 .

Nous ne démontrerons pas le théorème, assez difficile, suivant.\\
Théorème 16 (D. Ornstein 1970) Deux décalages de Bernoulli qui ont la même entropie sont isomorphes.

On dit que l'entropie est un invariant d'isomorphisme complet pour les décalages de Bernoulli. L'intérêt de cette classe est que de nombreux systèmes dynamiques d'intérêt courant sont isomorphes, du point de vue mesurable, à des décalages de Bernoulli.

Théorème 17 (Y. Katznelson, 1971) Soit \(A \in G l(n, \mathbb{Z})\) une matrice entière inversible. On suppose que les valeurs propres de \(A\) ne sont pas des racines de l'unité. Alors la transformation \(T_{A}\) induite sur le tore est conjuguée à un décalage de Bernoulli.

En revanche, une rotation du cercle n'est pas conjuguée à un décalage de Bernoulli, puisque son entropie est nulle.

\subsection*{7.7 Automorphismes du tore}
Le tore, c'est l'espace quotient \(\mathbb{R}^{n} / \mathbb{Z}^{n}\). C'est un groupe. On admet que ses automorphismes continus proviennent d'homomorphismes continus du groupe \(\mathbb{R}^{n}\) dans lui-même. Ceux-ci coïncident avec les applications linéaires de \(\mathbb{R}^{n}\) (cette étape n'est pas difficile). Une bijection linéaire \(A: \mathbb{R}^{n} \rightarrow\) \(\mathbb{R}^{n}\) passe au quotient si et seulement si \(A \mathbb{Z}^{n} \subset \mathbb{Z}^{n}\), i.e. si la matrice de \(A\) est à coefficients entiers. C'est une bijection du quotient si et seulement si \(A^{-1}\) est aussi à coefficients entiers, ce qui signifie que le déterminant de \(A\) vaut 1 ou -1 . Le groupe des matrices \(n \times n\) entières de déterminant \(\pm 1\) est noté \(G l(n, \mathbb{Z})\). La formule de changement de variable montre que les bijections linéaires associées à ces matrices préservent la mesure de Lebesgue de \(\mathbb{R}^{n}\). Elles induisent des bijections de \(\mathbb{R}^{n} / \mathbb{Z}^{n}\) qui préserve la mesure de probabilité naturelle sur le quotient, identifié au cube unité de \(\mathbb{R}^{n}\).

Proposition 112 Soit \(T\) l'automorphisme de \(X=\mathbb{R}^{n} / \mathbb{Z}^{n}\) associé à la matrice \(A \in G l(n, \mathbb{Z})\). Alors \(T\) est ergodique si et seulement si aucune des valeurs propres de \(A\) n'est une racine de l'unité.

Preuve On utilise le développement en série de Fourier des fonctions sur \(\mathbb{R}^{n} / \mathbb{Z}^{n}\). Si \(f \in L^{2}\left(\mathbb{R}^{n} / \mathbb{Z}^{n}\right)\), alors \(f\) est la somme de la série

\[
f=\sum_{\xi \in \mathbb{Z}^{n}} \hat{f}_{\xi} e_{\xi}
\]

où la famille de fonctions \(e_{\xi}(x)=e^{2 i \pi \xi \cdot x}\) est orthonormée pour le produit scalaire (hermitien) \(L^{2}\). On remarque que \(e_{\xi} \circ T=e_{A^{\top} \xi}\).

Si l'une des valeurs propres de \(A\) est une racine \(k\)-ème de l'unité, alors 1 est valeur propre de \(A^{k}\). Le système linéaire à coefficients entiers \(\left(A^{k}\right)^{\top} \xi=\xi\) possède une solution non nulle rationnelle, donc une solution non nulle entière \(\xi\). Alors la fonction \(e_{\xi}\) est \(T^{k}\)-invariante. La fonction

\[
f: X \rightarrow \mathbb{C}, \quad f=\sum_{j=1}^{k} e_{\xi} \circ T^{j}
\]

est \(T\)-invariante mais n'est pas presque partout constante (elle a des coefficients de Fourier non nuls). Cela prouve que \(T\) n'est pas ergodique.

Réciproquement, supposons qu'aucune valeur propre de \(A\) n'est une racine de l'unité. Alors pour tous vecteurs entiers non nuls \(\xi\) et \(\xi^{\prime}\), et tout \(k\) assez grand, \(\left(A^{\top}\right)^{k} \xi \neq \xi^{\prime}\). En effet, sinon, il existe \(k \neq k^{\prime}\) tels que \(\left(A^{\top}\right)^{k} \xi=\xi^{\prime}=\left(A^{\top}\right)^{k^{\prime}} \xi\). Alors \(\left(A^{\top}\right)^{k^{\prime}-k} \xi=\xi\), 1 est valeur propre d'une puissance de \(A^{\top}\), donc \(A\) a une valeur propre qui est une racine de l'unité, contradiction. Par conséquent, pour tout \(\xi, \xi^{\prime}\) et \(k\) assez grand, le produit scalaire \(\left\langle e_{\left(A^{\top}\right)^{k} \xi}, e_{\xi^{\prime}}\right\rangle=0\). En particulier,

\[
\lim _{k \rightarrow \infty}\left\langle e_{\xi} \circ T^{k}, e_{\xi^{\prime}}\right\rangle=0
\]

C'est évidemment encore vrai pour des combinaisons linéaires finies de fonctions \(e_{\xi}\). D'après la décomposition de Fourier, ce sous-espace vectoriel est dense dans \(L^{2}\left(\mathbb{R}^{n} / \mathbb{Z}^{n}\right)\). Comme dans la preuve de la Proposition 76 , on en déduit que pour tous \(f, g \in L^{2}\left(\mathbb{R}^{n} / \mathbb{Z}^{n}\right)\),

\[
\lim _{k \rightarrow \infty}\left\langle f \circ T^{k}, g\right\rangle=0
\]

et l'ergodicité de \(T\) en résulte.\\
Théorème 18 (Ya. Sinai, 1959) Soit \(A \in G l(n, \mathbb{Z})\) une matrice entière inversible. On suppose que les valeurs propres de \(A\) ne sont pas des racines de l'unité. Soient \(\left|\lambda_{1}\right| \geq \cdots \geq\left|\lambda_{n}\right|\) les modules des valeurs propres de \(A\). Soient \(\left|\lambda_{1}\right| \geq \cdots \geq\left|\lambda_{k}\right|\) ceux de ces nombres qui sont \(>1\). L'entropie métrique de la transformation \(T_{A}\) correspondante vaut

\[
h\left(T_{A}\right)=\sum_{i=1}^{k} \log _{2}\left|\lambda_{k}\right|
\]

Preuve On donne la preuve lorsque \(n=2\). Dans ce cas, \(T_{A}\) est ergodique si et seulement si ses valeurs propres sont réelles et distinctes (en effet, si elles sont non réelles, les valeurs propres sont conjuguées, de module 1 et de partie réelle entière ou demi-entière, donc des racines cubiques ou quatrièmes de l'unité). Les valeurs propres \(\lambda_{1}>\lambda_{2}=1 / \lambda_{1}\) sont irrationnelles.

On construit une partition finie \(\alpha\). Chaque pièce est l'image dans \(\mathbb{R}^{2} / \mathbb{Z}^{2}\) d'un parallélogramme dont les côtés sont parallèles aux deux espaces propres de \(A\). Pour la construire on part de deux longs segments de droites propres issus de l'origine dans \(\mathbb{R}^{2}\).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-49(1)}

Leurs images \(\gamma_{1}\) et \(\gamma_{2}\) dans le tore ont une extrémité commune, à l'origine.\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-49}

Si l'autre extrémité de \(\gamma_{1}\) n'est pas sur \(\gamma_{2}\), on prolonge \(\gamma_{1}\) jusqu'à ce qu'elle le soit. On fait de même avec \(\gamma_{2}\).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-49(3)}

L'image réciproque du réseau de lignes obtenu divise le plan en polygones qui ont un sommet rentrant aux points entiers.\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-49(2)}

Pour y remédier, on prolonge \(\gamma_{1}\) dans la direction opposée, au-delà de l'origine, jusqu'à ce que son extrémité soit sur \(\gamma_{2}\), et on fait de même pour \(\gamma_{2}\). De la sorte, on obtient un réseau de lignes sur le tore, qui définit une partition \(\alpha\) (à ensembles de mesure nulle près).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-50}

Cette fois, l'image réciproque du réseau dans le plan divise le plan en polygones localement convexes (donc convexes) à côtés parallèles aux droites propres, ce sont des parallélogrammes.\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-50(1)}

L'image réciproque par \(A^{n}\) d'un parallélogramme à côtés parallèles aux droites propres est un parallélogramme à côtés parallèles aux droites propres, mais \(\lambda_{1}^{-n}\) fois plus court dans la direction de \(\gamma_{1}\) et \(\lambda_{2}^{-n}\) fois plus long dans la direction de \(\gamma_{2}\). Soit \(L_{i}\) (resp. \(\ell_{i}\) ) la plus grande (resp. plus petite) longueur des côtés parallèles à \(\gamma_{i}\) des pièces de \(\alpha\). Alors les longueurs correspondantes dans la partition \(T_{A^{n}} \alpha\) sont comprises entre \(\ell_{i} \lambda_{i}^{-n}\) et \(L_{i} \lambda_{i}^{-n}\).\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-50(2)}

Les partitions \(T_{A} \alpha\) et \(\alpha \vee T_{A} \alpha\).\\
Notons \(\beta_{i}\) la réunion des lignes parallèles à \(\gamma_{i}\) (son image réciproque dans le plan est la réunion des images de \(\gamma_{1}\) par les translations entières). Comme \(\gamma_{1} \subset A^{-1}\left(\gamma_{1}\right)\) et \(A^{-1}\left(\gamma_{2}\right) \subset \gamma_{2}, \beta_{1} \subset\) \(A^{-1}\left(\beta_{1}\right)\) et \(A^{-1}\left(\beta_{2}\right) \subset \beta_{2}\). Par conséquent, dans la partition \(\alpha \vee T_{A} \alpha\), chaque parallélogramme est divisé en parallélogrammes de même longueur (dans la direction de \(\gamma_{1}\) ), mais de largeur (dans la direction de \(\gamma_{2}\) ) divisée par un facteur de l'ordre de \(\lambda_{1}\). De même, dans la partition \(\alpha \vee T_{A} \alpha \vee \cdots \vee\) \(\left(T_{A}\right)^{n-1} \alpha\), les pièces sont des parallélogrammes de longueurs (dans la direction de \(\gamma_{1}\) ) comprises entre \(\ell_{1}\) et \(L_{1}\) et de largeurs (dans la direction de \(\gamma_{2}\) ) comprises entre \(\ell_{2} \lambda_{2}^{-n}\) et \(L_{2} \lambda_{2}^{-n}\). Il en résulte que leurs aires sont comprises entre \(\ell_{1} \ell_{2} \lambda_{1}^{-n}\) et \(L_{1} L_{2} \lambda_{1}^{-n}\). L'entropie satisfait

\[
\begin{aligned}
H\left(\alpha \vee T_{A} \alpha \vee \cdots \vee\left(T_{A}\right)^{n-1} \alpha\right) & =\sum_{B}-\mu(B) \log 2 \mu(B) \\
& \geq \sum_{B}-\mu(B) \log 2\left(L_{1} L_{2} \lambda_{1}^{-n}\right) \\
& \geq n \log _{2}\left(\lambda_{1}\right)-\log _{2}\left(L_{1} L_{2}\right)
\end{aligned}
\]

De même,

\[
\begin{aligned}
H\left(\alpha \vee T_{A} \alpha \vee \cdots \vee\left(T_{A}\right)^{n-1} \alpha\right) & =\sum_{B}-\mu(B) \log 2 \mu(B) \\
& \leq \sum_{B}-\mu(B) \log 2\left(\ell_{1} \ell_{2} \lambda_{1}^{-n}\right) \\
& \leq n \log _{2}\left(\lambda_{1}\right)-\log _{2}\left(\ell_{1} \ell_{2}\right)
\end{aligned}
\]

On conclut que

\[
H\left(\Xi_{\alpha, T_{A}}\right)=\lim _{n \rightarrow \infty} \frac{1}{n} H\left(\alpha \vee T_{A} \alpha \vee \cdots \vee\left(T_{A}\right)^{n-1} \alpha\right)=\log _{2}\left(\lambda_{1}\right)
\]

Les pièces de la partition \(T^{-n} \alpha\) sont des parallélogrammes dont les côtés parallèles à \(\gamma_{1}\) ont des longueurs de l'ordre de \(\lambda_{2}^{n}\). Par conséquent, les pièces de la partition \(T^{-n} \alpha \vee T^{n} \alpha\) ont un diamètre qui tend vers 0 .\\
\includegraphics[max width=\textwidth, center]{2025_06_25_daf1217afebc4c634cb9g-51}

Soit \(\epsilon>0\), soit \(R\) un rectangle du tore. Pour \(n\) assez grand, il existe une réunion \(U\) de pièces de \(T^{-n} \alpha \vee T^{n} \alpha\) telle que \(\mu(R \Delta U)<\epsilon\). Cela prouve que la tribu \(\alpha_{-\infty}^{\infty}\) contient \(R\). Comme les rectangles engendrent la tribu borélienne du tore, \(\alpha\) est génératrice. D'après le Théorème 15,

\[
h_{\mu}\left(T_{A}\right)=H\left(\Xi_{\alpha, T_{A}}\right)=\log _{2}\left(\lambda_{1}\right)
\]

\subsection*{7.8 Chaos}
Ce terme n'a pas de définition mathématique précise. Il évoque la sensibilité aux conditions initiales: Un système dynamique est chaotique si deux points très voisins peuvent néanmoins avoir des trajectoires qui s'éloignent. Les automorphismes ergodiques du tore ont cette propriété. D'une certaine façon, l'entropie quantifie ce phénomène : étant donnée une partition \(\alpha\) arbitrairement fine, la transformation en fait des partitions qui recoupent \(\alpha\) suivant une multitude de pièces. Entropie \(h\) positive signifie qu'il y a en gros \(2^{n h}\) pièces de mesures à peu près égales.

\subsection*{7.9 A retenir}
\begin{itemize}
  \item Le lien entre processus stationnaires et systèmes dynamiques.
  \item La notion de conjugaison dans la catégorie des espaces probabilisés.
  \item La notion d'entropie métrique, invariant de conjugaison dans cete catégorie.
  \item Le lien entre entropie et chaos.
\end{itemize}

Il s'agit d'un chapitre théorique, où une nouvelle notion a été introduite, dans un autre champ des mathématiques, les systèmes dynamiques. Elle s'applique à certains des exemples les plus fascinants de systèmes dynamiques. Il y a un théorème dont la preuve est difficile, car elle s'appuie sur des manipulations de tribus auxquelles on est peu familier en M1.


\end{document}