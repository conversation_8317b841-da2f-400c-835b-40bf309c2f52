println("=== REPARATION DEPENDANCES PYCALL ===")

println("\n1. Reconstruction complete de PyCall...")
using Pkg

# Nettoyer et reconstruire PyCall
println("Nettoyage PyCall...")
try
    Pkg.rm("PyCall")
    println("OK: PyCall supprime")
catch e
    println("Info: PyCall n'etait pas installe ou erreur: ", e)
end

println("Reinstallation PyCall...")
Pkg.add("PyCall")
println("OK: PyCall reinstalle")

println("Reconstruction PyCall...")
Pkg.build("PyCall")
println("OK: PyCall reconstruit")

println("\n2. Test PyCall apres reparation...")
try
    using PyCall
    println("OK: PyCall charge avec succes")
    
    # Test fonction Python
    py"""
    def test_repair():
        return "PyCall repare avec succes"
    """
    result = py"test_repair()"
    println("OK: Test fonction: ", result)
    
catch e
    println("ERREUR PyCall apres reparation: ", e)
    
    # Si l'erreur persiste, essayer une approche différente
    println("\n3. Approche alternative - Configuration manuelle...")
    
    # Configurer manuellement le chemin Python
    ENV["PYTHON"] = raw"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"
    
    println("Reconstruction avec Python specifique...")
    Pkg.build("PyCall")
    
    try
        using PyCall
        println("OK: PyCall fonctionne avec configuration manuelle")
    catch e2
        println("ERREUR persistante: ", e2)
    end
end

println("\n=== FIN REPARATION ===")
