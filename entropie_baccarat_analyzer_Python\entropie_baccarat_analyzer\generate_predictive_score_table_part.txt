# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2454 à 2561
# Type: Méthode de la classe INDEX5PredictiveScoreTable

    def generate_predictive_score_table_part(self, sequence, evolution, analyzer, start_main, end_main, part_number, precomputed_differentials=None):
        """
        Génère une partie du tableau prédictif avec SCORES au lieu des différentiels
        CORRECTION : Utilise les différentiels pré-calculés pour synchronisation parfaite
        """
        if not sequence or not evolution:
            return "❌ Données insuffisantes pour générer le tableau prédictif"

        # Pour le premier tableau, commencer à la main 6 au lieu de la main 1
        if part_number == 1:
            actual_start = max(start_main, 6)  # Commencer à la main 6
        else:
            actual_start = start_main

        # Ajuster les limites selon la longueur de la séquence
        actual_end = min(end_main, len(sequence))
        if actual_start > len(sequence):
            return f"❌ Main {actual_start} dépasse la longueur de la séquence ({len(sequence)} mains)"

        # Créer les en-têtes avec séparateurs verticaux
        header_line1 = "               |"
        header_line2 = "               |"
        header_separator = "===============|"
        separator_line = "===============|"

        for main_num in range(actual_start, actual_end + 1):
            header_line1 += f"Main {main_num}".ljust(12) + "|"
            header_line2 += "SCORE".ljust(12) + "|"
            header_separator += "=" * 12 + "|"
            separator_line += "=" * 12 + "|"

        table = f"🎯 TABLEAU PRÉDICTIF - SCORES POUR LES 9 VALEURS INDEX5 POSSIBLES\n"
        table += f"📊 PARTIE {part_number} - MAINS {actual_start} À {actual_end}\n"
        table += separator_line + "\n"
        table += header_line1 + "\n"
        table += header_separator + "\n"
        table += header_line2 + "\n"
        table += separator_line + "\n"

        # CORRECTION : Utiliser directement les différentiels pré-calculés
        if not precomputed_differentials:
            return "❌ Aucun différentiel pré-calculé fourni pour la synchronisation"

        # CORRECTION : Extraire les données du cache avec la bonne structure
        # Le cache utilise des clés (tuple(sequence), position, id(analyzer))
        # Il faut les convertir en structure {position: data}
        all_predictive_diffs = {}
        for cache_key, cache_data in precomputed_differentials.items():
            if len(cache_key) == 3:  # (tuple(sequence), position, id(analyzer))
                position = cache_key[1]
                all_predictive_diffs[position] = cache_data

        # Générer les lignes pour chaque valeur INDEX5 possible avec SCORES
        for i, index5_value in enumerate(self.all_index5_values):
            line = index5_value.ljust(15)

            for position in range(actual_start - 1, actual_end):  # -1 car les indices commencent à 0
                line += "|"  # Séparateur vertical avant chaque main

                if position < len(sequence) and position in all_predictive_diffs:
                    predictive_diffs = all_predictive_diffs[position]

                    if index5_value in predictive_diffs:
                        diffs = predictive_diffs[index5_value]
                        # Calculer le SCORE au lieu d'afficher les différentiels
                        score = self.score_calculator.calculate_predictive_score(
                            diffs['DiffCond'],
                            diffs['DiffTaux'],
                            diffs['DiffDivEntropG'],
                            diffs['DiffEntropG']
                        )

                        # Formater le score avec 4 chiffres après la virgule
                        if score == float('inf'):
                            cell_content = "INF"
                        else:
                            cell_content = f"{score:.4f}"
                    else:
                        cell_content = "N/A"
                else:
                    cell_content = "---"

                line += cell_content.ljust(12)

            line += "|"  # Séparateur final
            table += line + "\n"

            # Ajouter un séparateur horizontal entre 0_C_TIE et 1_A_BANKER
            if index5_value == "0_C_TIE":
                table += separator_line + "\n"

        # Ajouter la ligne OBSERVÉ
        table += separator_line + "\n"
        observed_line = "OBSERVÉ        |"

        for position in range(actual_start - 1, actual_end):
            if position < len(sequence):
                observed_value = sequence[position]
                cell_content = observed_value[:10]  # Tronquer si trop long
            else:
                cell_content = "---"

            observed_line += cell_content.ljust(12) + "|"

        table += observed_line + "\n"
        table += separator_line + "\n"

        return table