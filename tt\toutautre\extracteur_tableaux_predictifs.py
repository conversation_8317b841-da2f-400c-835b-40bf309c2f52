#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXTRACTEUR DE TABLEAUX PRÉDICTIFS - DIFFÉRENTIELS ET SCORES
===========================================================

Programme pour extraire les valeurs des tableaux prédictifs :
🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS POUR LES 9 VALEURS INDEX5 POSSIBLES
🎯 TABLEAU PRÉDICTIF - SCORES POUR LES 9 VALEURS INDEX5 POSSIBLES

Pour chaque valeur observée, extrait :
- Différentiels : DiffC|DiffT|DivEG|EntG
- Scores : SCORE

Structure des tableaux :
- 2 parties chacun (Mains 6-30 et Mains 31-60)
- Ligne "OBSERVÉ" contient les valeurs INDEX5 réellement observées
- 9 lignes de valeurs INDEX5 possibles avec délimiteur entre 0_C_TIE et 1_A_BANKER
"""

import re
import json
from typing import Dict, List, Tuple, Optional


class ExtracteurTableauxPredictifs:
    """
    Extracteur spécialisé pour analyser les tableaux prédictifs
    et extraire les valeurs correspondant aux résultats observés
    """
    
    def __init__(self):
        """Initialisation de l'extracteur"""
        self.all_index5_values = [
            "0_A_BANKER", "0_B_BANKER", "0_C_BANKER",
            "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER", 
            "0_A_TIE", "0_B_TIE", "0_C_TIE",
            "1_A_BANKER", "1_B_BANKER", "1_C_BANKER",
            "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER",
            "1_A_TIE", "1_B_TIE", "1_C_TIE"
        ]
        
        # Résultats d'extraction
        self.differentiels_observes = {}  # {main: {DiffC, DiffT, DivEG, EntG}}
        self.scores_observes = {}         # {main: SCORE}
        self.valeurs_observees = {}       # {main: INDEX5_value}
    
    def extraire_ligne_observee(self, contenu_fichier: str, section: str) -> Dict[int, str]:
        """
        Extrait la ligne OBSERVÉ d'une section donnée
        
        Args:
            contenu_fichier: Contenu complet du fichier
            section: "DIFFÉRENTIELS" ou "SCORES"
            
        Returns:
            Dict {main_number: index5_value}
        """
        valeurs_observees = {}
        
        # Patterns pour identifier les sections
        if section == "DIFFÉRENTIELS":
            pattern_debut = r"🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS"
            pattern_partie = r"📊 PARTIE (\d+) - MAINS (\d+) À (\d+)"
        else:  # SCORES
            pattern_debut = r"🎯 TABLEAU PRÉDICTIF - SCORES"
            pattern_partie = r"📊 PARTIE (\d+) - MAINS (\d+) À (\d+)"
        
        # Diviser le contenu en sections
        sections = re.split(pattern_debut, contenu_fichier)
        
        for i, section_content in enumerate(sections[1:], 1):  # Ignorer la première partie avant le premier tableau
            # Extraire les parties de cette section
            parties = re.findall(pattern_partie + r"(.*?)(?=📊 PARTIE|\Z)", section_content, re.DOTALL)
            
            for partie_num, main_debut, main_fin, partie_content in parties:
                main_debut, main_fin = int(main_debut), int(main_fin)
                
                # Chercher la ligne OBSERVÉ dans cette partie
                lignes = partie_content.split('\n')
                for ligne in lignes:
                    if ligne.strip().startswith('OBSERVÉ'):
                        # Parser la ligne OBSERVÉ
                        valeurs = self._parser_ligne_observee(ligne, main_debut, main_fin)
                        valeurs_observees.update(valeurs)
                        break
        
        return valeurs_observees
    
    def _parser_ligne_observee(self, ligne_observee: str, main_debut: int, main_fin: int) -> Dict[int, str]:
        """
        Parse une ligne OBSERVÉ pour extraire les valeurs INDEX5

        Format attendu:
        OBSERVÉ        |1_C_BANKER              |0_A_PLAYER              |...
        OBSERVÉ        |1_C_BANKER  |0_A_PLAYER  |...
        """
        valeurs = {}

        # Supprimer "OBSERVÉ" au début et nettoyer
        if '|' not in ligne_observee:
            return valeurs

        # Trouver le premier | et prendre tout ce qui suit
        ligne_clean = ligne_observee[ligne_observee.find('|')+1:]

        # Diviser par | et nettoyer chaque cellule
        cellules_brutes = ligne_clean.split('|')
        cellules = []

        for cellule in cellules_brutes:
            valeur = cellule.strip()
            if valeur and valeur not in ['---', 'N/A', '']:
                cellules.append(valeur)

        # Associer chaque cellule à sa main correspondante
        for i, valeur in enumerate(cellules):
            main_num = main_debut + i
            if main_num <= main_fin:
                valeurs[main_num] = valeur

        return valeurs
    
    def extraire_differentiels_pour_valeur(self, contenu_fichier: str, main: int, index5_value: str) -> Optional[Dict[str, float]]:
        """
        Extrait les différentiels DiffC|DiffT|DivEG|EntG pour une valeur INDEX5 donnée à une main donnée
        
        Args:
            contenu_fichier: Contenu du fichier
            main: Numéro de la main (6-60)
            index5_value: Valeur INDEX5 à chercher
            
        Returns:
            Dict avec DiffC, DiffT, DivEG, EntG ou None si non trouvé
        """
        # Déterminer dans quelle partie se trouve cette main
        if 6 <= main <= 30:
            partie_num = 1
            main_debut, main_fin = 6, 30
        elif 31 <= main <= 60:
            partie_num = 2
            main_debut, main_fin = 31, 60
        else:
            return None
        
        # Chercher la section des différentiels
        pattern_section = r"🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS.*?(?=🎯|$)"
        match_section = re.search(pattern_section, contenu_fichier, re.DOTALL)
        
        if not match_section:
            return None
        
        section_content = match_section.group(0)
        
        # Chercher la partie correspondante
        pattern_partie = f"📊 PARTIE {partie_num} - MAINS {main_debut} À {main_fin}(.*?)(?=📊 PARTIE|🎯|$)"
        match_partie = re.search(pattern_partie, section_content, re.DOTALL)
        
        if not match_partie:
            return None
        
        partie_content = match_partie.group(1)
        
        # LOGIQUE CORRECTE : Chercher la ligne de référence correspondant à index5_value
        # Cette ligne contient les valeurs pour toutes les mains
        lignes = partie_content.split('\n')
        for ligne in lignes:
            ligne_clean = ligne.strip()
            # Vérifier si cette ligne commence par notre index5_value de référence
            if ligne_clean.startswith(index5_value + ' ') or ligne_clean.startswith(index5_value + '\t') or ligne_clean.startswith(index5_value + '|'):
                # Parser cette ligne pour extraire les valeurs à la colonne de la main demandée
                return self._parser_ligne_differentiels(ligne_clean, main, main_debut, main_fin)

        return None
    
    def _parser_ligne_differentiels(self, ligne: str, main_cible: int, main_debut: int, main_fin: int) -> Optional[Dict[str, float]]:
        """
        Parse une ligne de différentiels pour extraire les valeurs d'une main spécifique

        Format attendu:
        0_A_BANKER     |N/A  |N/A  |N/A  |N/A   |0.017|0.028|0.022|0.022 |...
        Utilise les délimiteurs | pour parser correctement
        """
        # Supprimer le nom INDEX5 au début
        if '|' not in ligne:
            return None

        ligne_clean = ligne[ligne.find('|')+1:]

        # Calculer l'index de la cellule correspondant à main_cible
        index_cellule = main_cible - main_debut

        # Parsing manuel précis en divisant par | et regroupant par 4
        # Structure: |val1|val2|val3|val4 |val5|val6|val7|val8 |...

        # Diviser par | et nettoyer
        parties = [p.strip() for p in ligne_clean.split('|') if p.strip()]

        # Regrouper par 4 pour former les cellules
        cellules = []
        for i in range(0, len(parties), 4):
            if i + 3 < len(parties):
                cellules.append((parties[i], parties[i+1], parties[i+2], parties[i+3]))

        if index_cellule >= len(cellules):
            return None

        # Extraire les 4 valeurs de la cellule correspondante
        valeurs = cellules[index_cellule]

        try:
            return {
                'DiffC': float(valeurs[0]) if valeurs[0] not in ['N/A', '---', ''] else None,
                'DiffT': float(valeurs[1]) if valeurs[1] not in ['N/A', '---', ''] else None,
                'DivEG': float(valeurs[2]) if valeurs[2] not in ['N/A', '---', ''] else None,
                'EntG': float(valeurs[3]) if valeurs[3] not in ['N/A', '---', ''] else None
            }
        except (ValueError, IndexError):
            return None
    
    def extraire_score_pour_valeur(self, contenu_fichier: str, main: int, index5_value: str) -> Optional[float]:
        """
        Extrait le SCORE pour une valeur INDEX5 donnée à une main donnée
        
        Args:
            contenu_fichier: Contenu du fichier
            main: Numéro de la main (6-60)
            index5_value: Valeur INDEX5 à chercher
            
        Returns:
            Score (float) ou None si non trouvé
        """
        # Déterminer dans quelle partie se trouve cette main
        if 6 <= main <= 30:
            partie_num = 1
            main_debut, main_fin = 6, 30
        elif 31 <= main <= 60:
            partie_num = 2
            main_debut, main_fin = 31, 60
        else:
            return None
        
        # Chercher la section des scores
        pattern_section = r"🎯 TABLEAU PRÉDICTIF - SCORES.*?(?=📈|$)"
        match_section = re.search(pattern_section, contenu_fichier, re.DOTALL)
        
        if not match_section:
            return None
        
        section_content = match_section.group(0)
        
        # Chercher la partie correspondante
        pattern_partie = f"📊 PARTIE {partie_num} - MAINS \\d+ À \\d+(.*?)(?=📊 PARTIE|📈|$)"
        match_partie = re.search(pattern_partie, section_content, re.DOTALL)
        
        if not match_partie:
            return None
        
        partie_content = match_partie.group(1)
        
        # LOGIQUE CORRECTE : Chercher la ligne de référence correspondant à index5_value
        # Cette ligne contient les scores pour toutes les mains
        lignes = partie_content.split('\n')
        for ligne in lignes:
            ligne_clean = ligne.strip()
            # Vérifier si cette ligne commence par notre index5_value de référence
            if ligne_clean.startswith(index5_value + ' ') or ligne_clean.startswith(index5_value + '\t') or ligne_clean.startswith(index5_value + '|'):
                # Parser cette ligne pour extraire le score à la colonne de la main demandée
                return self._parser_ligne_score(ligne_clean, main, main_debut, main_fin)

        return None
    
    def _parser_ligne_score(self, ligne: str, main_cible: int, main_debut: int, main_fin: int) -> Optional[float]:
        """
        Parse une ligne de scores pour extraire le score d'une main spécifique

        Format attendu:
        0_A_BANKER     |N/A         |0.1533      |0.1207      |...
        Utilise les délimiteurs | pour parser correctement
        """
        # Supprimer le nom INDEX5 au début
        if '|' not in ligne:
            return None

        ligne_clean = ligne[ligne.find('|')+1:]

        # Calculer l'index de la cellule correspondant à main_cible
        index_cellule = main_cible - main_debut

        # Parsing manuel précis en divisant par | et nettoyant
        # Structure: |val1         |val2         |val3         |...

        # Diviser par | et nettoyer
        parties = [p.strip() for p in ligne_clean.split('|') if p.strip()]

        if index_cellule >= len(parties):
            return None

        # Extraire la valeur de la cellule correspondante
        score_str = parties[index_cellule]

        # Nettoyer et convertir la valeur
        try:
            if score_str in ['N/A', '---', 'INF', '']:
                return None
            return float(score_str)
        except ValueError:
            return None

    def analyser_fichier_complet(self, chemin_fichier: str) -> Dict:
        """
        Analyse complète d'un fichier de rapport pour extraire toutes les valeurs observées

        Args:
            chemin_fichier: Chemin vers le fichier de rapport

        Returns:
            Dict avec toutes les extractions organisées
        """
        try:
            with open(chemin_fichier, 'r', encoding='utf-8') as f:
                contenu = f.read()
        except FileNotFoundError:
            print(f"❌ Fichier non trouvé : {chemin_fichier}")
            return {}

        # Extraire les valeurs observées des deux tableaux
        valeurs_observees_diff = self.extraire_ligne_observee(contenu, "DIFFÉRENTIELS")
        valeurs_observees_scores = self.extraire_ligne_observee(contenu, "SCORES")

        # Vérifier la cohérence
        if valeurs_observees_diff != valeurs_observees_scores:
            print("⚠️  Incohérence détectée entre les valeurs observées des deux tableaux")

        # Utiliser les valeurs des différentiels comme référence
        self.valeurs_observees = valeurs_observees_diff

        # Extraire les différentiels et scores pour chaque valeur observée
        resultats = {
            'valeurs_observees': self.valeurs_observees,
            'differentiels': {},
            'scores': {},
            'tableau_unifie': {},  # Nouveau : tableau unifié
            'resume': {
                'total_mains': len(self.valeurs_observees),
                'mains_avec_differentiels': 0,
                'mains_avec_scores': 0,
                'mains_completes': 0
            }
        }

        for main, index5_value in self.valeurs_observees.items():
            # Extraire les différentiels
            differentiels = self.extraire_differentiels_pour_valeur(contenu, main, index5_value)
            if differentiels:
                resultats['differentiels'][main] = {
                    'index5_value': index5_value,
                    'differentiels': differentiels
                }
                resultats['resume']['mains_avec_differentiels'] += 1

            # Extraire le score
            score = self.extraire_score_pour_valeur(contenu, main, index5_value)
            if score is not None:
                resultats['scores'][main] = {
                    'index5_value': index5_value,
                    'score': score
                }
                resultats['resume']['mains_avec_scores'] += 1

            # Compter les mains complètes (avec différentiels ET score)
            if differentiels and score is not None:
                resultats['resume']['mains_completes'] += 1

            # Construire l'entrée du tableau unifié
            entree_unifiee = {
                'index5_value': index5_value,
                'differentiels': differentiels,
                'score': score
            }
            resultats['tableau_unifie'][main] = entree_unifiee

        return resultats

    def generer_rapport_extraction(self, resultats: Dict, fichier_sortie: str = None):
        """
        Génère un rapport détaillé des extractions

        Args:
            resultats: Résultats de l'analyse complète
            fichier_sortie: Fichier de sortie (optionnel)
        """
        rapport = []
        rapport.append("📊 RAPPORT D'EXTRACTION DES TABLEAUX PRÉDICTIFS")
        rapport.append("=" * 60)
        rapport.append("")

        # Résumé
        resume = resultats.get('resume', {})
        rapport.append("📈 RÉSUMÉ DE L'EXTRACTION")
        rapport.append("-" * 30)
        rapport.append(f"Total de mains analysées : {resume.get('total_mains', 0)}")
        rapport.append(f"Mains avec différentiels : {resume.get('mains_avec_differentiels', 0)}")
        rapport.append(f"Mains avec scores : {resume.get('mains_avec_scores', 0)}")
        rapport.append(f"Mains complètes : {resume.get('mains_completes', 0)}")
        rapport.append("")

        # Tableau unifié des extractions
        rapport.append("📊 TABLEAU UNIFIÉ DES EXTRACTIONS")
        rapport.append("=" * 80)
        rapport.append(f"{'Main':>4} | {'INDEX5':^12} | {'DiffC':>6} | {'DiffT':>6} | {'DivEG':>6} | {'EntG':>6} | {'SCORE':>8}")
        rapport.append("-" * 80)

        # Récupérer toutes les mains qui ont au moins une donnée
        toutes_mains = set()
        toutes_mains.update(resultats.get('differentiels', {}).keys())
        toutes_mains.update(resultats.get('scores', {}).keys())

        for main in sorted(toutes_mains):
            main_int = int(main)

            # Récupérer les données de différentiels
            diff_data = resultats.get('differentiels', {}).get(main, {})
            if diff_data:
                index5_value = diff_data['index5_value']
                diff = diff_data['differentiels']
                diff_c = f"{diff['DiffC']:6.3f}" if diff['DiffC'] is not None else "  N/A "
                diff_t = f"{diff['DiffT']:6.3f}" if diff['DiffT'] is not None else "  N/A "
                div_eg = f"{diff['DivEG']:6.3f}" if diff['DivEG'] is not None else "  N/A "
                ent_g = f"{diff['EntG']:6.3f}" if diff['EntG'] is not None else "  N/A "
            else:
                # Récupérer l'INDEX5 depuis les scores si pas de différentiels
                score_data = resultats.get('scores', {}).get(main, {})
                index5_value = score_data.get('index5_value', 'N/A') if score_data else 'N/A'
                diff_c = diff_t = div_eg = ent_g = "  N/A "

            # Récupérer le score
            score_data = resultats.get('scores', {}).get(main, {})
            if score_data and score_data.get('score') is not None:
                score = f"{score_data['score']:8.4f}"
            else:
                score = "    N/A "

            rapport.append(f"{main_int:4d} | {index5_value:^12} | {diff_c} | {diff_t} | {div_eg} | {ent_g} | {score}")

        rapport.append("=" * 80)

        # Afficher le rapport
        rapport_text = '\n'.join(rapport)
        print(rapport_text)

        # Sauvegarder si demandé
        if fichier_sortie:
            try:
                with open(fichier_sortie, 'w', encoding='utf-8') as f:
                    f.write(rapport_text)
                print(f"\n✅ Rapport sauvegardé dans : {fichier_sortie}")
            except Exception as e:
                print(f"❌ Erreur lors de la sauvegarde : {e}")


def afficher_aide():
    """Affiche l'aide d'utilisation du programme"""
    print("🔮 EXTRACTEUR DE TABLEAUX PRÉDICTIFS")
    print("=" * 50)
    print("📖 UTILISATION :")
    print("  python extracteur_tableaux_predictifs.py [fichier_rapport.txt]")
    print()
    print("📁 EXEMPLES :")
    print("  python extracteur_tableaux_predictifs.py rapport1.txt")
    print("  python extracteur_tableaux_predictifs.py rapport25.txt")
    print("  python extracteur_tableaux_predictifs.py")  # Détection automatique
    print()
    print("📊 FONCTIONNEMENT :")
    print("  • Sans argument : détecte automatiquement le fichier rapport*.txt le plus récent")
    print("  • Avec argument : utilise le fichier spécifié")
    print("  • Génère rapport_extraction[N].txt et resultats_extraction[N].json")
    print()
    print("🎯 FORMATS SUPPORTÉS :")
    print("  • rapport1.txt, rapport2.txt, rapport25.txt, etc.")
    print("  • Tout fichier suivant le format rapport[numéro].txt")


def main():
    """
    Fonction principale - accepte n'importe quel fichier rapport*.txt
    """
    print("🔮 EXTRACTEUR DE TABLEAUX PRÉDICTIFS")
    print("=" * 50)

    # Gestion des arguments de ligne de commande
    import sys
    import glob
    import os

    if len(sys.argv) > 1:
        # Vérifier si c'est une demande d'aide
        if sys.argv[1] in ['-h', '--help', 'help', '?']:
            afficher_aide()
            return

        # Fichier spécifié en argument
        fichier_rapport = sys.argv[1]
        print(f"📁 Fichier spécifié : {fichier_rapport}")
    else:
        # Recherche automatique du fichier rapport le plus récent
        fichiers_rapport = glob.glob("rapport*.txt")

        if not fichiers_rapport:
            print("❌ Erreur : Aucun fichier rapport*.txt trouvé dans le répertoire courant.")
            print("💡 Usage : python extracteur_tableaux_predictifs.py [fichier_rapport.txt]")
            print("💡 Ou placez un fichier rapport*.txt dans le répertoire courant.")
            return

        # Trier par nom pour prendre le plus récent (rapport1.txt, rapport2.txt, etc.)
        fichiers_rapport.sort()
        fichier_rapport = fichiers_rapport[-1]  # Le dernier dans l'ordre alphabétique

        print(f"📁 Fichier détecté automatiquement : {fichier_rapport}")

    if not os.path.exists(fichier_rapport):
        print(f"❌ Erreur : Le fichier {fichier_rapport} n'existe pas.")
        return

    # Créer l'extracteur
    extracteur = ExtracteurTableauxPredictifs()

    # Analyser le fichier
    print(f"📊 Analyse du fichier {fichier_rapport}...")
    resultats = extracteur.analyser_fichier_complet(fichier_rapport)

    if resultats:
        # Générer les noms de fichiers de sortie basés sur le fichier d'entrée
        numero_partie = fichier_rapport.replace("rapport", "").replace(".txt", "")
        if numero_partie:
            nom_rapport = f"rapport_extraction{numero_partie}.txt"
            nom_json = f"resultats_extraction{numero_partie}.json"
        else:
            nom_rapport = "rapport_extraction.txt"
            nom_json = "resultats_extraction.json"

        # Générer le rapport
        extracteur.generer_rapport_extraction(resultats, nom_rapport)

        # Sauvegarder les résultats en JSON
        try:
            with open(nom_json, 'w', encoding='utf-8') as f:
                json.dump(resultats, f, indent=2, ensure_ascii=False)
            print(f"✅ Résultats sauvegardés en JSON : {nom_json}")
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde JSON : {e}")
    else:
        print("❌ Aucun résultat extrait")


if __name__ == "__main__":
    main()
