# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 2598 à 2626
# Type: Méthode de la classe INDEX5PredictiveScoreTable

    def verify_score_consistency(self, sequence, evolution, analyzer, position, index5_value):
        """
        Méthode de vérification pour s'assurer que les SCORES correspondent aux différentiels
        """
        differential_table = INDEX5PredictiveDifferentialTable()
        predictive_diffs = differential_table.calculate_predictive_differentials(
            sequence, evolution, position, analyzer
        )

        if index5_value in predictive_diffs:
            diffs = predictive_diffs[index5_value]
            calculated_score = self.score_calculator.calculate_predictive_score(
                diffs['DiffCond'],
                diffs['DiffTaux'],
                diffs['DiffDivEntropG'],
                diffs['DiffEntropG']
            )

            # Calcul manuel pour vérification
            manual_score = (diffs['DiffCond'] + diffs['DiffEntropG']) / (diffs['DiffTaux'] + diffs['DiffDivEntropG'])

            return {
                'diffs': diffs,
                'calculated_score': calculated_score,
                'manual_score': manual_score,
                'match': abs(calculated_score - manual_score) < 0.001
            }

        return None