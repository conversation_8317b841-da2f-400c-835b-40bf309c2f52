# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\8\entropie_baccarat_analyzer.py
# Lignes: 1448 à 1487
# Type: Méthode de la classe INDEX5Calculator

    def calculate_multi_algorithm_consensus_score(self, sequence_history, all_metrics):
        """
        Calcule un score de consensus entre différents algorithmes d'analyse.
        Plus le score est élevé, plus les différentes méthodes sont en accord
        sur les caractéristiques de la séquence.
        """
        if not sequence_history or len(sequence_history) < 5:
            return 0.0

        # 1. Calculer différents scores avec les méthodes disponibles
        scores = {}

        # Score de prédictibilité contextuelle
        context_score = self.calculate_context_predictability(sequence_history, all_metrics)
        scores['context'] = context_score

        # Score de force des patterns
        pattern_score = self.calculate_pattern_strength(sequence_history)
        scores['pattern'] = pattern_score

        # Score de compression
        compression_score = self.calculate_compression_score(sequence_history, all_metrics)
        scores['compression'] = compression_score

        # Score de divergence bayésienne (inverser pour cohérence)
        divergence_score = self.calculate_bayesian_divergence_score(sequence_history)
        scores['bayesian'] = 1.0 - divergence_score

        # 2. Calculer la variance des scores (faible variance = consensus élevé)
        score_values = list(scores.values())
        if len(score_values) < 2:
            return 0.0

        mean_score = sum(score_values) / len(score_values)
        variance = sum((score - mean_score) ** 2 for score in score_values) / len(score_values)

        # 3. Convertir en score de consensus (faible variance = consensus élevé)
        consensus_score = 1.0 / (1.0 + variance * 10)  # Normalisation

        return round(consensus_score, 4)