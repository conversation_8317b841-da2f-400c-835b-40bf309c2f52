println("=== DIAGNOSTIC COMPLET PYCALL ===")

println("\n1. Test chargement PyCall...")
try
    using PyCall
    println("OK: PyCall charge avec succes")
catch e
    println("ERREUR PyCall: ", e)
    exit(1)
end

println("\n2. Configuration PyCall...")
println("Python executable: ", PyCall.python)
println("Python version: ", PyCall.pyversion)
println("Libpython: ", PyCall.libpython)

println("\n3. Test fonctions Python basiques...")
try
    py"""
    def test_basic():
        return "Hello from Python"
    """
    result = py"test_basic()"
    println("OK: Fonction Python basique: ", result)
catch e
    println("ERREUR fonction Python: ", e)
end

println("\n4. Test variables d'environnement...")
println("JULIA_PYTHONCALL_EXE: ", get(<PERSON>NV, "JULIA_PYTHONCALL_EXE", "Non defini"))
println("PYTHON: ", get(ENV, "PYTHON", "Non defini"))

println("\n5. Test import sys Python...")
try
    sys = pyimport("sys")
    println("OK: Python sys.executable: ", sys.executable)
    println("OK: Python sys.version: ", sys.version)
catch e
    println("ERREUR import sys: ", e)
end

println("\n6. Test compatibilite versions...")
try
    # Verifier que les versions correspondent
    julia_python = PyCall.python
    env_python = get(ENV, "PYTHON", "")
    
    println("Julia utilise Python: ", julia_python)
    println("ENV PYTHON: ", env_python)
    
    if julia_python != env_python && env_python != ""
        println("ATTENTION: Versions Python differentes!")
    else
        println("OK: Versions Python coherentes")
    end
catch e
    println("ERREUR verification versions: ", e)
end

println("\n=== FIN DIAGNOSTIC ===")
